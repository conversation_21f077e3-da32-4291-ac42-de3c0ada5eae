# DeepCode AST 分析器插件

一个用于 IntelliJ IDEA 的多语言 AST（抽象语法树）分析插件，支持 Java 和 Python 代码结构分析和方法调用关系提取。

## 🚀 功能特性

### 核心功能
- 🔍 **完整的 AST 分析**：支持类、方法、字段、函数、变量的结构分析
- 📞 **调用关系分析**：提取方法间的调用关系，支持内部调用和外部调用
- 🌐 **多语言支持**：同时支持 Java 和 Python 代码分析
- 🔒 **线程安全**：所有 PSI 访问都在 ReadAction 中进行
- 🎯 **按调用方分组**：调用关系按调用方分组显示，包含完整的方法 ID 信息
- 🛡️ **错误处理**：优雅的异常处理机制
- 🔧 **调试支持**：可配置的调试输出
- 🖥️ **丰富的UI**：工具窗口支持，hover显示详细信息

### Java 分析特性
- ✅ 类、接口、枚举分析
- ✅ 方法签名和实现解析
- ✅ 字段声明分析
- ✅ Maven 模块自动识别
- ✅ 精确的行号计算
- ✅ 智能的内外部调用区分

### Python 分析特性
- ✅ 类和函数定义分析
- ✅ 模块级变量识别
- ✅ 递归调用检测
- ✅ 内置方法调用支持（str、list、dict 等）
- ✅ Super() 调用处理
- ✅ Python 包结构识别

## 🏗️ 项目架构

本项目采用模块化架构设计，解决了不同 IDE 版本的兼容性问题：

```
deepcode-ast-plugin/
├── shared-core/                    # 共享核心模块
│   ├── 数据模型 (AnalysisNode, CallRelation)
│   ├── 用户界面组件
│   └── 分析器接口定义
├── shared-ui/                      # 共享UI组件
├── deepcode-java-plugin/           # Java专用插件
│   └── 适用于 IntelliJ IDEA Community
└── deepcode-python-plugin/         # Python专用插件
    └── 适用于 PyCharm Community
```

### 支持的 IDE
- **Java插件**: IntelliJ IDEA (Community & Ultimate)
- **Python插件**: PyCharm (Community & Professional)

## 📦 快速开始

### 环境要求
- Java 17 或更高版本
- IntelliJ IDEA 2024.1.4+ 或 PyCharm 2024.1.4+
- Gradle（已包含 wrapper）

### 构建插件
```bash
# 构建所有模块
./gradlew build

# 构建Java插件
./gradlew :deepcode-java-plugin:build

# 构建Python插件
./gradlew :deepcode-python-plugin:build

# 运行Java插件开发环境
./gradlew :deepcode-java-plugin:runIde

# 运行Python插件开发环境
./gradlew :deepcode-python-plugin:runIde
```

### 安装插件
1. 构建完成后，在 `build/distributions/` 目录下找到生成的插件包
2. 打开对应的 IDE（IDEA 或 PyCharm）
3. 进入 `File` → `Settings` → `Plugins` → 齿轮图标 → `Install Plugin from Disk...`
4. 选择对应的插件包并重启 IDE

### 基本使用
1. 打开 Java 或 Python 文件
2. 右键选择 "Quick AST Analyzer" 或使用快捷键 `Ctrl+Alt+Shift+T`
3. 查看分析结果并可以导出为 JSON 格式

## 📚 文档

- [使用手册](USAGE_GUIDE.md) - 详细的使用说明和功能介绍
- [开发文档](DEVELOPMENT_GUIDE.md) - 开发环境搭建和架构说明
- [shared-core/](shared-core/README.md) - 共享核心模块说明
- [shared-ui/](shared-ui/README.md) - 共享UI组件说明
- [deepcode-java-plugin/](deepcode-java-plugin/README.md) - Java插件说明
- [deepcode-python-plugin/](deepcode-python-plugin/README.md) - Python插件说明

## 🛠️ 技术栈

- **平台**: IntelliJ Platform 2024.1.4
- **语言**: Java 17+
- **构建工具**: Gradle
- **分析引擎**: PSI (Program Structure Interface)

## ⌨️ 快捷键

- `Ctrl+Alt+Shift+T` - 快速 AST 分析器
- `Ctrl+Alt+T` - 显示 AST 分析工具窗口
- `Ctrl+Alt+A` - 生成 AST 报告
- `Ctrl+Alt+E` - 导出 AST 报告

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来帮助改进项目。

## 📄 许可证

本项目由三快在线（美团）工程团队开发和维护。
