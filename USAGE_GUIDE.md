# 使用说明文档

## 📖 概述

AST 分析插件是一个强大的多语言代码分析工具，支持 Java 和 Python 代码的结构分析和调用关系提取。本文档将详细介绍如何安装、配置和使用该插件。

## 🚀 快速开始

### 安装插件

#### 方式一：从构建文件安装
1. 构建插件：`./gradlew buildPlugin`
2. 打开 IntelliJ IDEA/PyCharm
3. 进入 `File` → `Settings` → `Plugins`
4. 点击齿轮图标 → `Install Plugin from Disk...`
5. 选择 `build/distributions/AST Analysis Plugin-1.0-SNAPSHOT.zip`
6. 重启 IDE

#### 方式二：开发模式安装
```bash
# 启动沙盒 IDE（推荐开发测试）
./gradlew runIde
```

### 验证安装
安装成功后，您应该能在以下位置找到插件功能：
- 右键菜单中的 "Quick AST Analyzer"
- `Tools` 菜单中的 AST 分析选项
- 快捷键 `Ctrl+Alt+Shift+T` 可用

## 🎯 基本使用

### 分析 Java 文件

1. **打开 Java 文件**
   - 在 IDE 中打开任意 `.java` 文件
   - 确保文件属于一个有效的 Java 项目

2. **启动分析**
   - 方式1：右键点击文件，选择 "Quick AST Analyzer"
   - 方式2：使用快捷键 `Ctrl+Alt+Shift+T`
   - 方式3：通过 `Tools` → `AST Analysis` 菜单

3. **查看结果**
   - 弹窗显示分析结果摘要
   - 工具窗口显示详细的节点和调用关系信息

### 分析 Python 文件

1. **确保 Python 插件可用**
   - 如果安装了 Python 插件，将获得完整的 PSI 分析
   - 如果没有 Python 插件，将使用文本模式分析

2. **分析步骤**
   - 打开 `.py` 文件
   - 使用相同的快捷键或菜单选项
   - 查看 Python 特有的分析结果

## ⌨️ 快捷键和菜单

### 快捷键列表
- `Ctrl+Alt+Shift+T` - 快速 AST 分析器
- `Ctrl+Alt+T` - 显示 AST 分析工具窗口
- `Ctrl+Alt+A` - 生成 AST 报告
- `Ctrl+Alt+E` - 导出 AST 报告

### 菜单位置
- **右键菜单**：`Quick AST Analyzer`
- **工具菜单**：`Tools` → `AST Analysis`
- **工具窗口**：底部面板的 "AST Analysis" 标签

## 📊 分析结果解读

### 节点信息

每个分析节点包含以下信息：

```
节点ID: com.example.UserService.findUser
类型: METHOD
名称: findUser
包名: com.example
模块名: user-service-module
文件路径: /src/main/java/com/example/UserService.java
行号: 25
签名: findUser(Long id): User
语言: Java
```

### 调用关系

调用关系显示方法/函数之间的调用情况：

```
调用方: com.example.UserService.findUser
被调用方: com.example.UserRepository.findById
调用类型: 内部调用
调用位置: 第 28 行 - userRepository.findById(id)
```

### 内外部调用判定

#### Java 调用判定逻辑
- **内部调用**：被调用方法的类在当前项目的源码目录中
- **外部调用**：
  - Java 标准库（`java.*`, `javax.*`, `sun.*` 等）
  - 第三方库（`org.apache.*`, `org.springframework.*` 等）
  - 依赖库中的类（非源码目录）
  - 无法解析的方法

#### Python 调用判定逻辑
- **内部调用**：同一模块内的函数调用
- **外部调用**：
  - 内置方法（`str.upper()`, `list.append()` 等）
  - 标准库函数
  - 第三方库函数
- **递归调用**：函数调用自身

## 🔧 高级功能

### 调试模式

启用详细调试输出以排查问题：

```bash
# 方式1：JVM 参数
./gradlew runIde -Dast.analyzer.debug=true

# 方式2：环境变量
export AST_ANALYZER_DEBUG=true

# 方式3：在代码中设置
System.setProperty("ast.analyzer.debug", "true");
```

调试模式下的输出示例：
```
[DEBUG] Analyzing Java file: /path/to/UserService.java
[DEBUG] Found class: com.example.UserService
[DEBUG] Found method: findUserById(Long id)
[DEBUG] Found call: validateUser -> isValidUser (internal)
[DEBUG] Found call: logger.info -> Logger.info (external)
[DEBUG] Maven module detected: user-service-module
[DEBUG] Analysis completed: 15 nodes, 8 call relations
```

### 批量分析

对于大型项目，可以批量分析多个文件：

1. 选择项目或目录
2. 使用 `Tools` → `AST Analysis` → `Batch Analysis`
3. 选择要分析的文件类型（Java、Python 或全部）
4. 查看汇总报告

### 导出功能

分析结果可以导出为多种格式：

- **JSON 格式**：完整的结构化数据
- **CSV 格式**：适合在 Excel 中查看
- **HTML 报告**：可视化的分析报告

导出步骤：
1. 完成分析后，使用 `Ctrl+Alt+E` 或菜单选项
2. 选择导出格式和保存位置
3. 确认导出设置

## 🎨 界面说明

### 工具窗口布局

AST 分析工具窗口包含以下面板：

1. **节点树视图**
   - 按类型分组显示所有节点
   - 支持搜索和过滤
   - 双击节点跳转到源码

2. **调用关系视图**
   - 显示方法/函数调用关系
   - 区分内部和外部调用
   - 支持调用链追踪

3. **统计信息面板**
   - 显示分析统计数据
   - 包括节点数量、调用关系数量等
   - 性能指标和耗时信息

4. **详情面板**
   - 显示选中节点的详细信息
   - 包括签名、注解、文档等
   - 支持复制和导出

### 交互功能

- **悬停提示**：鼠标悬停显示详细信息
- **右键菜单**：提供复制、导出、跳转等操作
- **搜索过滤**：支持按名称、类型、包名等过滤
- **排序功能**：支持按不同字段排序

## 🔍 使用场景

### 代码理解
- 快速了解项目结构
- 分析类和方法的关系
- 识别核心组件和依赖

### 重构支持
- 找出方法的所有调用者
- 分析影响范围
- 识别可以安全删除的代码

### 架构分析
- 分析模块间的依赖关系
- 识别循环依赖
- 评估代码复杂度

### 代码审查
- 检查调用关系的合理性
- 识别潜在的设计问题
- 验证架构约束

## ⚠️ 注意事项

### Java 分析注意事项

1. **Maven 模块识别**
   - 确保项目有正确的 `pom.xml` 文件
   - 多模块项目会自动识别模块信息

2. **类路径问题**
   - 确保项目编译成功
   - 检查依赖是否正确配置

3. **泛型处理**
   - 泛型信息可能不完整
   - 复杂的泛型可能影响分析精度

### Python 分析注意事项

1. **插件依赖**
   - 完整功能需要 Python 插件
   - 没有插件时使用文本模式分析

2. **动态特性**
   - Python 的动态特性可能影响分析精度
   - 运行时生成的代码无法分析

3. **导入处理**
   - 复杂的导入语句可能无法完全解析
   - 相对导入可能影响模块识别

## 🐛 故障排除

### 常见问题

1. **插件未加载**
   - 检查 IDE 版本兼容性
   - 验证插件是否正确安装
   - 查看 IDE 错误日志

2. **分析结果为空**
   - 启用调试模式查看详细过程
   - 检查文件是否为有效的源码文件
   - 确认项目结构正确

3. **性能问题**
   - 大文件分析可能较慢
   - 考虑分批处理
   - 检查内存使用情况

4. **调用关系不准确**
   - 启用调试模式查看判定过程
   - 检查类路径配置
   - 验证依赖库是否正确

### 获取帮助

如果遇到问题，可以：

1. 启用调试模式获取详细日志
2. 查看 IDE 的错误日志
3. 检查项目配置和依赖
4. 参考开发文档进行故障排除

## 📈 性能优化

### 分析性能

- **小文件**（< 500 行）：通常 < 100ms
- **中等文件**（500-2000 行）：100-500ms
- **大文件**（> 2000 行）：500ms-2s

### 优化建议

1. **避免分析过大的文件**
2. **使用过滤器减少分析范围**
3. **定期清理缓存**
4. **在性能较好的机器上运行**

通过本使用指南，您应该能够充分利用 AST 分析插件的强大功能，提高代码理解和开发效率。
