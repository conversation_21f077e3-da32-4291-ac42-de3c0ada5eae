plugins {
    id("java")
    id("org.jetbrains.intellij.platform") version "2.6.0"
}

group = "com.sankuai.deepcode"
version = "1.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
    intellijPlatform {
        defaultRepositories()
    }
}

dependencies {
    implementation(project(":shared-core"))
    implementation(project(":shared-ui"))
    
    intellijPlatform {
        intellijIdeaCommunity("2024.1.4")
        bundledPlugins("com.intellij.java")
        
        pluginVerifier()
        zipSigner()
    }
}

intellijPlatform {
    buildSearchableOptions = false
    instrumentCode = false
    
    pluginConfiguration {
        id = "com.sankuai.deepcode.astplugin.java"
        name = "DeepCode AST Analyzer for Java"
        vendor {
            name = "Shanghai Sankuai Technology Co., Ltd."
            email = "<EMAIL>"
        }
        description = """
            AST分析器 - Java语言支持

            专门为IntelliJ IDEA设计的Java代码分析插件。
            支持Java代码的AST解析、结构分析和调用关系分析。

            功能特性：
            - Java 类、接口、枚举结构解析
            - 方法和字段分析
            - 内嵌类和匿名类支持
            - 函数调用关系分析
            - import语句分析
            - 注解和泛型支持
            - 代码统计信息
            - 可视化AST结果展示
            
            支持的文件类型：
            - .java (Java)
        """.trimIndent()
        
        ideaVersion {
            sinceBuild = "241"
            untilBuild = "252.*"
        }
    }
    
    pluginVerification {
        ides {
            recommended()
        }
    }
}

tasks {
    jar {
        dependsOn(":shared-core:jar", ":shared-ui:jar")
        duplicatesStrategy = DuplicatesStrategy.EXCLUDE
        from({
            configurations.runtimeClasspath.get()
                .filter { it.name.contains("shared") && !it.name.contains("searchableOptions") }
                .map { zipTree(it) }
        }) {
            exclude("META-INF/MANIFEST.MF")
        }
    }

    runIde {
        maxHeapSize = "2g"

        jvmArgs = listOf(
            "-Xms512m",
            "-Xmx2g",
            "-XX:ReservedCodeCacheSize=512m",
            "-XX:+UseConcMarkSweepGC",
            "-XX:SoftRefLRUPolicyMSPerMB=50",
            "-ea",
            "-XX:CICompilerCount=2",
            "-Dsun.io.useCanonPrefixCache=false",
            "-Djdk.http.auth.tunneling.disabledSchemes=\"\"",
            "-XX:+HeapDumpOnOutOfMemoryError",
            "-XX:-OmitStackTraceInFastThrow",
            "-Dide.show.tips.on.startup.default.value=false",
            "-Didea.ProcessCanceledException=disabled",
            // 启用详细日志输出
            "-Didea.log.debug.categories=#com.sankuai.deepcode.astplugin",
            "-Dide.log.level=DEBUG",
            "-Didea.log.level=DEBUG"
        )
    }
    
    compileJava {
        options.compilerArgs.addAll(listOf("-Xlint:deprecation", "-Xlint:unchecked"))
    }
}