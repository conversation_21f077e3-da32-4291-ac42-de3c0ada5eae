# Java 插件开发指南

本文档介绍 DeepCode Java Plugin 的开发环境搭建、架构设计和扩展方法。

## 🏗️ 架构设计

### 核心组件

```
deepcode-java-plugin/src/main/java/
└── com/sankuai/deepcode/astplugin/java/
    ├── JavaASTAnalyzer.java          # 主分析器
    ├── JavaNodeVisitor.java          # PSI 访问器
    ├── JavaCallAnalyzer.java         # 调用关系分析
    ├── JavaImportAnalyzer.java       # 导入分析
    └── util/
        ├── JavaPsiUtils.java         # PSI 工具方法  
        ├── JavaSignatureUtils.java   # 签名工具
        └── MavenModuleDetector.java  # Maven 模块检测
```

### 分析流程

```
PsiFile (Java) 
    ↓
JavaASTAnalyzer
    ├── JavaNodeVisitor (遍历 PSI 节点)
    ├── JavaCallAnalyzer (分析调用关系)
    ├── JavaImportAnalyzer (分析导入)
    └── MavenModuleDetector (检测模块)
    ↓
AnalysisResult (统一结果格式)
```

## 🛠️ 开发环境

### 环境要求

- Java 17+ (开发语言)
- IntelliJ IDEA 2024.1.4+ (开发环境)
- Gradle 7.6+ (构建工具)
- Java Plugin (IntelliJ Java 支持)

### 项目配置

**build.gradle.kts**:
```kotlin
plugins {
    id("java")
    id("org.jetbrains.intellij") version "1.17.4"
}

dependencies {
    implementation(project(":shared-core"))
    implementation(project(":shared-ui"))
}

intellij {
    version.set("2024.1.4")
    type.set("IC")
    plugins.set(listOf("com.intellij.java"))
}
```

### 开发启动

```bash
# 克隆项目
git clone <repository>
cd deepcode-ast-plugin

# 构建项目  
./gradlew :deepcode-java-plugin:build

# 启动开发环境
./gradlew :deepcode-java-plugin:runIde

# 调试模式
./gradlew :deepcode-java-plugin:runIde --debug-jvm
```

## 🔍 核心实现

### JavaASTAnalyzer

主分析器负责协调整个分析过程：

```java
public class JavaASTAnalyzer implements ASTAnalyzer {
    
    @Override
    public boolean supports(PsiFile psiFile) {
        return psiFile instanceof PsiJavaFile;
    }
    
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        return ReadAction.compute(() -> {
            AnalysisResult result = new AnalysisResult();
            result.setFilePath(psiFile.getVirtualFile().getPath());
            result.setLanguage(Language.JAVA);
            
            // 检测 Maven 模块
            String moduleName = MavenModuleDetector.detectModule(psiFile);
            
            // 分析文件结构
            JavaNodeVisitor visitor = new JavaNodeVisitor(result, moduleName);
            psiFile.accept(visitor);
            
            // 分析调用关系
            JavaCallAnalyzer callAnalyzer = new JavaCallAnalyzer(result);
            callAnalyzer.analyzeCallRelations(psiFile);
            
            return result;
        });
    }
}
```

### PSI 访问优化

确保所有PSI操作都在ReadAction中进行：

```java
// 正确的PSI访问方式
public void analyzeMethods(PsiClass psiClass) {
    ReadAction.run(() -> {
        PsiMethod[] methods = psiClass.getMethods();
        for (PsiMethod method : methods) {
            processMethod(method);
        }
    });
}

// 错误的方式 - 多次进入ReadAction
public void analyzeMethodsWrong(PsiClass psiClass) {
    for (PsiMethod method : psiClass.getMethods()) {
        ReadAction.run(() -> processMethod(method));
    }
}
```

## 🧪 测试开发

### 单元测试示例

```java
class JavaASTAnalyzerTest {
    
    @Test
    void testClassAnalysis() {
        String javaCode = """
            package com.example;
            
            public class TestService {
                private String name;
                
                public String getName() {
                    return name;
                }
            }
            """;
            
        PsiJavaFile testFile = createTestFile(javaCode);
        JavaASTAnalyzer analyzer = new JavaASTAnalyzer();
        AnalysisResult result = analyzer.analyze(testFile);
        
        assertEquals(2, result.getNodes().size());
        assertTrue(result.getNodes().containsKey("com.example.TestService"));
    }
}
```

## 🔧 工具类实现

### JavaPsiUtils

提供常用的PSI操作方法：

```java
public class JavaPsiUtils {
    
    public static String getFullyQualifiedName(PsiClass psiClass) {
        return psiClass.getQualifiedName();
    }
    
    public static String getMethodId(PsiMethod method) {
        PsiClass containingClass = method.getContainingClass();
        if (containingClass == null) return method.getName();
        
        String className = containingClass.getQualifiedName();
        String methodName = method.getName();
        String parameters = getParameterTypes(method);
        
        return className + "." + methodName + "(" + parameters + ")";
    }
    
    public static boolean isInSourceDirectory(VirtualFile file) {
        return file.getPath().contains("/src/main/java/");
    }
    
    public static int getLineNumber(PsiElement element) {
        Document document = PsiDocumentManager.getInstance(element.getProject())
            .getDocument(element.getContainingFile());
        if (document != null) {
            return document.getLineNumber(element.getTextOffset()) + 1;
        }
        return 0;
    }
}
```

### 调用关系分析

判断内部和外部调用：

```java
private boolean isInternalCall(PsiMethodCallExpression expression) {
    PsiMethod targetMethod = expression.resolveMethod();
    if (targetMethod == null) return false;
    
    PsiClass containingClass = targetMethod.getContainingClass();
    if (containingClass == null) return false;
    
    // 检查是否在源码目录中
    VirtualFile containingFile = containingClass.getContainingFile().getVirtualFile();
    return JavaPsiUtils.isInSourceDirectory(containingFile);
}
```

## 🚀 性能优化

### 缓存策略

```java
public class CachedJavaAnalyzer implements ASTAnalyzer {
    private final Map<String, AnalysisResult> cache = new ConcurrentHashMap<>();
    
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        String cacheKey = generateCacheKey(psiFile);
        return cache.computeIfAbsent(cacheKey, key -> performAnalysis(psiFile));
    }
    
    private String generateCacheKey(PsiFile psiFile) {
        VirtualFile vFile = psiFile.getVirtualFile();
        return vFile.getPath() + ":" + vFile.getModificationStamp();
    }
}
```

## 🔌 扩展开发

### 添加新分析功能

```java
public class EnhancedJavaAnalyzer extends JavaASTAnalyzer {
    
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        AnalysisResult result = super.analyze(psiFile);
        
        // 添加自定义分析
        analyzeDesignPatterns(psiFile, result);
        analyzeCodeSmells(psiFile, result);
        
        return result;
    }
    
    private void analyzeDesignPatterns(PsiFile psiFile, AnalysisResult result) {
        // 设计模式识别逻辑
    }
}
```

## 🐛 调试技巧

### 启用调试日志

```java
private static final Logger logger = Logger.getInstance(JavaASTAnalyzer.class);

private void debugLog(String message, Object... args) {
    if (Boolean.getBoolean("ast.analyzer.debug")) {
        logger.info("[JAVA-DEBUG] " + String.format(message, args));
    }
}
```

### 常见问题解决

1. **PSI访问异常**：确保在ReadAction中操作
2. **性能问题**：使用缓存，避免重复计算
3. **空指针异常**：检查PSI元素的有效性
4. **内存泄漏**：及时清理缓存和引用

## 📚 参考资源

- [IntelliJ Platform SDK](https://plugins.jetbrains.com/docs/intellij/)
- [PSI API文档](https://plugins.jetbrains.com/docs/intellij/psi.html)
- [Java PSI参考](https://plugins.jetbrains.com/docs/intellij/psi-java.html)

通过遵循这些开发指南，您可以高效地开发和扩展Java插件功能。