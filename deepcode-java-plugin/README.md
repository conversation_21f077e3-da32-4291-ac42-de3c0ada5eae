# DeepCode Java Plugin

Java 语言专用的 AST 分析插件，基于 IntelliJ Platform 的 PSI API 实现精确的 Java 代码结构分析和调用关系提取。

## 🎯 插件特性

### 核心功能
- 🏗️ **完整的 Java AST 解析**：支持类、接口、枚举、注解的结构分析
- 🔍 **精确的方法分析**：解析方法签名、参数、返回类型、修饰符
- 📊 **字段分析**：识别实例字段、静态字段、常量
- 📞 **智能调用分析**：区分内部调用和外部库调用
- 📦 **Maven 模块支持**：自动识别 Maven 项目模块结构
- 🎯 **精确行号定位**：准确计算代码元素的行号位置

### Java 语言特性支持
- ✅ **类继承关系**：父类、接口实现、内部类
- ✅ **泛型支持**：泛型类、泛型方法、通配符
- ✅ **注解处理**：类注解、方法注解、字段注解
- ✅ **静态导入**：静态方法、静态字段导入
- ✅ **Lambda 表达式**：函数式接口、Stream API
- ✅ **方法引用**：静态方法引用、实例方法引用

## 🏗️ 适用场景

### 目标 IDE
- **IntelliJ IDEA Community Edition** - 主要目标平台
- **IntelliJ IDEA Ultimate Edition** - 完全兼容
- 其他基于 IntelliJ Platform 的 IDE

### 支持的 Java 版本
- Java 8+
- Java 11
- Java 17 (推荐)
- Java 21

## 📦 安装和配置

### 安装方式一：构建安装
```bash
# 构建 Java 插件
./gradlew :deepcode-java-plugin:build

# 生成的插件位于
# deepcode-java-plugin/build/distributions/deepcode-java-plugin-*.zip
```

### 安装方式二：开发模式
```bash
# 启动带插件的 IDEA 开发环境
./gradlew :deepcode-java-plugin:runIde

# 在沙盒环境中测试插件
./gradlew :deepcode-java-plugin:runIdeSandboxed
```

### 手动安装
1. 打开 IntelliJ IDEA
2. `File` → `Settings` → `Plugins`
3. 齿轮图标 → `Install Plugin from Disk...`
4. 选择构建生成的 zip 文件
5. 重启 IDE

## 🚀 使用方法

### 基本使用

1. **打开 Java 文件**
   - 在 IDEA 中打开任意 `.java` 文件
   - 确保文件属于一个有效的 Java 项目

2. **启动分析**
   ```
   方式一：右键菜单 → "Quick AST Analyzer"
   方式二：快捷键 Ctrl+Alt+Shift+T
   方式三：Tools → AST Analysis → Quick Analysis
   ```

3. **查看结果**
   - 弹窗显示分析摘要
   - 工具窗口显示详细信息
   - 支持导出为 JSON 格式

### 工具窗口使用

通过 `View` → `Tool Windows` → `AST Analysis` 打开工具窗口，包含：

- **节点树**：显示类、方法、字段的层级结构
- **调用关系**：显示方法间的调用关系表格
- **统计信息**：显示代码结构统计数据

### 导出功能

支持将分析结果导出为：
- **JSON 格式**：完整的结构化数据
- **CSV 格式**：适合电子表格分析  
- **HTML 报告**：可视化分析报告

## 🔍 分析示例

### Java 类分析

对于以下 Java 代码：

```java
package com.example.service;

import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class UserService {
    private UserRepository userRepository;
    
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }
    
    public User getUserById(Long id) {
        return userRepository.findById(id);
    }
}
```

### 分析结果

**识别的节点**：
```
1. CLASS: com.example.service.UserService
   - 位置: UserService.java:6
   - 修饰符: public
   - 注解: @Service

2. FIELD: com.example.service.UserService.userRepository  
   - 位置: UserService.java:7
   - 类型: UserRepository
   - 修饰符: private

3. METHOD: com.example.service.UserService.UserService(UserRepository)
   - 位置: UserService.java:9
   - 返回类型: void (构造函数)
   - 参数: UserRepository userRepository

4. METHOD: com.example.service.UserService.getAllUsers()
   - 位置: UserService.java:13
   - 返回类型: List<User>
   - 修饰符: public

5. METHOD: com.example.service.UserService.getUserById(Long)  
   - 位置: UserService.java:17
   - 返回类型: User
   - 参数: Long id
```

**调用关系**：
```
1. UserService.getAllUsers() → UserRepository.findAll() [外部调用]
   - 位置: UserService.java:14
   
2. UserService.getUserById(Long) → UserRepository.findById(Long) [外部调用]
   - 位置: UserService.java:18
```

## 🔧 特殊功能

### Maven 模块识别

自动识别 Maven 项目结构：

```java
public class ModuleDetection {
    // 自动检测项目模块
    // 从 pom.xml 读取 artifactId 作为模块名
    // 支持多模块项目的层级结构
}
```

### 内外部调用区分

**内部调用判定条件**：
- 被调用类在项目源码目录 (src/main/java) 中
- 被调用类属于当前项目模块

**外部调用判定条件**：
- Java 标准库：`java.*`, `javax.*`, `sun.*`
- 第三方库：`org.apache.*`, `org.springframework.*`
- 依赖库中的类（非源码目录）

### 泛型处理

支持复杂的泛型分析：

```java
// 泛型类
public class GenericService<T extends BaseEntity> {
    // 泛型方法
    public <R> R process(Function<T, R> processor) {
        // Lambda 表达式调用
        return processor.apply(entity);
    }
}
```

## 🛠️ 开发指南

### 环境搭建

```bash
# 克隆项目
git clone <repository>
cd deepcode-ast-plugin

# 确保 Java 17+
java -version

# 构建项目
./gradlew :deepcode-java-plugin:build

# 运行开发环境
./gradlew :deepcode-java-plugin:runIde
```

### 代码结构

```
deepcode-java-plugin/src/main/java/com/sankuai/deepcode/astplugin/java/
├── JavaASTAnalyzer.java        # 主分析器实现
├── JavaNodeVisitor.java        # PSI 节点访问器
├── JavaCallAnalyzer.java       # 调用关系分析器
├── JavaImportAnalyzer.java     # 导入语句分析器
└── util/
    ├── JavaPsiUtils.java       # PSI 工具方法
    ├── JavaSignatureUtils.java # 方法签名工具
    └── MavenModuleDetector.java # Maven 模块检测
```

### 核心分析流程

```java
public class JavaASTAnalyzer implements ASTAnalyzer {
    
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        return ReadAction.compute(() -> {
            AnalysisResult result = new AnalysisResult();
            
            // 1. 分析文件结构
            analyzeFileStructure(psiFile, result);
            
            // 2. 分析调用关系  
            analyzeCallRelations(psiFile, result);
            
            // 3. 分析导入关系
            analyzeImports(psiFile, result);
            
            // 4. 计算统计信息
            calculateStatistics(result);
            
            return result;
        });
    }
}
```

### 扩展分析器

添加新的 Java 语言特性支持：

```java
public class EnhancedJavaAnalyzer extends JavaASTAnalyzer {
    
    @Override
    protected void analyzeSpecialCases(PsiElement element, AnalysisResult result) {
        super.analyzeSpecialCases(element, result);
        
        // 添加新的分析逻辑
        if (element instanceof PsiRecord) {
            analyzeRecord((PsiRecord) element, result);
        }
        
        if (element instanceof PsiSwitchExpression) {
            analyzeSwitchExpression((PsiSwitchExpression) element, result);
        }
    }
}
```

### 调试技巧

#### 启用调试模式

```bash
# 方式一：环境变量
export AST_ANALYZER_DEBUG=true
./gradlew :deepcode-java-plugin:runIde

# 方式二：JVM 参数
./gradlew :deepcode-java-plugin:runIde -Dast.analyzer.debug=true
```

#### 查看调试输出

```java
private void debugLog(String message, Object... args) {
    if (isDebugEnabled()) {
        logger.info("[DEBUG] " + String.format(message, args));
    }
}

// 使用示例
debugLog("Analyzing class: %s at line %d", className, lineNumber);
debugLog("Found method call: %s -> %s", caller, callee);
```

### 性能优化

#### PSI 访问优化

```java
// ✅ 正确：批量访问 PSI
public void analyzeMethods(PsiClass psiClass, AnalysisResult result) {
    ReadAction.run(() -> {
        PsiMethod[] methods = psiClass.getMethods();
        for (PsiMethod method : methods) {
            analyzeMethod(method, result);
        }
    });
}

// ❌ 错误：多次进入 ReadAction  
public void analyzeMethodsSlowly(PsiClass psiClass, AnalysisResult result) {
    for (PsiMethod method : psiClass.getMethods()) {
        ReadAction.run(() -> analyzeMethod(method, result));
    }
}
```

#### 缓存使用

```java
private final Map<String, AnalysisResult> fileCache = new ConcurrentHashMap<>();

public AnalysisResult analyze(PsiFile psiFile) {
    String fileKey = generateCacheKey(psiFile);
    return fileCache.computeIfAbsent(fileKey, key -> performAnalysis(psiFile));
}
```

## ⚠️ 使用注意事项

### 项目配置

1. **确保项目结构正确**
   - 标准的 Maven/Gradle 项目结构
   - 正确的 `src/main/java` 目录
   - 有效的 `pom.xml` 或 `build.gradle`

2. **解决依赖问题**
   - 确保项目能够正常编译
   - 检查缺失的依赖库
   - 刷新 Maven/Gradle 依赖

### 性能考虑

1. **大文件处理**
   - 文件超过 2000 行时分析较慢
   - 考虑分批处理大型项目

2. **内存使用**
   - 复杂项目可能消耗较多内存
   - 适当调整 IDE 内存设置

### 限制说明

1. **动态特性**
   - 无法分析反射调用
   - 运行时生成的代码无法识别

2. **复杂泛型**
   - 嵌套泛型可能解析不完整
   - 类型擦除后的信息丢失

## 🔗 相关文档

- [项目总览](../README.md)
- [共享核心模块](../shared-core/README.md)
- [共享UI模块](../shared-ui/README.md)  
- [Python插件](../deepcode-python-plugin/README.md)
- [开发文档](../DEVELOPMENT_GUIDE.md)
- [使用手册](../USAGE_GUIDE.md)

## 🐛 问题排查

### 常见问题

1. **插件未加载**
   - 检查 IDE 版本兼容性（>=2024.1.4）
   - 确认 Java 插件已启用
   - 查看 IDE 错误日志

2. **分析结果为空**
   - 启用调试模式查看详细过程
   - 检查文件是否为有效 Java 源文件
   - 确认项目结构正确

3. **调用关系不准确**
   - 检查项目依赖是否正确配置
   - 确认类路径设置
   - 查看调试日志中的判定过程

### 日志查看

IDE 日志位置：
- **Windows**: `%APPDATA%\JetBrains\<IDE>\<version>\log\idea.log`
- **macOS**: `~/Library/Logs/JetBrains/<IDE>/`
- **Linux**: `~/.cache/JetBrains/<IDE>/log/`

通过 DeepCode Java Plugin，您可以深入了解 Java 代码的结构和调用关系，提高代码理解效率和重构安全性。