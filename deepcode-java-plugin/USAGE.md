# Java 插件使用指南

本文档详细介绍 DeepCode Java Plugin 的使用方法、功能特性和最佳实践。

## 🎯 快速开始

### 安装验证

安装插件后，验证是否成功：

1. 重启 IntelliJ IDEA
2. 打开任意 Java 项目
3. 检查以下位置是否有插件选项：
   - 右键菜单中的 "Quick AST Analyzer"
   - `Tools` 菜单中的 "AST Analysis"
   - 工具窗口列表中的 "AST Analysis"

### 第一次使用

1. **打开 Java 文件**：选择一个中等大小的 .java 文件（200-500行）
2. **运行分析**：右键选择 "Quick AST Analyzer" 或按 `Ctrl+Alt+Shift+T`
3. **查看结果**：会弹出分析结果对话框，显示基本统计信息

## 🎨 用户界面

### 快速分析对话框

分析完成后会显示如下信息：

```
┌───────── Java AST Analysis Results ─────────┐
│                                             │
│ File: UserService.java                      │
│ ✓ Analysis completed in 156ms               │
│                                             │
│ 📊 Summary:                                 │
│ • Classes: 1                                │
│ • Methods: 4                                │
│ • Fields: 2                                 │
│ • Call Relations: 6                         │
│                                             │
│ 🔗 Call Relations:                          │
│ • Internal calls: 2                         │
│ • External calls: 4                         │
│                                             │
│ [View Details] [Export JSON] [Close]        │
└─────────────────────────────────────────────┘
```

### 工具窗口

通过 `View` → `Tool Windows` → `AST Analysis` 打开详细工具窗口：

#### 节点树面板
```
AST 节点
├── 📦 com.example.service
│   └── 🏛️ UserService
│       ├── 📦 userRepository: UserRepository
│       ├── 📦 cache: Map<Long, User>
│       ├── ⚙️ UserService(UserRepository)
│       ├── ⚙️ getUserById(Long): User
│       ├── ⚙️ getAllUsers(): List<User>
│       └── ⚙️ clearCache(): void
```

#### 调用关系面板
| 调用方 | 被调用方 | 类型 | 位置 |
|--------|----------|------|------|
| UserService.getUserById | userRepository.findById | 外部 | 行 25 |
| UserService.getUserById | cache.put | 外部 | 行 27 |
| UserService.getAllUsers | userRepository.findAll | 外部 | 行 32 |
| UserService.clearCache | cache.clear | 外部 | 行 36 |

#### 统计面板
- **节点统计**：类 1 个、方法 4 个、字段 2 个
- **调用统计**：总计 4 个调用关系，外部调用 4 个
- **复杂度**：平均方法行数 8 行

## 🔧 详细功能

### 节点分析

#### 类（CLASS）
- **识别内容**：public/private 类、接口、枚举、注解
- **提取信息**：包名、继承关系、修饰符、注解
- **示例**：
```java
@Service
public class UserService extends BaseService implements UserOperations {
    // 识别为：
    // 类型: CLASS
    // 名称: UserService
    // 包名: com.example.service
    // 修饰符: public
    // 基类: BaseService
    // 接口: UserOperations
    // 注解: @Service
}
```

#### 方法（METHOD）
- **识别内容**：构造函数、实例方法、静态方法
- **提取信息**：参数类型、返回类型、修饰符、注解
- **示例**：
```java
@Transactional
public User getUserById(Long id, boolean includeDeleted) {
    // 识别为：
    // 类型: METHOD
    // 签名: getUserById(Long, boolean): User
    // 修饰符: public
    // 注解: @Transactional
}
```

#### 字段（FIELD）
- **识别内容**：实例变量、静态变量、常量
- **提取信息**：类型、修饰符、初始值
- **示例**：
```java
private static final String DEFAULT_NAME = "Unknown";
private UserRepository userRepository;

// 识别为两个FIELD节点：
// 1. DEFAULT_NAME: String (static final)
// 2. userRepository: UserRepository (private)
```

### 调用关系分析

#### 内部调用判定
满足以下条件的调用被认为是内部调用：
- 被调用方法所在类的源文件在项目的 `src/main/java` 目录中
- 被调用方法属于同一个 Maven 模块

**判定示例**：
```java
// UserService.java 在 src/main/java/com/example 中
public class UserService {
    private UserValidator validator = new UserValidator(); // UserValidator 也在项目中
    
    public boolean validateUser(User user) {
        return validator.validate(user); // → 内部调用
    }
}
```

#### 外部调用判定
包括以下类型的调用：

1. **Java 标准库**：
```java
String.valueOf(123);           // java.lang.String
List.of(1, 2, 3);             // java.util.List
System.out.println("test");    // java.lang.System
```

2. **第三方库**：
```java
log.info("message");                    // org.slf4j.Logger
Optional.of(value);                     // 来自依赖的类
userRepository.save(user);              // Spring Data JPA
```

3. **无法解析的调用**：
```java
// 依赖缺失或配置错误时
unknownObject.someMethod();             // 无法找到定义
```

### Maven 模块识别

插件会自动检测 Maven 项目结构：

```xml
<!-- pom.xml -->
<modelVersion>4.0.0</modelVersion>
<groupId>com.example</groupId>
<artifactId>user-service</artifactId>  <!-- 用作模块名 -->
<version>1.0.0</version>
```

**检测结果**：
- 模块名：`user-service`
- 所有该模块内的类都会标记相同的模块名
- 跨模块调用会被正确识别

## 📊 导出和报告

### JSON 导出格式

```json
{
  "filePath": "/src/main/java/com/example/UserService.java",
  "language": "JAVA",
  "analysisTime": "2024-06-18T10:30:45.123Z",
  "nodes": {
    "com.example.UserService": {
      "id": "com.example.UserService",
      "type": "CLASS",
      "name": "UserService",
      "packageName": "com.example",
      "moduleName": "user-service",
      "lineNumber": 15,
      "signature": "public class UserService",
      "language": "JAVA"
    },
    "com.example.UserService.getUserById(Long)": {
      "id": "com.example.UserService.getUserById(Long)",
      "type": "METHOD",
      "name": "getUserById",
      "packageName": "com.example",
      "lineNumber": 25,
      "signature": "getUserById(Long id): User",
      "language": "JAVA"
    }
  },
  "callRelations": [
    {
      "callerId": "com.example.UserService.getUserById(Long)",
      "calleeId": "UserRepository.findById(Long)",
      "isInternal": false,
      "instances": [
        {
          "lineNumber": 27,
          "context": "userRepository.findById(id)"
        }
      ]
    }
  ],
  "statistics": {
    "totalNodes": 6,
    "nodesByType": {
      "CLASS": 1,
      "METHOD": 4,
      "FIELD": 1
    },
    "totalCalls": 4,
    "internalCalls": 1,
    "externalCalls": 3
  }
}
```

### CSV 导出格式

**节点信息表 (nodes.csv)**：
```csv
ID,Type,Name,Package,Module,File,Line,Signature
com.example.UserService,CLASS,UserService,com.example,user-service,UserService.java,15,"public class UserService"
com.example.UserService.getUserById,METHOD,getUserById,com.example,user-service,UserService.java,25,"getUserById(Long): User"
```

**调用关系表 (calls.csv)**：
```csv
Caller,Callee,Internal,Line,Context
com.example.UserService.getUserById,UserRepository.findById,false,27,userRepository.findById(id)
```

### HTML 报告

生成包含可视化图表的 HTML 报告：
- 节点统计柱状图
- 调用关系网络图  
- 包依赖关系图
- 详细的代码结构表格

## 🎯 使用场景

### 代码理解

**场景**：接手新项目，需要快速了解代码结构

**操作步骤**：
1. 选择核心业务类（如 Service、Controller）
2. 运行 AST 分析
3. 查看类的方法和依赖关系
4. 通过调用关系了解业务流程

**收益**：
- 快速识别核心业务逻辑
- 了解代码分层和依赖关系
- 发现潜在的设计问题

### 重构准备

**场景**：准备重构一个方法，需要评估影响范围

**操作步骤**：
1. 分析包含目标方法的文件
2. 查看该方法的调用关系
3. 导出 JSON 数据，编写脚本分析调用链
4. 评估重构的影响范围

**收益**：
- 确定重构的安全边界
- 识别需要同步修改的代码
- 降低重构风险

### 架构审查

**场景**：审查项目架构是否符合设计约束

**操作步骤**：
1. 批量分析多个核心文件
2. 导出调用关系数据
3. 使用数据分析工具检查：
   - 是否有循环依赖
   - 分层是否清晰
   - 外部依赖是否合理

**收益**：
- 发现架构问题
- 验证设计约束
- 指导后续优化

### 文档生成

**场景**：为遗留代码生成技术文档

**操作步骤**：
1. 分析核心模块的主要类
2. 导出 HTML 报告
3. 结合代码注释生成完整文档

**收益**：
- 自动化文档生成
- 保持文档和代码同步
- 提高文档质量

## ⚙️ 高级配置

### 调试模式

启用详细的调试输出：

```bash
# 方式一：环境变量
export AST_ANALYZER_DEBUG=true
./gradlew :deepcode-java-plugin:runIde

# 方式二：IDE 启动参数
-Dast.analyzer.debug=true
```

调试输出示例：
```
[DEBUG] Starting analysis for: UserService.java
[DEBUG] Found class: com.example.UserService at line 15
[DEBUG] Found method: getUserById(Long) at line 25
[DEBUG] Analyzing call: userRepository.findById(id) at line 27
[DEBUG] Resolved call target: UserRepository.findById(Long) - external
[DEBUG] Maven module detected: user-service
[DEBUG] Analysis completed: 6 nodes, 4 call relations
```

### 性能调优

**大文件处理**：
- 文件超过 2000 行时，分析时间会显著增加
- 建议分批分析或使用更强的硬件

**内存配置**：
```
# IDE 内存配置 (idea64.exe.vmoptions 或 idea.vmoptions)
-Xmx2048m
-XX:ReservedCodeCacheSize=512m
```

### 过滤器配置

通过代码配置分析过滤器：

```java
// 自定义分析器配置
AnalysisConfig config = new AnalysisConfig();
config.excludeTestFiles(true);
config.excludeGeneratedCode(true);
config.setMaxDepth(10);
config.addPackageFilter("com.example.*");

AnalysisResult result = analyzer.analyze(file, config);
```

## ❗ 注意事项

### 项目要求

1. **有效的 Java 项目结构**：
   ```
   project/
   ├── src/main/java/     # 必须存在
   ├── pom.xml           # Maven 项目
   └── build.gradle      # 或 Gradle 项目
   ```

2. **编译状态**：项目应该能够正常编译，否则某些调用关系可能无法解析

3. **依赖完整**：确保所有依赖都已正确配置和下载

### 性能影响

1. **IDE 响应性**：分析大文件时可能会暂时影响 IDE 响应速度
2. **内存使用**：复杂项目可能消耗较多内存
3. **磁盘 I/O**：导出大型报告时会产生磁盘写入操作

### 限制说明

1. **反射调用**：无法分析运行时反射调用
2. **动态代理**：Spring AOP 等动态代理调用无法追踪
3. **泛型擦除**：复杂的泛型信息可能丢失
4. **注解处理器**：编译时注解处理生成的代码无法分析

## 🔧 故障排除

### 常见问题

**问题一：分析结果为空**

可能原因：
- 文件不是有效的 Java 源文件
- 项目结构不正确
- IDE 索引未完成

解决方法：
1. 确认文件扩展名为 .java
2. 检查项目结构是否标准
3. 等待 IDE 索引完成后重试
4. 启用调试模式查看详细日志

**问题二：调用关系不准确**

可能原因：
- 项目编译失败
- 依赖配置错误
- 类路径问题

解决方法：
1. 确保项目能正常编译
2. 刷新 Maven/Gradle 依赖
3. 检查 IDE 的项目设置
4. 查看编译错误并修复

**问题三：性能问题**

可能原因：
- 文件过大
- 系统资源不足
- PSI 索引问题

解决方法：
1. 分析较小的文件进行测试
2. 增加 IDE 可用内存
3. 重建 PSI 索引：`File` → `Invalidate Caches and Restart`

### 日志查看

查看插件运行日志：

**日志位置**：
- Windows：`%APPDATA%\JetBrains\IntelliJIdea2024.1\log\idea.log`
- macOS：`~/Library/Logs/JetBrains/IntelliJIdea2024.1/`
- Linux：`~/.cache/JetBrains/IntelliJIdea2024.1/log/`

**关键词搜索**：
- `JavaASTAnalyzer`
- `DeepCode AST`
- `QuickAnalyzerAction`

通过proper使用 Java 插件，您可以深入了解 Java 代码的结构和调用关系，提高开发效率和代码质量。