package com.sankuai.deepcode.astplugin.java;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import com.sankuai.deepcode.astplugin.export.ExportRegistry;
import com.sankuai.deepcode.astplugin.java.export.JavaExportService;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 插件启动组件 - 确保Java分析器在插件启动时正确注册
 *
 * <AUTHOR>
 */
public class JavaPluginStartup implements ProjectActivity {

    private static final Logger LOG = Logger.getInstance(JavaPluginStartup.class);

    @Nullable
    @Override
    public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        // 在后台线程中初始化分析器服务
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                LOG.info("🚀 Java Plugin startup - initializing analyzer service");

                // 获取或创建JavaAnalyzerService实例，这会触发分析器注册
                JavaAnalyzerService javaService = ApplicationManager.getApplication()
                        .getService(JavaAnalyzerService.class);

                // 确保分析器已注册
                if (javaService != null) {
                    javaService.ensureAnalyzerRegistered();
                    LOG.info("✅ Java analyzer registered successfully");
                } else {
                    LOG.error("❌ Failed to get JavaAnalyzerService instance");
                }

                // 注册Java导出服务（当前为空实现）
                try {
                    ExportRegistry exportRegistry = ExportRegistry.getInstance();
                    JavaExportService javaExportService = new JavaExportService();
                    exportRegistry.registerExportService(javaExportService);
                    LOG.info("✅ Java export service registered successfully");
                } catch (Exception e) {
                    LOG.error("❌ Failed to register Java export service", e);
                }

                LOG.info("✅ Java Plugin startup completed successfully");

            } catch (Exception e) {
                LOG.error("💥 Error during Java plugin startup", e);
            }
        });
        return Unit.INSTANCE;
    }
}