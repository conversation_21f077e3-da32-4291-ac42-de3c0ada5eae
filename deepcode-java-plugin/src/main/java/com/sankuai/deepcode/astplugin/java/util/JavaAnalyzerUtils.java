package com.sankuai.deepcode.astplugin.java.util;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiJavaFile;

/**
 * Java分析器工具类
 * 
 * <AUTHOR>
 */
public class JavaAnalyzerUtils {
    
    /**
     * 获取项目相对文件路径
     */
    public static String getFilePath(PsiFile psiFile) {
        try {
            VirtualFile virtualFile = psiFile.getVirtualFile();
            Project project = psiFile.getProject();

            if (virtualFile != null) {
                // 获取项目根目录
                String projectBasePath = project.getBasePath();
                if (projectBasePath != null) {
                    String absolutePath = virtualFile.getPath();
                    // 计算相对于项目根目录的路径
                    if (absolutePath.startsWith(projectBasePath)) {
                        String relativePath = absolutePath.substring(projectBasePath.length());
                        // 移除开头的路径分隔符
                        if (relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                            relativePath = relativePath.substring(1);
                        }
                        // 统一使用正斜杠作为路径分隔符
                        relativePath = relativePath.replace("\\", "/");
                        return relativePath;
                    }
                }
                // 如果无法获取项目根目录，返回绝对路径
                return virtualFile.getPath();
            }
        } catch (Exception e) {
            // 静默失败，返回文件名
        }
        return psiFile.getName();
    }

    /**
     * 检测Maven模块信息
     */
    public static String detectMavenModule(PsiFile psiFile) {
        try {
            VirtualFile virtualFile = psiFile.getVirtualFile();
            if (virtualFile != null) {
                VirtualFile current = virtualFile.getParent();
                while (current != null) {
                    VirtualFile pomFile = current.findChild("pom.xml");
                    if (pomFile != null) {
                        String moduleName = parsePomArtifactId(pomFile);
                        if (moduleName != null) {
                            return moduleName;
                        }
                        break;
                    }
                    current = current.getParent();
                }
            }
        } catch (Exception e) {
            // 静默失败
        }
        return null;
    }
    
    /**
     * 解析pom.xml文件获取artifactId
     */
    public static String parsePomArtifactId(VirtualFile pomFile) {
        try {
            String content = new String(pomFile.contentsToByteArray());
            int start = content.indexOf("<artifactId>");
            if (start != -1) {
                start += "<artifactId>".length();
                int end = content.indexOf("</artifactId>", start);
                if (end != -1) {
                    return content.substring(start, end).trim();
                }
            }
        } catch (Exception e) {
            // 静默失败
        }
        return null;
    }
    
    /**
     * 从Java文件中获取包名
     */
    public static String getPackageNameFromFile(PsiFile file) {
        try {
            if (file instanceof PsiJavaFile javaFile) {
                return javaFile.getPackageName();
            }

            // 回退方法：从文件内容中解析包名
            return parsePackageFromContent(file);
        } catch (Exception e) {
            // 静默失败
        }
        return "";
    }
    
    /**
     * 从文件内容中解析包名
     */
    public static String parsePackageFromContent(PsiFile file) {
        try {
            String content = file.getText();
            if (content != null) {
                String[] lines = content.split("\n");
                for (String line : lines) {
                    line = line.trim();
                    if (line.startsWith("package ") && line.endsWith(";")) {
                        return line.substring(8, line.length() - 1).trim();
                    }
                }
            }
        } catch (Exception e) {
            // 静默失败
        }
        return "";
    }
    
    /**
     * 简化的文件有效性检查
     */
    public static boolean isValidJavaFile(PsiFile psiFile) {
        return psiFile != null && (
            psiFile instanceof PsiJavaFile || 
            psiFile.getName().endsWith(".java") ||
            "JAVA".equals(psiFile.getLanguage().getID())
        );
    }
}