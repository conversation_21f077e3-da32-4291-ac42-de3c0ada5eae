package com.sankuai.deepcode.astplugin.java;

import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.roots.ProjectFileIndex;
import com.intellij.openapi.roots.ProjectRootManager;
import com.intellij.openapi.vfs.VfsUtilCore;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.*;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.analyzer.ASTAnalyzer;
import com.sankuai.deepcode.astplugin.java.util.JavaAnalyzerUtils;
import com.sankuai.deepcode.astplugin.model.*;

import java.util.*;

/**
 * Java语言专用的AST分析器
 * <p>
 * 特性：
 * - 线程安全：所有PSI访问都在ReadAction中进行
 * - 功能完备：支持类、方法、字段和调用关系分析
 * - Maven模块识别：支持多模块Maven工程的模块识别
 * - 错误处理：优雅的异常处理和回退机制
 * - 调试支持：可配置的调试输出
 *
 * <AUTHOR>
 */
public class JavaASTAnalyzer implements ASTAnalyzer {

    private static final Logger LOG = Logger.getInstance(JavaASTAnalyzer.class);
    private static final boolean DEBUG_MODE = Boolean.getBoolean("ast.analyzer.debug");
    // 添加强制调试模式，用于问题诊断
    private static final boolean FORCE_DEBUG = true;

    /**
     * 外部方法信息类，用于存储 jar 包和文件路径信息
     *
     * @param jarInfo   jar 包信息
     * @param classPath class 文件路径
     */
    private record ExternalMethodInfo(String jarInfo, String classPath) {
        private ExternalMethodInfo(String jarInfo, String classPath) {
            this.jarInfo = jarInfo != null ? jarInfo : "UNKNOWN_JAR";
            this.classPath = classPath != null ? classPath : "UNKNOWN_CLASS";
        }
    }

    @Override
    public boolean supports(PsiFile psiFile) {
        return psiFile instanceof PsiJavaFile ||
                psiFile.getName().endsWith(getSupportedLanguage().getFileExtension()) ||
                "JAVA".equals(psiFile.getLanguage().getID());
    }

    @Override
    public Language getSupportedLanguage() {
        return Language.JAVA;
    }

    /**
     * 分析Java文件，返回完整的分析结果
     */
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        debugLog("=== Starting Java file analysis ===");
        debugLog("Input object type: " + (psiFile != null ? psiFile.getClass().getName() : "null"));

        // 运行时类型检查 - 放宽验证条件
        if (!isValidJavaFile(psiFile)) {
            debugLog("ERROR: Invalid Java file type validation failed");
            AnalysisResult errorResult = new AnalysisResult("unknown", Language.JAVA);
            errorResult.addError("Invalid Java file type: " +
                    (psiFile != null ? psiFile.getClass().getName() : "null"));
            return errorResult;
        }

        debugLog("File name: " + psiFile.getName());
        debugLog("File language: " + psiFile.getLanguage().getID());

        return ReadAction.compute(() -> {
            // 获取项目相对路径作为文件标识
            String projectRelativePath = JavaAnalyzerUtils.getFilePath(psiFile);
            AnalysisResult result = new AnalysisResult(projectRelativePath, Language.JAVA);

            try {
                debugLog("Starting analysis for file: " + projectRelativePath);

                // 简化Java文件检查
                if (!isValidJavaFile(psiFile)) {
                    String error = "Not a Java file: " + psiFile.getName() +
                            " (language: " + psiFile.getLanguage().getID() + ")";
                    debugLog("ERROR: " + error);
                    result.addError(error);
                    return result;
                }

                // 获取Maven模块信息
                String moduleName = detectMavenModule(psiFile);
                String filePath = JavaAnalyzerUtils.getFilePath(psiFile);
                debugLog("Module: " + moduleName + ", Path: " + filePath);

                // 按顺序执行各项分析，增加详细日志
                debugLog("Step 1: Analyzing package...");
                analyzePackage(psiFile, result);

                debugLog("Step 2: Analyzing imports...");
                analyzeImports(psiFile, result);

                debugLog("Step 3: Analyzing classes...");
                analyzeClasses(psiFile, result, moduleName, filePath);

                debugLog("Step 4: Analyzing call relations...");
                analyzeCallRelations(psiFile, result);

                debugLog("Analysis completed successfully. Found " +
                        result.getNodes().size() + " nodes and " +
                        result.getCallRelations().size() + " call relations");

                // 输出统计信息
                debugLog("Statistics: " + result.getStatistics());

            } catch (Exception e) {
                String errorMsg = "Analysis failed for " + psiFile.getName() + ": " + e.getMessage();
                result.addError(errorMsg);
                debugLog("ERROR: " + errorMsg);
                e.printStackTrace(); // 强制输出堆栈跟踪
            }

            return result;
        });
    }

    /**
     * 检查是否为有效的Java文件 - 简化版本
     */
    private boolean isValidJavaFile(PsiFile psiFile) {
        return JavaAnalyzerUtils.isValidJavaFile(psiFile) && supports(psiFile);
    }

    /**
     * 检测Maven模块名
     */
    private String detectMavenModule(PsiFile javaFile) {
        try {
            Project project = javaFile.getProject();
            VirtualFile virtualFile = javaFile.getVirtualFile();

            if (virtualFile != null) {
                // 查找最近的pom.xml文件
                VirtualFile current = virtualFile.getParent();
                while (current != null) {
                    VirtualFile pomFile = current.findChild("pom.xml");
                    if (pomFile != null) {
                        // 尝试解析pom.xml获取artifactId
                        String moduleName = parsePomArtifactId(pomFile);
                        if (moduleName != null) {
                            debugLog("Detected Maven module: " + moduleName);
                            return moduleName;
                        }
                        break;
                    }
                    current = current.getParent();
                }
            }
        } catch (Exception e) {
            debugLog("Error detecting Maven module: " + e.getMessage());
        }
        return null;
    }

    /**
     * 解析pom.xml文件获取artifactId
     */
    private String parsePomArtifactId(VirtualFile pomFile) {
        try {
            String content = new String(pomFile.contentsToByteArray());
            // 简单的XML解析，查找<artifactId>标签
            int start = content.indexOf("<artifactId>");
            if (start != -1) {
                start += "<artifactId>".length();
                int end = content.indexOf("</artifactId>", start);
                if (end != -1) {
                    return content.substring(start, end).trim();
                }
            }
        } catch (Exception e) {
            debugLog("Error parsing pom.xml: " + e.getMessage());
        }
        return null;
    }


    /**
     * 分析包信息
     */
    private void analyzePackage(PsiFile javaFile, AnalysisResult result) {
        try {
            String packageName = getPackageNameFromFile(javaFile);
            if (!packageName.isEmpty()) {
                result.updateStatistics("packages", 1);
                debugLog("Found package: " + packageName);
            }
        } catch (Exception e) {
            debugLog("Error analyzing package: " + e.getMessage());
        }
    }

    /**
     * 分析导入语句
     */
    private void analyzeImports(PsiFile javaFile, AnalysisResult result) {
        try {
            if (!(javaFile instanceof PsiJavaFile psiJavaFile)) {
                debugLog("File is not a PsiJavaFile, skipping import analysis");
                return;
            }
            PsiImportList importList = psiJavaFile.getImportList();

            if (importList != null) {
                PsiImportStatement[] imports = importList.getImportStatements();

                result.updateStatistics("imports", imports.length);
                debugLog("Found " + imports.length + " imports");

                // 收集详细的导入信息，包含行号
                List<ImportInfo> importDetails = new ArrayList<>();
                for (PsiImportStatement importStatement : imports) {
                    try {
                        String importText = importStatement.getText();

                        if (importText != null && !importText.trim().isEmpty()) {
                            // 获取导入语句的行号
                            int lineNumber = getSafeLineNumber(importStatement);
                            ImportInfo importInfo = analyzeImportStatement(importStatement, importText.trim(), lineNumber);
                            importDetails.add(importInfo);
                            debugLog("Import: " + importText.trim() + " at line " + lineNumber + " type: " + importInfo.getType());
                        }
                    } catch (Exception ex) {
                        debugLog("Error getting import text: " + ex.getMessage());
                    }
                }

                // 将导入详情存储到结果中
                if (!importDetails.isEmpty()) {
                    for (ImportInfo importInfo : importDetails) {
                        result.addImport(importInfo);
                    }
                    debugLog("Collected " + importDetails.size() + " import details");
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing imports: " + e.getMessage());
        }
    }

    /**
     * 分析所有类
     */
    private void analyzeClasses(PsiFile javaFile, AnalysisResult result, String moduleName, String filePath) {
        try {
            if (!(javaFile instanceof PsiJavaFile psiJavaFile)) {
                debugLog("File is not a PsiJavaFile, skipping class analysis");
                return;
            }

            PsiClass[] classes = psiJavaFile.getClasses();
            debugLog("Found " + classes.length + " top-level classes");

            // 获取包名
            String packageName = psiJavaFile.getPackageName();

            for (PsiClass cls : classes) {
                analyzeClass(cls, packageName, result, moduleName, filePath);
            }
        } catch (Exception e) {
            debugLog("Error analyzing classes: " + e.getMessage());
        }
    }

    /**
     * 分析单个类
     */
    private void analyzeClass(PsiClass cls, String packageName, AnalysisResult result, String moduleName, String filePath) {
        try {
            // 创建类节点
            String classId = generateClassId(cls, packageName);
            AnalysisNode.NodeType nodeType = determineClassType(cls);

            AnalysisNode classNode = new AnalysisNode(
                    classId,
                    nodeType,
                    cls.getName(),
                    cls.getName(),
                    packageName,
                    getSafeLineNumber(cls),
                    generateClassSignature(cls, packageName)
            );

            // 设置模块名和文件路径
            classNode.setModuleName(moduleName);
            classNode.setFilePath(filePath);

            result.addNode(classNode);
            result.updateStatistics("classes", result.getStatistics().getOrDefault("classes", 0) + 1);
            debugLog("Added class: " + classId + " in module: " + moduleName + " at path: " + filePath);

            // 分析类的成员
            analyzeFields(cls, packageName, result, moduleName, filePath);
            analyzeMethods(cls, packageName, result, moduleName, filePath);

            // 递归分析内部类
            for (PsiClass innerClass : cls.getInnerClasses()) {
                analyzeClass(innerClass, packageName, result, moduleName, filePath);
            }

        } catch (Exception e) {
            debugLog("Error analyzing class " + cls.getName() + ": " + e.getMessage());
        }
    }

    /**
     * 分析字段
     */
    private void analyzeFields(PsiClass cls, String packageName, AnalysisResult result, String moduleName, String filePath) {
        for (PsiField field : cls.getFields()) {
            try {
                String fieldId = generateFieldId(field, cls, packageName);

                AnalysisNode fieldNode = new AnalysisNode(
                        fieldId,
                        AnalysisNode.NodeType.FIELD,
                        field.getName(),
                        cls.getName(),
                        packageName,
                        getSafeLineNumber(field),
                        generateFieldSignature(field, cls, packageName)
                );

                // 设置模块名和文件路径
                fieldNode.setModuleName(moduleName);
                fieldNode.setFilePath(filePath);

                result.addNode(fieldNode);
                result.updateStatistics("fields", result.getStatistics().getOrDefault("fields", 0) + 1);
                debugLog("Added field: " + fieldId + " in module: " + moduleName + " at path: " + filePath);

            } catch (Exception e) {
                debugLog("Error analyzing field " + field.getName() + ": " + e.getMessage());
            }
        }
    }

    /**
     * 分析方法
     */
    private void analyzeMethods(PsiClass cls, String packageName, AnalysisResult result, String moduleName, String filePath) {
        for (PsiMethod method : cls.getMethods()) {
            try {
                String methodId = generateMethodId(method, cls, packageName);

                AnalysisNode methodNode = new AnalysisNode(
                        methodId,
                        AnalysisNode.NodeType.METHOD,
                        method.getName(),
                        cls.getName(),
                        packageName,
                        getSafeLineNumber(method),
                        generateMethodSignature(method, cls, packageName)
                );

                // 设置模块名和文件路径
                methodNode.setModuleName(moduleName);
                methodNode.setFilePath(filePath);

                result.addNode(methodNode);
                result.updateStatistics("methods", result.getStatistics().getOrDefault("methods", 0) + 1);
                debugLog("Added method: " + methodId + " in module: " + moduleName + " at path: " + filePath);

            } catch (Exception e) {
                debugLog("Error analyzing method " + method.getName() + ": " + e.getMessage());
            }
        }
    }

    /**
     * 分析调用关系
     */
    private void analyzeCallRelations(PsiFile javaFile, AnalysisResult result) {
        debugLog("Starting call relation analysis");

        // 查找所有方法调用
        Collection<PsiMethodCallExpression> methodCalls =
                PsiTreeUtil.findChildrenOfType(javaFile, PsiMethodCallExpression.class);

        debugLog("Found " + methodCalls.size() + " method calls");

        // 使用Map聚合调用关系，避免重复
        Map<String, CallRelation> aggregatedCalls = new HashMap<>();

        for (PsiMethodCallExpression call : methodCalls) {
            analyzeMethodCall(call, result, aggregatedCalls);
        }

        // 将聚合后的调用关系添加到结果中
        for (CallRelation relation : aggregatedCalls.values()) {
            result.addCallRelation(relation);
            result.updateStatistics("method_calls", result.getStatistics().getOrDefault("method_calls", 0) + 1);
        }

        debugLog("Call relation analysis completed. Found " + aggregatedCalls.size() + " unique relations");
    }

    /**
     * 分析单个方法调用
     */
    private void analyzeMethodCall(PsiMethodCallExpression call, AnalysisResult result,
                                   Map<String, CallRelation> aggregatedCalls) {
        try {
            debugLog("Analyzing call: " + call.getText());

            // 查找调用者方法
            PsiMethod callerMethod = PsiTreeUtil.getParentOfType(call, PsiMethod.class);
            if (callerMethod == null) {
                debugLog("No caller method found for: " + call.getText());
                return;
            }

            PsiClass callerClass = callerMethod.getContainingClass();
            if (callerClass == null) {
                debugLog("No caller class found for: " + call.getText());
                return;
            }

            // 生成调用者ID
            String packageName = getPackageName(callerClass);
            String callerId = generateMethodId(callerMethod, callerClass, packageName);

            // 查找调用者节点
            AnalysisNode caller = result.getNodes().get(callerId);
            if (caller == null) {
                debugLog("Caller node not found: " + callerId);
                return;
            }

            // 解析被调用方法
            AnalysisNode callee = resolveCallee(call, result, packageName);
            if (callee == null) {
                debugLog("Could not resolve callee for: " + call.getText());
                return;
            }

            // 判断是否为外部调用
            boolean isExternal = isExternalCall(callerClass, callee);

            // 创建或更新调用关系
            String aggregationKey = callerId + " -> " + callee.getId();
            int lineNumber = getSafeLineNumber(call);
            String expression = call.getText();

            CallRelation existingRelation = aggregatedCalls.get(aggregationKey);
            if (existingRelation == null) {
                // 创建新的调用关系
                List<CallRelation.CallInstance> instances = new ArrayList<>();
                instances.add(new CallRelation.CallInstance(lineNumber, expression));
                CallRelation newRelation = new CallRelation(caller, callee, lineNumber, expression, isExternal, instances);
                aggregatedCalls.put(aggregationKey, newRelation);
            } else {
                // 更新现有调用关系，添加新的调用实例
                List<CallRelation.CallInstance> instances = new ArrayList<>(existingRelation.getAllCallInstances());
                instances.add(new CallRelation.CallInstance(lineNumber, expression));
                CallRelation updatedRelation = new CallRelation(
                        existingRelation.getCaller(),
                        existingRelation.getCallee(),
                        existingRelation.getCallLineNumber(),
                        existingRelation.getCallExpression(),
                        existingRelation.isExternal(),
                        instances
                );
                aggregatedCalls.put(aggregationKey, updatedRelation);
            }

            debugLog("Added call relation: " + caller.getSignature() + " -> " + callee.getSignature());

        } catch (Exception e) {
            debugLog("Error analyzing call " + call.getText() + ": " + e.getMessage());
        }
    }

    /**
     * 解析被调用方法 - 激进版本，包含 jar 包和文件路径信息
     */
    private AnalysisNode resolveCallee(PsiMethodCallExpression call, AnalysisResult result, String currentPackage) {
        try {
            // 方法1：标准PSI解析
            PsiMethod resolvedMethod = call.resolveMethod();
            if (resolvedMethod != null) {
                PsiClass calleeClass = resolvedMethod.getContainingClass();
                if (calleeClass != null) {
                    String calleePackage = getPackageName(calleeClass);
                    String calleeId = generateMethodId(resolvedMethod, calleeClass, calleePackage);

                    // 检查是否为已知的内部方法（只返回当前文件中已分析的方法）
                    AnalysisNode existingCallee = result.getNodes().get(calleeId);
                    if (existingCallee != null) {
                        return existingCallee;
                    }

                    // 激进解析外部方法的详细信息
                    ExternalMethodInfo externalInfo = resolveExternalMethodInfo(resolvedMethod, calleeClass);

                    // 对于外部方法，创建包含详细信息的临时节点
                    AnalysisNode externalCallee = new AnalysisNode(
                            calleeId,
                            AnalysisNode.NodeType.METHOD,
                            resolvedMethod.getName(),
                            calleeClass.getName(),
                            calleePackage,
                            getSafeLineNumber(resolvedMethod),
                            generateMethodSignature(resolvedMethod, calleeClass, calleePackage)
                    );

                    // 设置模块信息和文件路径
                    externalCallee.setModuleName(externalInfo.jarInfo);
                    externalCallee.setFilePath(externalInfo.classPath);

                    debugLog("Resolved external method: " + calleeId +
                            " from " + externalInfo.jarInfo +
                            " at " + externalInfo.classPath);
                    return externalCallee;
                }
            }

            // 方法2：基于方法名的模糊匹配（用于PSI解析失败的情况）
            PsiReferenceExpression methodExpression = call.getMethodExpression();
            String methodName = methodExpression.getReferenceName();

            if (methodName != null) {
                // 尝试在当前文件中查找同名方法
                for (AnalysisNode node : result.getNodes().values()) {
                    if (node.getType() == AnalysisNode.NodeType.METHOD &&
                            node.getName().equals(methodName) &&
                            node.getPackageName().equals(currentPackage)) {
                        debugLog("Found method by name matching: " + node.getSignature());
                        return node;
                    }
                }

                // 尝试激进解析未直接解析的方法调用
                AnalysisNode aggressiveCallee = aggressiveResolveMethod(call, methodName, currentPackage);
                if (aggressiveCallee != null) {
                    return aggressiveCallee;
                }

                // 创建未解析的方法节点（临时节点，不添加到结果中）
                String unknownId = "UNKNOWN." + methodName + "()";
                AnalysisNode unknownCallee = new AnalysisNode(
                        unknownId,
                        AnalysisNode.NodeType.METHOD,
                        methodName,
                        "UNKNOWN",
                        "UNKNOWN",
                        0,
                        unknownId
                );
                unknownCallee.setModuleName("UNKNOWN_JAR");
                unknownCallee.setFilePath("UNKNOWN_CLASS");
                debugLog("Created unknown method node: " + unknownId);
                return unknownCallee;
            }

        } catch (Exception e) {
            debugLog("Error resolving callee: " + e.getMessage());
        }

        return null;
    }

    /**
     * 判断是否为外部调用
     */
    private boolean isExternalCall(PsiClass callerClass, AnalysisNode callee) {
        try {
            // 如果被调用方法是未知的，肯定是外部调用
            if ("UNKNOWN".equals(callee.getPackageName())) {
                return true;
            }

            // 获取调用者所在的文件
            PsiFile callerFile = callerClass.getContainingFile();
            if (callerFile == null) {
                return true;
            }

            // 检查被调用方法是否在同一个文件中
            String calleePackage = callee.getPackageName();
            if (isStandardLibraryPackage(calleePackage)) {
                return true; // Java标准库调用
            }

            // 检查被调用方法的类是否能在当前工程中找到
            return !isInCurrentProject(callerFile, calleePackage, callee.getClassName());

        } catch (Exception e) {
            debugLog("Error determining external call: " + e.getMessage());
            return true;
        }
    }

    private ExternalMethodInfo resolveExternalMethodInfo(PsiMethod method, PsiClass containingClass) {
        try {
            // 获取包含类的文件
            PsiFile containingFile = containingClass.getContainingFile();
            if (containingFile == null) {
                return new ExternalMethodInfo("NO_FILE", "NO_CLASS_PATH");
            }

            VirtualFile virtualFile = containingFile.getVirtualFile();
            if (virtualFile == null) {
                return new ExternalMethodInfo("NO_VIRTUAL_FILE", "NO_CLASS_PATH");
            }

            String filePath = virtualFile.getPath();
            debugLog("Resolving external method info for: " + method.getName() + " in file: " + filePath);

            // 判断是否来自 jar 包
            if (filePath.contains(".jar!") || filePath.contains(".jar/")) {
                return resolveJarMethodInfo(filePath, containingClass);
            }

            // 判断是否来自编译后的 class 文件
            if (filePath.endsWith(".class")) {
                return resolveClassFileInfo(filePath, containingClass);
            }

            // 判断是否来自源码文件
            if (filePath.endsWith(".java")) {
                return resolveSourceFileInfo(filePath, containingClass);
            }

            // 其他情况
            return new ExternalMethodInfo("UNKNOWN_SOURCE", filePath);

        } catch (Exception e) {
            debugLog("Error resolving external method info: " + e.getMessage());
            return new ExternalMethodInfo("ERROR_RESOLVING", "ERROR_PATH");
        }
    }

    /**
     * 解析来自 jar 包的方法信息
     */
    private ExternalMethodInfo resolveJarMethodInfo(String filePath, PsiClass containingClass) {
        try {
            // 提取 jar 包路径和内部类路径
            String jarPath = "";
            String classPath = "";

            if (filePath.contains(".jar!")) {
                String[] parts = filePath.split("\\.jar!");
                if (parts.length >= 2) {
                    jarPath = parts[0] + ".jar";
                    classPath = parts[1];
                }
            } else if (filePath.contains(".jar/")) {
                String[] parts = filePath.split("\\.jar/");
                if (parts.length >= 2) {
                    jarPath = parts[0] + ".jar";
                    classPath = parts[1];
                }
            }

            // 提取 jar 包名称
            String jarName = extractJarName(jarPath);

            // 构建完整的类路径
            String fullClassPath = classPath;
            if (!classPath.startsWith("/")) {
                fullClassPath = "/" + classPath;
            }

            debugLog("Resolved jar method: jar=" + jarName + ", classPath=" + fullClassPath);
            return new ExternalMethodInfo(jarName, fullClassPath);

        } catch (Exception e) {
            debugLog("Error resolving jar method info: " + e.getMessage());
            return new ExternalMethodInfo("JAR_PARSE_ERROR", filePath);
        }
    }

    /**
     * 解析来自 class 文件的方法信息
     */
    private ExternalMethodInfo resolveClassFileInfo(String filePath, PsiClass containingClass) {
        try {
            // 尝试从路径中提取模块信息
            String moduleName = extractModuleFromPath(filePath);

            // 构建类路径
            String className = containingClass.getQualifiedName();
            if (className == null) {
                className = containingClass.getName();
            }

            String classPath = className != null ? className.replace(".", "/") + ".class" : "UNKNOWN.class";

            debugLog("Resolved class file method: module=" + moduleName + ", classPath=" + classPath);
            return new ExternalMethodInfo(moduleName, classPath);

        } catch (Exception e) {
            debugLog("Error resolving class file info: " + e.getMessage());
            return new ExternalMethodInfo("CLASS_PARSE_ERROR", filePath);
        }
    }

    /**
     * 解析来自源码文件的方法信息
     */
    private ExternalMethodInfo resolveSourceFileInfo(String filePath, PsiClass containingClass) {
        try {
            // 尝试从路径中提取模块信息
            String moduleName = extractModuleFromPath(filePath);

            // 使用相对路径作为类路径
            String relativePath = extractRelativePath(filePath);

            debugLog("Resolved source file method: module=" + moduleName + ", path=" + relativePath);
            return new ExternalMethodInfo(moduleName, relativePath);

        } catch (Exception e) {
            debugLog("Error resolving source file info: " + e.getMessage());
            return new ExternalMethodInfo("SOURCE_PARSE_ERROR", filePath);
        }
    }

    /**
     * 从文件路径中提取 jar 包名称
     */
    private String extractJarName(String jarPath) {
        try {
            if (jarPath == null || jarPath.isEmpty()) {
                return "UNKNOWN_JAR";
            }

            // 获取文件名
            int lastSlash = Math.max(jarPath.lastIndexOf('/'), jarPath.lastIndexOf('\\'));
            String fileName = lastSlash >= 0 ? jarPath.substring(lastSlash + 1) : jarPath;

            // 保留完整的 .jar 名称，不移除扩展名
            return fileName;

        } catch (Exception e) {
            debugLog("Error extracting jar name: " + e.getMessage());
            return "JAR_NAME_ERROR";
        }
    }

    /**
     * 从文件路径中提取模块名称
     */
    private String extractModuleFromPath(String filePath) {
        try {
            if (filePath == null || filePath.isEmpty()) {
                return "UNKNOWN_MODULE";
            }

            // 查找常见的模块标识符
            String[] moduleIndicators = {"/target/classes/", "/build/classes/", "/out/production/", "/bin/"};

            for (String indicator : moduleIndicators) {
                int index = filePath.indexOf(indicator);
                if (index > 0) {
                    String beforeIndicator = filePath.substring(0, index);
                    int lastSlash = Math.max(beforeIndicator.lastIndexOf('/'), beforeIndicator.lastIndexOf('\\'));
                    if (lastSlash >= 0) {
                        return beforeIndicator.substring(lastSlash + 1);
                    }
                }
            }

            // 如果没有找到标准模块结构，尝试从路径中提取可能的模块名
            String[] pathParts = filePath.split("[/\\\\]");
            for (int i = pathParts.length - 1; i >= 0; i--) {
                String part = pathParts[i];
                if (part.contains("-") || part.matches(".*\\d+.*")) {
                    return part;
                }
            }

            return "EXTRACTED_MODULE";

        } catch (Exception e) {
            debugLog("Error extracting module from path: " + e.getMessage());
            return "MODULE_EXTRACT_ERROR";
        }
    }

    /**
     * 提取相对路径
     */
    private String extractRelativePath(String fullPath) {
        try {
            if (fullPath == null || fullPath.isEmpty()) {
                return "UNKNOWN_PATH";
            }

            // 查找 src/main/java 或类似的源码路径
            String[] sourceIndicators = {"src/main/java/", "src/test/java/", "src/java/", "java/"};

            for (String indicator : sourceIndicators) {
                int index = fullPath.indexOf(indicator);
                if (index >= 0) {
                    return fullPath.substring(index + indicator.length());
                }
            }

            // 如果没有找到标准源码路径，返回文件名
            int lastSlash = Math.max(fullPath.lastIndexOf('/'), fullPath.lastIndexOf('\\'));
            return lastSlash >= 0 ? fullPath.substring(lastSlash + 1) : fullPath;

        } catch (Exception e) {
            debugLog("Error extracting relative path: " + e.getMessage());
            return "PATH_EXTRACT_ERROR";
        }
    }

    /**
     * 激进解析方法调用 - 尝试通过多种方式解析未直接解析的方法
     */
    private AnalysisNode aggressiveResolveMethod(PsiMethodCallExpression call, String methodName, String currentPackage) {
        try {
            debugLog("Attempting aggressive method resolution for: " + methodName);

            // 方法1：尝试通过限定符解析
            PsiReferenceExpression methodExpression = call.getMethodExpression();
            PsiExpression qualifierExpression = methodExpression.getQualifierExpression();

            if (qualifierExpression != null) {
                // 尝试解析限定符的类型
                PsiType qualifierType = qualifierExpression.getType();
                if (qualifierType != null) {
                    String qualifierTypeName = qualifierType.getPresentableText();
                    debugLog("Found qualifier type: " + qualifierTypeName);

                    // 创建基于限定符类型的方法节点
                    String aggressiveId = qualifierTypeName + "." + methodName + "()";
                    AnalysisNode aggressiveNode = new AnalysisNode(
                            aggressiveId,
                            AnalysisNode.NodeType.METHOD,
                            methodName,
                            qualifierTypeName,
                            extractPackageFromType(qualifierTypeName),
                            getSafeLineNumber(call),
                            aggressiveId
                    );
                    aggressiveNode.setModuleName("AGGRESSIVE_RESOLVE");
                    aggressiveNode.setFilePath("TYPE_BASED_RESOLUTION");
                    debugLog("Created aggressive method node: " + aggressiveId);
                    return aggressiveNode;
                }
            }

            // 方法2：尝试通过常见模式推断
            AnalysisNode patternBasedNode = resolveMethodByPattern(methodName, call);
            if (patternBasedNode != null) {
                return patternBasedNode;
            }

        } catch (Exception e) {
            debugLog("Error in aggressive method resolution: " + e.getMessage());
        }

        return null;
    }

    /**
     * 从类型名称中提取包名
     */
    private String extractPackageFromType(String typeName) {
        if (typeName == null || !typeName.contains(".")) {
            return "UNKNOWN_PACKAGE";
        }

        int lastDot = typeName.lastIndexOf('.');
        return typeName.substring(0, lastDot);
    }

    /**
     * 通过常见模式解析方法
     */
    private AnalysisNode resolveMethodByPattern(String methodName, PsiMethodCallExpression call) {
        try {
            // 基于方法名的常见模式推断
            String inferredClass = inferClassFromMethodName(methodName);
            if (inferredClass != null) {
                String patternId = inferredClass + "." + methodName + "()";
                AnalysisNode patternNode = new AnalysisNode(
                        patternId,
                        AnalysisNode.NodeType.METHOD,
                        methodName,
                        inferredClass,
                        "INFERRED_PACKAGE",
                        getSafeLineNumber(call),
                        patternId
                );
                patternNode.setModuleName("PATTERN_RESOLVE");
                patternNode.setFilePath("PATTERN_BASED_RESOLUTION");
                debugLog("Created pattern-based method node: " + patternId);
                return patternNode;
            }
        } catch (Exception e) {
            debugLog("Error in pattern-based resolution: " + e.getMessage());
        }

        return null;
    }

    /**
     * 根据方法名推断可能的类名
     */
    private String inferClassFromMethodName(String methodName) {
        // 常见的方法名模式推断
        if (methodName.startsWith("get") || methodName.startsWith("set")) {
            return "BEAN_CLASS";
        }
        if (methodName.startsWith("is") || methodName.startsWith("has")) {
            return "BOOLEAN_PROVIDER";
        }
        if ("toString".equals(methodName) || "equals".equals(methodName) || "hashCode".equals(methodName)) {
            return "Object";
        }
        if (methodName.startsWith("create") || methodName.startsWith("build")) {
            return "FACTORY_CLASS";
        }

        return null;
    }

    /**
     * 检查是否为Java标准库或常见第三方库的包
     */
    private boolean isStandardLibraryPackage(String packageName) {
        if (packageName == null || packageName.isEmpty()) {
            return true;
        }

        // Java标准库
        if (packageName.startsWith("java.") ||
                packageName.startsWith("javax.") ||
                packageName.startsWith("sun.") ||
                packageName.startsWith("com.sun.") ||
                packageName.startsWith("jdk.")) {
            return true;
        }

        // 常见的第三方库
        String[] commonLibraries = {
                "org.apache.", "org.springframework.", "com.google.",
                "org.junit.", "org.slf4j.", "ch.qos.logback.",
                "org.hibernate.", "com.fasterxml.jackson.",
                "org.jetbrains.", "kotlin.", "scala."
        };

        for (String libPrefix : commonLibraries) {
            if (packageName.startsWith(libPrefix)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查指定的类是否在当前工程中
     */
    private boolean isInCurrentProject(PsiFile callerFile, String targetPackage, String targetClassName) {
        try {
            Project project = callerFile.getProject();

            GlobalSearchScope projectScope = GlobalSearchScope.projectScope(project);
            JavaPsiFacade psiFacade = JavaPsiFacade.getInstance(project);

            String fullClassName = targetPackage.isEmpty() ? targetClassName :
                    targetPackage + "." + targetClassName;

            PsiClass targetClass = psiFacade.findClass(fullClassName, projectScope);

            if (targetClass != null) {
                PsiFile targetFile = targetClass.getContainingFile();
                if (targetFile != null) {
                    VirtualFile virtualFile = targetFile.getVirtualFile();
                    if (virtualFile != null) {
                        ProjectFileIndex fileIndex = ProjectFileIndex.getInstance(project);

                        return fileIndex.isInSourceContent(virtualFile) &&
                                !fileIndex.isInLibrarySource(virtualFile);
                    }
                }
            }

            return false;

        } catch (Exception e) {
            debugLog("Error checking if class is in current project: " + e.getMessage());
            return false;
        }
    }

    // ==================== 辅助方法 ====================

    private String generateClassId(PsiClass cls, String packageName) {
        return packageName + "." + cls.getName();
    }

    private String generateMethodId(PsiMethod method, PsiClass cls, String packageName) {
        return generateClassId(cls, packageName) + "." + method.getName() + "(" +
                getParameterTypes(method) + ")";
    }

    private String generateFieldId(PsiField field, PsiClass cls, String packageName) {
        return generateClassId(cls, packageName) + "." + field.getName();
    }

    private String generateClassSignature(PsiClass cls, String packageName) {
        return packageName + "." + cls.getName();
    }

    private String generateMethodSignature(PsiMethod method, PsiClass cls, String packageName) {
        StringBuilder signature = new StringBuilder();
        signature.append(generateClassId(cls, packageName))
                .append(".")
                .append(method.getName())
                .append("(")
                .append(getParameterTypes(method))
                .append(")");

        PsiType returnType = method.getReturnType();
        if (returnType != null) {
            signature.append(":").append(returnType.getPresentableText());
        }

        return signature.toString();
    }

    private String generateFieldSignature(PsiField field, PsiClass cls, String packageName) {
        return generateClassId(cls, packageName) + "." + field.getName() +
                ":" + field.getType().getPresentableText();
    }

    private String getParameterTypes(PsiMethod method) {
        PsiParameter[] parameters = method.getParameterList().getParameters();
        if (parameters.length == 0) {
            return "";
        }

        StringBuilder types = new StringBuilder();
        for (int i = 0; i < parameters.length; i++) {
            if (i > 0) {
                types.append(",");
            }
            types.append(parameters[i].getType().getPresentableText());
        }
        return types.toString();
    }

    private AnalysisNode.NodeType determineClassType(PsiClass cls) {
        if (cls.isInterface()) {
            return AnalysisNode.NodeType.INTERFACE;
        }
        if (cls.isEnum()) {
            return AnalysisNode.NodeType.ENUM;
        }
        return AnalysisNode.NodeType.CLASS;
    }

    /**
     * 从文件中获取包名（兼容方法）
     */
    private String getPackageNameFromFile(PsiFile file) {
        try {
            // 方法1：使用反射调用PsiJavaFile.getPackageName()
            try {
                if (file instanceof PsiJavaFile javaFile) {
                    return javaFile.getPackageName();
                }
            } catch (Exception e) {
                debugLog("Direct method failed: " + e.getMessage());
            }

            // 方法2：从文件内容中解析包名
            return JavaAnalyzerUtils.parsePackageFromContent(file);
        } catch (Exception e) {
            debugLog("Error getting package name: " + e.getMessage());
            return "";
        }
    }

    private String getPackageName(PsiClass cls) {
        PsiFile file = cls.getContainingFile();
        return getPackageNameFromFile(file);
    }


    /**
     * 分析导入语句，创建ImportInfo对象
     */
    private ImportInfo analyzeImportStatement(PsiImportStatement importStatement, String importText, int lineNumber) {
        try {
            // 获取文件路径
            String filePath = JavaAnalyzerUtils.getFilePath(importStatement.getContainingFile());

            // 判断导入类型
            ImportInfo.ImportType type = determineImportType(importText);

            // 判断是否为外部导入 - 使用与 CallRelation 相同的逻辑
            boolean isExternal = isExternalImport(importStatement, importText);

            // 解析导入的类
            List<String> resolvedClasses = new ArrayList<>();
            if (type == ImportInfo.ImportType.WILDCARD || type == ImportInfo.ImportType.STATIC_WILDCARD) {
                // 对于通配符导入，解析具体的类
                resolvedClasses = resolveWildcardImport(importStatement, importText);
            } else if (type == ImportInfo.ImportType.SINGLE || type == ImportInfo.ImportType.STATIC) {
                // 对于单行导入，提取导入的类名
                String importedClass = extractSingleImportClass(importText);
                if (importedClass != null && !importedClass.isEmpty()) {
                    resolvedClasses.add(importedClass);
                }
            }

            // 解析被导入的目标文件路径
            String targetFilePath = resolveTargetFilePath(importStatement, importText, isExternal);

            debugLog("Import analysis: " + importText + " -> External: " + isExternal + ", Type: " + type + ", File: " + filePath + ", Target: " + targetFilePath);
            return new ImportInfo(importText, lineNumber, type, isExternal, resolvedClasses, filePath, targetFilePath);

        } catch (Exception e) {
            debugLog("Error analyzing import statement: " + e.getMessage());
            String filePath = JavaAnalyzerUtils.getFilePath(importStatement.getContainingFile());
            return new ImportInfo(importText, lineNumber, ImportInfo.ImportType.SINGLE, true, new ArrayList<>(), filePath, null);
        }
    }

    /**
     * 解析被导入文件的目标路径
     */
    private String resolveTargetFilePath(PsiImportStatement importStatement, String importText, boolean isExternal) {
        try {
            // 如果是外部导入，不解析具体文件路径
            if (isExternal) {
                return null;
            }

            // 解析导入的类名
            PsiElement resolved = importStatement.resolve();
            if (resolved instanceof PsiClass) {
                PsiClass psiClass = (PsiClass) resolved;
                PsiFile containingFile = psiClass.getContainingFile();
                if (containingFile != null) {
                    return JavaAnalyzerUtils.getFilePath(containingFile);
                }
            }

            // 如果无法通过PSI解析，尝试通过包名推断路径
            String packageName = extractPackageFromImport(importText);
            if (packageName != null && !packageName.isEmpty()) {
                // 将包名转换为文件路径格式
                String relativePath = packageName.replace('.', '/') + ".java";

                // 检查路径是否在项目中存在
                Project project = importStatement.getProject();
                VirtualFile[] sourceRoots = ProjectRootManager.getInstance(project).getContentSourceRoots();
                for (VirtualFile sourceRoot : sourceRoots) {
                    VirtualFile targetFile = sourceRoot.findFileByRelativePath("src/main/java/" + relativePath);
                    if (targetFile != null) {
                        return getRelativePath(targetFile, project);
                    }
                    // 尝试其他常见的源码路径
                    targetFile = sourceRoot.findFileByRelativePath(relativePath);
                    if (targetFile != null) {
                        return getRelativePath(targetFile, project);
                    }
                }
            }

            return null;
        } catch (Exception e) {
            debugLog("Error resolving target file path: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取文件相对于项目的路径
     */
    private String getRelativePath(VirtualFile file, Project project) {
        VirtualFile projectDir = project.getBaseDir();
        if (projectDir != null) {
            return VfsUtilCore.getRelativePath(file, projectDir, '/');
        }
        return file.getPath();
    }

    /**
     * 判断导入是否为外部导入 - 使用与 CallRelation 相同的逻辑
     */
    private boolean isExternalImport(PsiImportStatement importStatement, String importText) {
        try {
            // 从导入语句中提取包名
            String packageName = extractPackageFromImport(importText);
            if (packageName == null || packageName.isEmpty()) {
                return true; // 无法解析的导入认为是外部的
            }

            // 检查是否为标准库或第三方库
            if (isStandardLibraryPackage(packageName)) {
                return true;
            }

            // 获取当前文件
            PsiFile currentFile = importStatement.getContainingFile();
            if (currentFile == null) {
                return true;
            }

            // 检查是否为当前项目中的类
            String className = extractClassNameFromPackage(packageName);
            return !isInCurrentProject(currentFile, packageName, className);

        } catch (Exception e) {
            debugLog("Error determining external import: " + e.getMessage());
            return true;
        }
    }

    /**
     * 从导入语句中提取包名
     */
    private String extractPackageFromImport(String importText) {
        // 移除 "import " 和 ";"
        String cleaned = importText.replace("import ", "").replace(";", "").trim();

        // 处理静态导入
        if (cleaned.startsWith("static ")) {
            cleaned = cleaned.substring(7);
        }

        // 对于静态导入，可能包含字段或方法名，需要提取包名部分
        if (importText.contains("import static")) {
            // 静态导入格式: import static com.example.Class.method
            // 提取包名部分: com.example
            int lastDot = cleaned.lastIndexOf('.');
            if (lastDot > 0) {
                String classPath = cleaned.substring(0, lastDot);
                int secondLastDot = classPath.lastIndexOf('.');
                if (secondLastDot > 0) {
                    return classPath.substring(0, secondLastDot);
                }
                return classPath;
            }
        } else {
            // 普通导入: import com.example.Class 或 import com.example.*
            if (cleaned.endsWith(".*")) {
                cleaned = cleaned.substring(0, cleaned.length() - 2);
            }
            // 提取包名
            int lastDot = cleaned.lastIndexOf('.');
            if (lastDot > 0) {
                return cleaned.substring(0, lastDot);
            }
        }

        return cleaned;
    }

    /**
     * 从包路径中提取类名
     */
    private String extractClassNameFromPackage(String packagePath) {
        if (packagePath == null || packagePath.isEmpty()) {
            return "";
        }
        int lastDot = packagePath.lastIndexOf('.');
        if (lastDot >= 0 && lastDot < packagePath.length() - 1) {
            return packagePath.substring(lastDot + 1);
        }
        return packagePath;
    }

    /**
     * 判断导入类型
     */
    private ImportInfo.ImportType determineImportType(String importText) {
        if (importText.contains("import static") && importText.endsWith("*;")) {
            return ImportInfo.ImportType.STATIC_WILDCARD;
        } else if (importText.contains("import static")) {
            return ImportInfo.ImportType.STATIC;
        } else if (importText.endsWith("*;")) {
            return ImportInfo.ImportType.WILDCARD;
        } else {
            return ImportInfo.ImportType.SINGLE;
        }
    }

    /**
     * 解析通配符导入的具体类
     */
    private List<String> resolveWildcardImport(PsiImportStatement importStatement, String importText) {
        List<String> resolvedClasses = new ArrayList<>();

        try {
            // 提取包名
            String packageName = extractPackageFromWildcard(importText);
            debugLog("Resolving wildcard import for package: " + packageName);

            // 尝试通过PSI解析获取包中的类
            resolvedClasses = findClassesInPackageViaPSI(importStatement, packageName);

            // 如果PSI解析失败，使用预定义的常见类
            if (resolvedClasses.isEmpty()) {
                resolvedClasses = getCommonClassesForPackage(packageName);
            }

            debugLog("Resolved " + resolvedClasses.size() + " classes for wildcard import: " + packageName);

        } catch (Exception e) {
            debugLog("Error resolving wildcard import: " + e.getMessage());
            // 回退方案：从包名推断常见类
            String packageName = extractPackageFromWildcard(importText);
            resolvedClasses = getCommonClassesForPackage(packageName);
        }

        return resolvedClasses;
    }

    /**
     * 从通配符导入语句中提取包名
     */
    private String extractPackageFromWildcard(String importText) {
        // 移除 "import " 和 ";"
        String cleaned = importText.replace("import ", "").replace(";", "").trim();

        // 处理静态导入
        if (cleaned.startsWith("static ")) {
            cleaned = cleaned.substring(7);
        }

        // 移除 ".*"
        if (cleaned.endsWith(".*")) {
            cleaned = cleaned.substring(0, cleaned.length() - 2);
        }

        return cleaned;
    }

    /**
     * 通过PSI查找包中的类
     */
    private List<String> findClassesInPackageViaPSI(PsiElement element, String packageName) {
        List<String> classes = new ArrayList<>();

        try {
            Project project = element.getProject();

            JavaPsiFacade psiFacade = JavaPsiFacade.getInstance(project);
            GlobalSearchScope scope = GlobalSearchScope.allScope(project);

            // 查找包
            PsiPackage psiPackage = psiFacade.findPackage(packageName);
            if (psiPackage != null) {
                // 获取包中的所有类
                PsiClass[] psiClasses = psiPackage.getClasses(scope);
                for (PsiClass psiClass : psiClasses) {
                    if (psiClass.getName() != null) {
                        classes.add(packageName + "." + psiClass.getName());
                    }
                }
            }

        } catch (Exception e) {
            debugLog("Error finding classes via PSI: " + e.getMessage());
        }

        return classes;
    }

    /**
     * 获取常见包的预定义类列表
     */
    private List<String> getCommonClassesForPackage(String packageName) {
        List<String> classes = new ArrayList<>();

        switch (packageName) {
            case "java.util":
                classes.addAll(Arrays.asList(
                        "java.util.List", "java.util.ArrayList", "java.util.LinkedList",
                        "java.util.Map", "java.util.HashMap", "java.util.LinkedHashMap", "java.util.TreeMap",
                        "java.util.Set", "java.util.HashSet", "java.util.LinkedHashSet", "java.util.TreeSet",
                        "java.util.Collection", "java.util.Collections", "java.util.Iterator",
                        "java.util.Date", "java.util.Calendar", "java.util.Optional"
                ));
                break;
            case "java.io":
                classes.addAll(Arrays.asList(
                        "java.io.File", "java.io.FileInputStream", "java.io.FileOutputStream",
                        "java.io.BufferedReader", "java.io.BufferedWriter", "java.io.IOException",
                        "java.io.InputStream", "java.io.OutputStream", "java.io.Reader", "java.io.Writer"
                ));
                break;
            case "java.lang":
                classes.addAll(Arrays.asList(
                        "java.lang.String", "java.lang.Integer", "java.lang.Long", "java.lang.Double",
                        "java.lang.Boolean", "java.lang.Object", "java.lang.Class", "java.lang.Thread",
                        "java.lang.Exception", "java.lang.RuntimeException", "java.lang.StringBuilder"
                ));
                break;
            case "java.time":
                classes.addAll(Arrays.asList(
                        "java.time.LocalDate", "java.time.LocalTime", "java.time.LocalDateTime",
                        "java.time.ZonedDateTime", "java.time.Instant", "java.time.Duration", "java.time.Period"
                ));
                break;
            default:
                debugLog("No predefined classes for package: " + packageName);
                break;
        }

        return classes;
    }

    /**
     * 从单行导入语句中提取导入的类名
     */
    private String extractSingleImportClass(String importText) {
        try {
            // 移除 "import " 和 ";"
            String cleaned = importText.replace("import ", "").replace(";", "").trim();

            // 处理静态导入
            if (cleaned.startsWith("static ")) {
                cleaned = cleaned.substring(7);
            }

            // 对于静态导入，可能包含字段或方法名，需要提取类名部分
            if (importText.contains("import static")) {
                // 静态导入格式: import static com.example.Class.method
                // 只返回类名部分: com.example.Class
                int lastDot = cleaned.lastIndexOf('.');
                if (lastDot > 0) {
                    cleaned = cleaned.substring(0, lastDot);
                }
            }

            return cleaned;

        } catch (Exception e) {
            debugLog("Error extracting single import class: " + e.getMessage());
            return null;
        }
    }

    private void debugLog(String message) {
        if (DEBUG_MODE || FORCE_DEBUG) {
            System.out.println("[JavaASTAnalyzer] " + message);
        }
    }
}