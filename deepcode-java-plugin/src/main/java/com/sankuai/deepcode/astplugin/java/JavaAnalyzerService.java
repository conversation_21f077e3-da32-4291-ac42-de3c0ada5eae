package com.sankuai.deepcode.astplugin.java;

import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.sankuai.deepcode.astplugin.analyzer.AnalyzerService;

/**
 * Java分析器服务
 *
 * <AUTHOR>
 */
@Service
public final class JavaAnalyzerService {

    private static final Logger LOG = Logger.getInstance(JavaAnalyzerService.class);

    public JavaAnalyzerService() {
        try {
            LOG.info("Initializing Java Analyzer Service");

            // 注册Java分析器到共享服务
            JavaASTAnalyzer javaAnalyzer = new JavaASTAnalyzer();
            AnalyzerService.getInstance().registerAnalyzer(javaAnalyzer);

            LOG.info("Java Analyzer registered successfully: " + javaAnalyzer.getClass().getSimpleName());
        } catch (Exception e) {
            LOG.error("Failed to register Java analyzer", e);
        }
    }

    /**
     * 手动触发分析器注册，用于调试
     */
    public void ensureAnalyzerRegistered() {
        try {
            JavaASTAnalyzer javaAnalyzer = new JavaASTAnalyzer();
            AnalyzerService analyzerService = AnalyzerService.getInstance();

            LOG.info("Available analyzers: " + analyzerService.getAvailableAnalyzers().size());
            for (var analyzer : analyzerService.getAvailableAnalyzers()) {
                LOG.info("- " + analyzer.getClass().getSimpleName() + " supports: " + analyzer.getSupportedLanguage());
            }

            analyzerService.registerAnalyzer(javaAnalyzer);
            LOG.info("Java analyzer re-registered. Total analyzers: " + analyzerService.getAvailableAnalyzers().size());

        } catch (Exception e) {
            LOG.error("Failed to ensure analyzer registration", e);
        }
    }
}