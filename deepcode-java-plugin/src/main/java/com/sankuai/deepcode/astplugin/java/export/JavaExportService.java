package com.sankuai.deepcode.astplugin.java.export;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.vfs.VirtualFile;
import com.sankuai.deepcode.astplugin.export.ExportOption;
import com.sankuai.deepcode.astplugin.export.ExportService;
import org.jetbrains.annotations.NotNull;

import java.util.Arrays;
import java.util.List;

/**
 * Java 语言导出服务实现
 * 当前为空实现，保留架构以便后续扩展
 *
 * <AUTHOR>
 */
public class JavaExportService implements ExportService {

    private static final List<ExportOption> SUPPORTED_OPTIONS = Arrays.asList(
            ExportOption.STATISTICS_CSV,
            ExportOption.CALL_DETAILS
    );

    @Override
    public @NotNull String getLanguageName() {
        return "Java";
    }

    @Override
    public @NotNull List<ExportOption> getSupportedExportOptions() {
        return SUPPORTED_OPTIONS;
    }

    @Override
    public void exportProject(@NotNull Project project, @NotNull ExportOption exportOption, @NotNull VirtualFile outputDir) {
        // 当前为空实现，显示待开发提示
        ApplicationManager.getApplication().invokeLater(() -> {
            Messages.showInfoMessage(
                    "Java " + exportOption.getDisplayName() + " 功能正在开发中，敬请期待！\n\n" +
                    "如有需要，请联系开发团队。",
                    "功能开发中"
            );
        });
        
        // TODO: 实现Java项目的导出功能
        // 1. 扫描Java源文件
        // 2. 分析Java AST
        // 3. 生成导出报告
        // 4. 保存到指定目录
    }

    @Override
    public boolean hasLanguageFilesInProject(@NotNull Project project) {
        // TODO: 实现Java文件检测
        // 暂时返回false，避免在选择对话框中显示
        return false;
    }

    @Override
    public int getLanguageFileCount(@NotNull Project project) {
        // TODO: 实现Java文件计数
        return 0;
    }
}