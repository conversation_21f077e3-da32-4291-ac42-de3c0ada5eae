# Python 插件开发指南

本文档介绍 DeepCode Python Plugin 的开发环境搭建、架构设计和扩展方法。

## 🏗️ 架构设计

### 核心组件

```
deepcode-python-plugin/src/main/java/
└── com/sankuai/deepcode/astplugin/python/
    ├── PythonASTAnalyzer.java        # 主分析器
    ├── PythonNodeVisitor.java        # Python PSI 访问器
    ├── PythonCallAnalyzer.java       # 调用关系分析
    ├── PythonImportAnalyzer.java     # 导入分析
    ├── PythonTypeAnalyzer.java       # 类型注解分析  
    └── util/
        ├── PythonPsiUtils.java       # Python PSI 工具
        ├── PythonSignatureUtils.java # 签名工具
        └── PythonPackageDetector.java # 包检测
```

### 分析流程

```
PsiFile (Python)
    ↓
PythonASTAnalyzer
    ├── PythonNodeVisitor (遍历 Python PSI)
    ├── PythonCallAnalyzer (分析调用关系)
    ├── PythonImportAnalyzer (分析导入)
    ├── PythonTypeAnalyzer (分析类型注解)
    └── PythonPackageDetector (检测包结构)
    ↓
AnalysisResult (统一结果格式)
```

## 🛠️ 开发环境

### 环境要求

- Java 17+ (开发语言)
- PyCharm 2024.1.4+ 或 IntelliJ IDEA + Python 插件
- Gradle 7.6+ (构建工具)
- Python 3.8+ (用于测试)

### 项目配置

**build.gradle.kts**:
```kotlin
plugins {
    id("java")
    id("org.jetbrains.intellij") version "1.17.4"
}

dependencies {
    implementation(project(":shared-core"))
    implementation(project(":shared-ui"))
}

intellij {
    version.set("2024.1.4")
    type.set("PC")  // PyCharm Community
    plugins.set(listOf("PythonCore"))
}
```

### 开发启动

```bash
# 克隆项目
git clone <repository>
cd deepcode-ast-plugin

# 构建项目
./gradlew :deepcode-python-plugin:build

# 启动开发环境
./gradlew :deepcode-python-plugin:runIde

# 调试模式
./gradlew :deepcode-python-plugin:runIde --debug-jvm
```

## 🔍 核心实现

### PythonASTAnalyzer

主分析器负责协调整个Python分析过程：

```java
public class PythonASTAnalyzer implements ASTAnalyzer {
    
    @Override
    public boolean supports(PsiFile psiFile) {
        return psiFile instanceof PyFile;
    }
    
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        return ReadAction.compute(() -> {
            AnalysisResult result = new AnalysisResult();
            result.setFilePath(psiFile.getVirtualFile().getPath());
            result.setLanguage(Language.PYTHON);
            
            PyFile pyFile = (PyFile) psiFile;
            
            // 检测包结构
            String packageName = PythonPackageDetector.detectPackage(pyFile);
            
            // 分析文件结构
            PythonNodeVisitor visitor = new PythonNodeVisitor(result, packageName);
            pyFile.accept(visitor);
            
            // 分析调用关系
            PythonCallAnalyzer callAnalyzer = new PythonCallAnalyzer(result);
            callAnalyzer.analyzeCallRelations(pyFile);
            
            // 分析类型注解
            PythonTypeAnalyzer typeAnalyzer = new PythonTypeAnalyzer(result);
            typeAnalyzer.analyzeTypes(pyFile);
            
            return result;
        });
    }
}
```

### PythonNodeVisitor

Python PSI 访问器：

```java
public class PythonNodeVisitor extends PyElementVisitor {
    private final AnalysisResult result;
    private final String packageName;
    
    @Override
    public void visitPyClass(PyClass pyClass) {
        AnalysisNode classNode = createClassNode(pyClass);
        result.addNode(classNode);
        super.visitPyClass(pyClass);
    }
    
    @Override
    public void visitPyFunction(PyFunction pyFunction) {
        AnalysisNode functionNode = createFunctionNode(pyFunction);
        result.addNode(functionNode);
        super.visitPyFunction(pyFunction);
    }
    
    @Override
    public void visitPyTargetExpression(PyTargetExpression target) {
        // 分析变量定义
        if (isModuleLevelVariable(target)) {
            AnalysisNode variableNode = createVariableNode(target);
            result.addNode(variableNode);
        }
        super.visitPyTargetExpression(target);
    }
    
    private AnalysisNode createClassNode(PyClass pyClass) {
        AnalysisNode node = new AnalysisNode();
        node.setId(PythonPsiUtils.getFullyQualifiedName(pyClass));
        node.setType(NodeType.CLASS);
        node.setName(pyClass.getName());
        node.setPackageName(packageName);
        node.setLineNumber(PythonPsiUtils.getLineNumber(pyClass));
        
        // 处理装饰器
        List<String> decorators = PythonPsiUtils.getDecorators(pyClass);
        if (!decorators.isEmpty()) {
            node.addMetadata("decorators", decorators);
        }
        
        // 处理基类
        List<String> baseClasses = PythonPsiUtils.getBaseClasses(pyClass);
        if (!baseClasses.isEmpty()) {
            node.addMetadata("baseClasses", baseClasses);
        }
        
        node.setSignature(PythonSignatureUtils.getClassSignature(pyClass));
        node.setLanguage(Language.PYTHON);
        
        return node;
    }
}
```

### Python特有处理

#### 装饰器处理

```java
public class DecoratorAnalyzer {
    
    public static List<String> analyzeDecorators(PyDecoratable decoratable) {
        PyDecoratorList decoratorList = decoratable.getDecoratorList();
        if (decoratorList == null) return Collections.emptyList();
        
        List<String> decorators = new ArrayList<>();
        for (PyDecorator decorator : decoratorList.getDecorators()) {
            String decoratorName = getDecoratorName(decorator);
            if (decoratorName != null) {
                decorators.add(decoratorName);
            }
        }
        return decorators;
    }
    
    private static String getDecoratorName(PyDecorator decorator) {
        PyExpression callee = decorator.getCallee();
        if (callee instanceof PyReferenceExpression) {
            return ((PyReferenceExpression) callee).getName();
        } else if (callee instanceof PyCallExpression) {
            // 参数化装饰器，如 @cache(maxsize=128)
            PyExpression func = ((PyCallExpression) callee).getCallee();
            if (func instanceof PyReferenceExpression) {
                return ((PyReferenceExpression) func).getName();
            }
        }
        return callee != null ? callee.getText() : null;
    }
}
```

#### 类型注解分析

```java
public class PythonTypeAnalyzer {
    
    public void analyzeFunctionTypes(PyFunction function, AnalysisResult result) {
        // 分析参数类型
        for (PyParameter parameter : function.getParameterList().getParameters()) {
            PyTypeAnnotation annotation = parameter.getAnnotation();
            if (annotation != null) {
                String typeText = annotation.getText();
                // 记录类型信息
                recordTypeAnnotation(parameter.getName(), typeText, result);
            }
        }
        
        // 分析返回类型
        PyTypeAnnotation returnAnnotation = function.getReturnType();
        if (returnAnnotation != null) {
            String returnType = returnAnnotation.getText();
            recordReturnType(function.getName(), returnType, result);
        }
    }
    
    public void analyzeVariableTypes(PyTargetExpression target, AnalysisResult result) {
        PyTypeAnnotation annotation = target.getAnnotation();
        if (annotation != null) {
            String typeText = annotation.getText();
            recordVariableType(target.getName(), typeText, result);
        }
    }
}
```

### 调用关系分析

```java
public class PythonCallAnalyzer extends PyElementVisitor {
    
    @Override
    public void visitPyCallExpression(PyCallExpression callExpression) {
        PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
        if (containingFunction != null) {
            String callerId = PythonPsiUtils.getFunctionId(containingFunction);
            String calleeId = resolveCalleeId(callExpression);
            
            if (callerId != null && calleeId != null) {
                boolean isInternal = isInternalCall(callExpression);
                CallRelation relation = new CallRelation(callerId, calleeId, isInternal);
                
                CallInstance instance = new CallInstance();
                instance.setLineNumber(PythonPsiUtils.getLineNumber(callExpression));
                instance.setContext(callExpression.getText());
                relation.addInstance(instance);
                
                result.addCallRelation(relation);
            }
        }
        
        super.visitPyCallExpression(callExpression);
    }
    
    private boolean isInternalCall(PyCallExpression callExpression) {
        PyExpression callee = callExpression.getCallee();
        
        if (callee instanceof PyReferenceExpression) {
            PsiElement resolved = ((PyReferenceExpression) callee).resolve();
            
            if (resolved instanceof PyFunction || resolved instanceof PyClass) {
                // 检查是否在同一项目中
                return isInProject(resolved);
            }
        }
        
        // 检查是否为内置函数
        if (isBuiltinCall(callExpression)) {
            return false;
        }
        
        // 默认认为是外部调用
        return false;
    }
    
    private boolean isBuiltinCall(PyCallExpression callExpression) {
        String callText = callExpression.getCallee().getText();
        
        // Python 内置函数列表
        Set<String> builtins = Set.of(
            "len", "str", "int", "float", "list", "dict", "tuple", "set",
            "print", "input", "range", "enumerate", "zip", "map", "filter",
            "any", "all", "sum", "max", "min", "abs", "round", "sorted"
        );
        
        return builtins.contains(callText);
    }
}
```

## 🧪 测试开发

### 单元测试

```java
class PythonASTAnalyzerTest {
    
    @Test
    void testClassAnalysis() {
        String pythonCode = """
            from dataclasses import dataclass
            
            @dataclass
            class User:
                id: int
                name: str
                
                def get_info(self) -> str:
                    return f"{self.name}({self.id})"
            """;
            
        PyFile testFile = createTestFile(pythonCode);
        PythonASTAnalyzer analyzer = new PythonASTAnalyzer();
        AnalysisResult result = analyzer.analyze(testFile);
        
        assertEquals(3, result.getNodes().size()); // class + 2 fields + method
        assertTrue(result.getNodes().containsKey("User"));
        
        AnalysisNode classNode = result.getNodes().get("User");
        List<String> decorators = (List<String>) classNode.getMetadata().get("decorators");
        assertEquals(List.of("dataclass"), decorators);
    }
    
    @Test
    void testTypeAnnotationAnalysis() {
        String pythonCode = """
            def process_data(items: List[str], default: str = "unknown") -> Dict[str, int]:
                result = {}
                for item in items:
                    result[item] = len(item)
                return result
            """;
            
        PyFile testFile = createTestFile(pythonCode);
        AnalysisResult result = analyzer.analyze(testFile);
        
        AnalysisNode methodNode = result.getNodes().get("process_data");
        assertEquals("Dict[str, int]", methodNode.getMetadata().get("returnType"));
    }
}
```

### 集成测试

```java
class PythonPluginIntegrationTest {
    
    @Test
    void testPythonPluginDependency() {
        // 测试Python插件是否可用
        PluginManager pluginManager = PluginManager.getInstance();
        PluginId pythonPluginId = PluginId.getId("PythonCore");
        assertTrue(pluginManager.isPluginEnabled(pythonPluginId));
    }
    
    @Test
    void testPsiElementCreation() {
        // 测试能否创建Python PSI元素
        PyElementGenerator generator = PyElementGenerator.getInstance(getProject());
        PyStatement statement = generator.createFromText(
            LanguageLevel.PYTHON38, 
            PyStatement.class, 
            "x = 1"
        );
        assertNotNull(statement);
        assertTrue(statement instanceof PyAssignmentStatement);
    }
}
```

## 🔧 工具类实现

### PythonPsiUtils

Python PSI 工具方法：

```java
public class PythonPsiUtils {
    
    public static String getFullyQualifiedName(PyClass pyClass) {
        PyFile containingFile = (PyFile) pyClass.getContainingFile();
        String moduleName = getModuleName(containingFile);
        return moduleName + "." + pyClass.getName();
    }
    
    public static String getFunctionId(PyFunction function) {
        String functionName = function.getName();
        if (functionName == null) return null;
        
        PyClass containingClass = function.getContainingClass();
        if (containingClass != null) {
            return getFullyQualifiedName(containingClass) + "." + functionName;
        } else {
            PyFile containingFile = (PyFile) function.getContainingFile();
            String moduleName = getModuleName(containingFile);
            return moduleName + "." + functionName;
        }
    }
    
    public static String getModuleName(PyFile pyFile) {
        VirtualFile virtualFile = pyFile.getVirtualFile();
        if (virtualFile == null) return "unknown";
        
        String fileName = virtualFile.getNameWithoutExtension();
        
        // 检查是否在包中
        VirtualFile parent = virtualFile.getParent();
        List<String> packageParts = new ArrayList<>();
        
        while (parent != null) {
            VirtualFile initFile = parent.findChild("__init__.py");
            if (initFile != null) {
                packageParts.add(0, parent.getName());
                parent = parent.getParent();
            } else {
                break;
            }
        }
        
        if (!packageParts.isEmpty()) {
            return String.join(".", packageParts) + "." + fileName;
        } else {
            return fileName;
        }
    }
    
    public static List<String> getDecorators(PyDecoratable decoratable) {
        PyDecoratorList decoratorList = decoratable.getDecoratorList();
        if (decoratorList == null) return Collections.emptyList();
        
        return Arrays.stream(decoratorList.getDecorators())
                .map(decorator -> getDecoratorName(decorator))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    public static List<String> getBaseClasses(PyClass pyClass) {
        return Arrays.stream(pyClass.getSuperClassExpressions())
                .map(expr -> expr.getText())
                .collect(Collectors.toList());
    }
    
    public static int getLineNumber(PsiElement element) {
        Document document = PsiDocumentManager.getInstance(element.getProject())
                .getDocument(element.getContainingFile());
        if (document != null) {
            return document.getLineNumber(element.getTextOffset()) + 1;
        }
        return 0;
    }
}
```

### PythonSignatureUtils

Python 签名生成工具：

```java
public class PythonSignatureUtils {
    
    public static String getClassSignature(PyClass pyClass) {
        StringBuilder signature = new StringBuilder();
        
        // 装饰器
        List<String> decorators = PythonPsiUtils.getDecorators(pyClass);
        for (String decorator : decorators) {
            signature.append("@").append(decorator).append(" ");
        }
        
        signature.append("class ").append(pyClass.getName());
        
        // 基类
        PyExpression[] superClassExpressions = pyClass.getSuperClassExpressions();
        if (superClassExpressions.length > 0) {
            signature.append("(");
            signature.append(Arrays.stream(superClassExpressions)
                    .map(expr -> expr.getText())
                    .collect(Collectors.joining(", ")));
            signature.append(")");
        }
        
        return signature.toString();
    }
    
    public static String getFunctionSignature(PyFunction function) {
        StringBuilder signature = new StringBuilder();
        
        // 装饰器
        List<String> decorators = PythonPsiUtils.getDecorators(function);
        for (String decorator : decorators) {
            signature.append("@").append(decorator).append(" ");
        }
        
        signature.append("def ").append(function.getName()).append("(");
        
        // 参数
        List<String> parameters = new ArrayList<>();
        for (PyParameter parameter : function.getParameterList().getParameters()) {
            StringBuilder paramSignature = new StringBuilder();
            paramSignature.append(parameter.getName());
            
            // 类型注解
            PyTypeAnnotation annotation = parameter.getAnnotation();
            if (annotation != null) {
                paramSignature.append(": ").append(annotation.getText());
            }
            
            // 默认值
            PyExpression defaultValue = parameter.getDefaultValue();
            if (defaultValue != null) {
                paramSignature.append(" = ").append(defaultValue.getText());
            }
            
            parameters.add(paramSignature.toString());
        }
        
        signature.append(String.join(", ", parameters));
        signature.append(")");
        
        // 返回类型
        PyTypeAnnotation returnType = function.getReturnType();
        if (returnType != null) {
            signature.append(" -> ").append(returnType.getText());
        }
        
        return signature.toString();
    }
}
```

## 🚀 性能优化

### PSI 访问优化

```java
// 正确的Python PSI访问
public void analyzePythonClasses(PyFile pyFile) {
    ReadAction.run(() -> {
        PyClass[] classes = pyFile.getTopLevelClasses();
        for (PyClass pyClass : classes) {
            analyzePythonClass(pyClass);
        }
    });
}

// 处理Python特有的动态特性
private boolean isCallable(PyExpression expression) {
    return ReadAction.compute(() -> {
        PyType type = TypeEvalContext.codeAnalysis(
            expression.getProject(), 
            expression.getContainingFile()
        ).getType(expression);
        return type instanceof PyCallableType;
    });
}
```

### 缓存策略

```java
public class CachedPythonAnalyzer implements ASTAnalyzer {
    private final Map<String, AnalysisResult> cache = new ConcurrentHashMap<>();
    
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        String cacheKey = generateCacheKey(psiFile);
        return cache.computeIfAbsent(cacheKey, key -> performAnalysis(psiFile));
    }
    
    private String generateCacheKey(PsiFile psiFile) {
        VirtualFile vFile = psiFile.getVirtualFile();
        return vFile.getPath() + ":" + vFile.getModificationStamp();
    }
}
```

## 🔌 扩展开发

### 添加异步代码分析

```java
public class AsyncPythonAnalyzer extends PythonASTAnalyzer {
    
    @Override
    protected void analyzeSpecialCases(PsiElement element, AnalysisResult result) {
        super.analyzeSpecialCases(element, result);
        
        if (element instanceof PyFunction) {
            PyFunction function = (PyFunction) element;
            if (function.isAsync()) {
                analyzeAsyncFunction(function, result);
            }
        }
        
        if (element instanceof PyWithStatement) {
            analyzeAsyncContext((PyWithStatement) element, result);
        }
    }
    
    private void analyzeAsyncFunction(PyFunction asyncFunction, AnalysisResult result) {
        // 分析 await 表达式
        asyncFunction.accept(new PyElementVisitor() {
            @Override
            public void visitPyAwaitExpression(PyAwaitExpression awaitExpr) {
                // 记录异步调用关系
                recordAsyncCall(asyncFunction, awaitExpr, result);
                super.visitPyAwaitExpression(awaitExpr);
            }
        });
    }
}
```

### 自定义Python特性分析

```java
public class AdvancedPythonAnalyzer extends PythonASTAnalyzer {
    
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        AnalysisResult result = super.analyze(psiFile);
        
        // 添加Python特有分析
        analyzeGenerators(psiFile, result);
        analyzeListComprehensions(psiFile, result);
        analyzeMetaclasses(psiFile, result);
        
        return result;
    }
    
    private void analyzeGenerators(PsiFile psiFile, AnalysisResult result) {
        psiFile.accept(new PyElementVisitor() {
            @Override
            public void visitPyYieldExpression(PyYieldExpression yieldExpr) {
                // 分析生成器
                recordGeneratorPattern(yieldExpr, result);
                super.visitPyYieldExpression(yieldExpr);
            }
        });
    }
}
```

## 🐛 调试技巧

### 启用Python特定调试

```java
private static final Logger logger = Logger.getInstance(PythonASTAnalyzer.class);

private void debugLog(String message, Object... args) {
    if (Boolean.getBoolean("ast.analyzer.debug")) {
        logger.info("[PYTHON-DEBUG] " + String.format(message, args));
    }
}

// 调试Python特有元素
private void debugPythonElement(PsiElement element) {
    if (element instanceof PyClass) {
        PyClass pyClass = (PyClass) element;
        debugLog("Found Python class: %s with decorators: %s", 
                pyClass.getName(), 
                PythonPsiUtils.getDecorators(pyClass));
    }
}
```

### 常见问题解决

1. **Python插件依赖问题**：
   - 确保PythonCore插件已启用
   - 检查插件版本兼容性
   - 验证Python PSI API可用性

2. **类型推断问题**：
   - 使用TypeEvalContext进行类型分析
   - 处理动态类型特性
   - 正确解析类型注解

3. **包导入问题**：
   - 正确处理相对导入
   - 解析包结构层次
   - 处理命名空间包

## 📚 参考资源

- [Python Plugin API](https://plugins.jetbrains.com/docs/intellij/python.html)
- [PyCharm Plugin Development](https://plugins.jetbrains.com/docs/intellij/pycharm.html) 
- [Python PSI Reference](https://github.com/JetBrains/intellij-community/tree/master/python/psi-api)

通过遵循这些开发指南，您可以高效地开发和扩展Python插件功能，充分利用Python语言的特性和PyCharm的强大PSI API。