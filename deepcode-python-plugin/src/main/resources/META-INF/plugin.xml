<idea-plugin>
    <id>com.sankuai.deepcode.astplugin.python</id>
    <name>DeepCode AST Analyzer for Python</name>
    <vendor email="<EMAIL>">Shanghai Sankuai Technology Co., Ltd.</vendor>

    <description><![CDATA[
    AST分析器 - Python语言支持

    专门为PyCharm Community版本设计的Python代码分析插件。
    支持Python代码的AST解析、结构分析和调用关系分析。

    功能特性：
    - Python类、函数、方法结构解析
    - 函数调用关系分析
    - 导入语句分析
    - 代码统计信息
    - 可视化AST结果展示
    ]]></description>

    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.modules.python</depends>

    <extensions defaultExtensionNs="com.intellij">
        <!-- 注册共享的分析器服务实现 -->
        <applicationService
                serviceInterface="com.sankuai.deepcode.astplugin.analyzer.AnalyzerService"
                serviceImplementation="com.sankuai.deepcode.astplugin.analyzer.AnalyzerService"/>

        <!-- 插件启动时初始化组件 -->
        <postStartupActivity
                implementation="com.sankuai.deepcode.astplugin.python.PythonPluginStartup"/>

        <!-- 注册工具窗口 - 使用shared-ui中的类 -->
        <toolWindow id="AST Analysis"
                   factoryClass="com.sankuai.deepcode.astplugin.ui.ASTAnalysisToolWindow"
                   anchor="right"
                   icon="AllIcons.Toolwindows.ToolWindowStructure"/>
    </extensions>

    <actions>
        <!-- 工具栏动作 - 使用shared-ui中的类 -->
        <group id="AST.AnalysisActions" text="AST Analysis" popup="true">
            <action id="AST.QuickAnalyzer"
                   class="com.sankuai.deepcode.astplugin.action.QuickAnalyzerAction"
                   text="Quick AST Analysis"
                   description="Quick AST analysis for current file"/>

            <action id="AST.ShowToolWindow"
                   class="com.sankuai.deepcode.astplugin.ui.ShowASTToolWindowAction"
                   text="Show AST Analysis Window"
                   description="Show AST Analysis tool window"/>

            <action id="AST.ExportReport"
                   class="com.sankuai.deepcode.astplugin.action.ExportReportAction"
                   text="Export AST Report"
                   description="Export AST analysis report">
                <keyboard-shortcut first-keystroke="ctrl alt E" keymap="$default"/>
            </action>

            <action id="AST.ExportProject"
                   class="com.sankuai.deepcode.astplugin.action.ExportProjectAction"
                   text="Export Project AST Report"
                   description="Export AST analysis report for entire project">
            </action>

            <add-to-group group-id="ToolsMenu" anchor="last"/>
        </group>

        <!-- 上下文菜单动作 -->
        <group id="AST.EditorPopup">
            <reference ref="AST.QuickAnalyzer"/>
            <add-to-group group-id="EditorPopupMenu" anchor="last"/>
            <separator/>
        </group>
    </actions>
</idea-plugin>