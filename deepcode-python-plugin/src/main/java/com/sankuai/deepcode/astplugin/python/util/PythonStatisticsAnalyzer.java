package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.psi.util.PsiTreeUtil;
import com.jetbrains.python.psi.PyFile;
import com.jetbrains.python.psi.PyFunction;
import com.jetbrains.python.psi.PyImportElement;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;

import java.util.Collection;
import java.util.List;

/**
 * Python 统计信息分析工具类
 * 
 * <AUTHOR>
 */
public final class PythonStatisticsAnalyzer {
    
    private PythonStatisticsAnalyzer() {
        // 工具类不允许实例化
    }
    
    /**
     * 更新统计信息
     */
    public static void updateStatistics(PyFile pythonFile, AnalysisResult result) {
        result.updateStatistics("file_size", pythonFile.getTextLength());
        result.updateStatistics("class_count", pythonFile.getTopLevelClasses().size());
        result.updateStatistics("function_count", pythonFile.getTopLevelFunctions().size());

        Collection<PyFunction> allMethods = PsiTreeUtil.findChildrenOfType(pythonFile, PyFunction.class);
        result.updateStatistics("method_count", allMethods.size());

        List<PyImportElement> imports = pythonFile.getImportTargets();
        result.updateStatistics("imports", imports.size());

        result.updateStatistics("language_supported", 1);
    }
}