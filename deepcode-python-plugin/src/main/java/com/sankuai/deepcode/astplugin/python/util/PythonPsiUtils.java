package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.jetbrains.python.psi.PyClass;
import com.jetbrains.python.psi.PyFile;
import com.jetbrains.python.psi.PyFunction;

/**
 * Python PSI 相关工具类
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonPsiUtils {

    private static final Logger LOG = Logger.getInstance(PythonPsiUtils.class);

    private PythonPsiUtils() {
        // 工具类不允许实例化
    }

    /**
     * 获取PSI元素的行号
     */
    public static int getLineNumber(PsiElement element) {
        return PythonAnalyzerUtils.getSafeLineNumber(element);
    }

    /**
     * 获取PSI元素的文件路径（项目相对路径）
     */
    public static String getFilePath(PsiElement element) {
        if (element == null || element.getContainingFile() == null) {
            return null;
        }

        PsiFile containingFile = element.getContainingFile();
        VirtualFile virtualFile = containingFile.getVirtualFile();

        if (virtualFile != null) {
            try {
                String fullPath = virtualFile.getPath();
                LOG.debug("Getting file path for element " + element.getClass().getSimpleName() +
                         ", full path: " + fullPath);

                // 使用新的通用路径解析工具
                String relativePath = PythonPathResolver.getProjectRelativePath(virtualFile, element.getProject());
                if (relativePath != null) {
                    LOG.debug("Returning relative path: " + relativePath);
                    return relativePath;
                }

            } catch (Exception e) {
                LOG.warn("Error getting file path for " + virtualFile.getPath() + ": " + e.getMessage(), e);
            }
        } else {
            LOG.debug("VirtualFile is null for " + containingFile.getName() +
                     ", element: " + element.getClass().getSimpleName());
        }

        String fileName = containingFile.getName();
        LOG.debug("Returning file name as fallback: " + fileName);
        return fileName;
    }



    /**
     * 获取模块名
     */
    public static String getModuleName(PsiElement element) {
        PsiFile containingFile = element.getContainingFile();
        if (containingFile instanceof PyFile pyFile) {
            String fileName = pyFile.getName();

            // 处理不同的Python文件扩展名
            if (fileName.endsWith(".py")) {
                return fileName.substring(0, fileName.length() - 3);
            } else if (fileName.endsWith(".pyi") || fileName.endsWith(".pyw")) {
                // Python类型标注文件
                // Windows Python脚本文件
                return fileName.substring(0, fileName.length() - 4);
            } else {
                // 其他情况，移除最后一个点后的扩展名
                int lastDot = fileName.lastIndexOf('.');
                return lastDot > 0 ? fileName.substring(0, lastDot) : fileName;
            }
        }
        return "Unknown";
    }

    /**
     * 获取完整的Python包名
     */
    public static String getPackageName(PsiElement element) {
        try {
            // 使用新的通用模块解析器
            PythonModuleResolver.ModuleInfo moduleInfo = PythonModuleResolver.resolveModuleInfo(element);
            return moduleInfo.packageName();
        } catch (Exception e) {
            LOG.warn("Error calculating package name: " + e.getMessage(), e);
            return getModuleName(element);
        }
    }



    /**
     * 获取包含函数的类名
     */
    public static String getContainingClassName(PyFunction function) {
        PyClass containingClass = PsiTreeUtil.getParentOfType(function, PyClass.class);
        return containingClass != null ? containingClass.getName() : null;
    }

    /**
     * 检查函数是否是嵌套函数（定义在另一个函数内部）
     */
    public static boolean isInnerFunction(PyFunction function) {
        // 查找父级是否有PyFunction（排除同一级别的函数）
        PsiElement parent = function.getParent();
        while (parent != null) {
            if (parent instanceof PyFunction && parent != function) {
                return true;
            }
            // 如果遇到PyClass，说明是类方法，不是嵌套函数
            if (parent instanceof PyClass || parent instanceof PyFile) {
                return false;
            }
            parent = parent.getParent();
        }
        return false;
    }

    /**
     * 获取嵌套函数的外层函数
     */
    public static PyFunction getContainingFunction(PyFunction innerFunction) {
        if (!isInnerFunction(innerFunction)) {
            return null;
        }
        return PsiTreeUtil.getParentOfType(innerFunction, PyFunction.class);
    }

    /**
     * 构建函数签名
     */
    public static String buildFunctionSignature(PyFunction function, String className) {
        StringBuilder signature = new StringBuilder();

        if (className != null) {
            signature.append(className).append(".");
        }

        signature.append(function.getName()).append("(");

        var parameterList = function.getParameterList();
        var parameters = parameterList.getParameters();

        for (int i = 0; i < parameters.length; i++) {
            if (i > 0) {
                signature.append(", ");
            }
            signature.append(parameters[i].getName());
        }

        signature.append(")");

        return signature.toString();
    }

    /**
     * 构建包含模块名的完整函数ID
     * 格式: module.class_name.method_name 或 module.func_name 或 module.outer_func.inner_func (对于嵌套函数)
     */
    public static String buildFullFunctionId(PyFunction function, String className) {
        StringBuilder fullId = new StringBuilder();

        // 添加模块名
        String moduleName = getModuleName(function);
        fullId.append(moduleName);

        // 添加类名（如果存在）
        if (className != null) {
            fullId.append(".").append(className);
        }

        // 处理嵌套函数的情况
        if (isInnerFunction(function)) {
            // 构建嵌套函数的完整路径
            PyFunction containingFunction = getContainingFunction(function);
            if (containingFunction != null) {
                fullId.append(".").append(containingFunction.getName());
            }
        }

        // 添加函数名
        fullId.append(".").append(function.getName());

        return fullId.toString();
    }

    /**
     * 检查是否是装饰器调用
     * 使用精确的PSI检测方法
     */
    public static boolean isDecoratorCall(com.jetbrains.python.psi.PyCallExpression callExpression) {
        try {
            // 最直接的方法：检查调用表达式的父元素是否直接是PyDecorator
            PsiElement parent = callExpression.getParent();
            if (parent instanceof com.jetbrains.python.psi.PyDecorator) {
                LOG.debug("Found decorator call (direct parent): " + callExpression.getText());
                return true;
            }

            // 检查向上两层，但严格限制范围
            PsiElement grandParent = parent != null ? parent.getParent() : null;
            if (grandParent instanceof com.jetbrains.python.psi.PyDecorator) {
                LOG.debug("Found decorator call (grandparent): " + callExpression.getText());
                return true;
            }

            return false;

        } catch (Exception e) {
            LOG.debug("Error checking decorator call for '" + callExpression.getText() + "': " + e.getMessage());
            // 保守策略：检测失败时返回false，不过滤
            return false;
        }
    }

    /**
     * 从调用文本中提取方法名
     */
    public static String extractMethodNameFromCall(String callText) {
        if (callText.contains(".")) {
            String[] parts = callText.split("\\.");
            return parts[parts.length - 1];
        }
        return callText;
    }
}