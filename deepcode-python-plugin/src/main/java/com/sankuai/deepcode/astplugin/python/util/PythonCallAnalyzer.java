package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.util.PsiTreeUtil;
import com.jetbrains.python.psi.*;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;
import com.sankuai.deepcode.astplugin.model.Language;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * Python 调用关系分析工具类
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonCallAnalyzer {

    private static final Logger LOG = Logger.getInstance(PythonCallAnalyzer.class);

    private PythonCallAnalyzer() {
        // 工具类不允许实例化
    }

    /**
     * 安全地提取PSI文本，确保不持有PSI引用
     */
    private static String extractSafeText(String psiText) {
        return psiText == null ? null : String.valueOf(psiText);
    }

    /**
     * 分析调用关系
     */
    public static void analyzeCallRelations(PyFile pythonFile, AnalysisResult result) {
        Collection<PyCallExpression> callExpressions =
                PsiTreeUtil.findChildrenOfType(pythonFile, PyCallExpression.class);

        for (PyCallExpression callExpression : callExpressions) {
            analyzeCallExpression(callExpression, result);
        }
    }

    /**
     * 分析单个调用表达式
     */
    public static void analyzeCallExpression(PyCallExpression callExpression, AnalysisResult result) {
        try {
            // 立即提取所有PSI文本并转换为安全字符串，避免后续持有PSI引用
            String safeCallExpressionText = extractSafeText(callExpression.getText());
            
            // 检查是否是装饰器调用，如果是则跳过
            if (PythonPsiUtils.isDecoratorCall(callExpression)) {
                LOG.debug("Skipping decorator call: " + safeCallExpressionText);
                return;
            }

            // 获取被调用的表达式
            PyExpression callee = callExpression.getCallee();
            if (callee == null) {
                return;
            }

            String calleeText = extractSafeText(callee.getText());

            // 找到调用方（可能是函数内调用或模块级调用）
            AnalysisNode callerNode = findCallerNode(callExpression, result);
            if (callerNode == null) {
                LOG.debug("No valid caller context found for call: " + safeCallExpressionText);
                return;
            }

            // 尝试解析被调用的函数/方法
            PsiElement resolved = null;
            if (callee instanceof PyReferenceExpression referenceExpression) {
                resolved = referenceExpression.getReference().resolve();
            }

            // 初始化变量，确保在所有分支中都有默认值
            AnalysisNode calleeNode = null;
            boolean isExternal = true;

            // 特殊处理 classmethod 中的 cls 调用（如 cls(...)）
            if (callee instanceof PyReferenceExpression referenceExpression &&
                "cls".equals(referenceExpression.getName()) &&
                isInClassMethod(callExpression)) {

                var resolveResult = handleClassMethodCall(callExpression, result);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
            }
            // 特殊处理属性访问调用（如 self.hf_model(**batch)）
            else if (callee instanceof PyReferenceExpression referenceExpression &&
                calleeText.contains(".") &&
                resolved instanceof PyTargetExpression) {

                var resolveResult = handleAttributeCall(referenceExpression, callExpression, calleeText, (PyTargetExpression) resolved, result);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
            }
            // 使用新的解析器来处理不同类型的解析结果
            else if (resolved instanceof PyFunction resolvedFunction) {
                var resolveResult = PythonCallExpressionResolver.resolveFunctionCall(resolvedFunction, callExpression, result);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
            } else if (resolved instanceof PyClass resolvedClass) {
                var resolveResult = PythonCallExpressionResolver.resolveClassCall(resolvedClass, callExpression, result);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
            } else if (resolved instanceof PyTargetExpression resolvedTarget) {
                var resolveResult = PythonCallExpressionResolver.resolveTargetExpressionCall(resolvedTarget, callExpression, calleeText);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
            } else if (resolved != null) {
                var resolveResult = PythonCallExpressionResolver.resolveGenericElement(resolved, calleeText, callExpression);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
            } else if (resolved == null && calleeText.contains(".")) {
                // 特殊处理：无法解析的属性调用，检查是否是参数调用
                String[] parts = calleeText.split("\\.", 2);
                String objectRef = parts[0]; // 如 "tokenizer"
                
                // 初始化变量
                calleeNode = null;
                isExternal = true;
                
                // 尝试解析对象引用部分
                try {
                    PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
                    if (containingFunction != null && containingFunction.isValid()) {
                        PyParameterList parameterList = containingFunction.getParameterList();
                        for (PyParameter parameter : parameterList.getParameters()) {
                            if (parameter.getName() != null && parameter.getName().equals(objectRef)) {
                                // 找到了参数，创建参数调用节点
                                LOG.debug("Detected unresolved parameter call: " + calleeText);
                                
                                // 创建参数调用节点，使用我们修复过的逻辑
                                var resolveResult = createUnresolvedParameterCallResult(parameter, callExpression, calleeText);
                                calleeNode = resolveResult.calleeNode();
                                isExternal = resolveResult.isExternal();
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    LOG.debug("Error checking unresolved attribute call for parameter: " + e.getMessage());
                }
                
                // 如果没有找到参数匹配，回退到外部方法节点
                if (calleeNode == null) {
                    calleeNode = PythonCallExpressionResolver.createExternalMethodNode(callExpression, calleeText);
                    isExternal = true;
                }
            } else {
                // 无法解析的调用，创建外部方法节点但不添加到节点集合中
                calleeNode = PythonCallExpressionResolver.createExternalMethodNode(callExpression, calleeText);
                isExternal = true;
            }

            
            
            List<CallRelation.CallInstance> instances = new ArrayList<>();
            instances.add(new CallRelation.CallInstance(
                    PythonPsiUtils.getLineNumber(callExpression),
                    safeCallExpressionText
            ));

            CallRelation relation = new CallRelation(
                    callerNode,
                    calleeNode,
                    PythonPsiUtils.getLineNumber(callExpression),
                    safeCallExpressionText,
                    isExternal,
                    instances
            );

            result.addCallRelation(relation);

        } catch (Exception e) {
            // 使用安全的文本，避免在错误处理时也访问PSI
            String errorText;
            try {
                errorText = extractSafeText(callExpression.getText());
            } catch (Exception psiException) {
                errorText = "[PSI_ACCESS_FAILED]";
            }
            LOG.warn("Error analyzing call expression: " + errorText, e);
        }
    }

    /**
     * 处理属性访问调用（如 self.hf_model）
     */
    private static PythonCallExpressionResolver.ResolveResult handleAttributeCall(
            PyReferenceExpression referenceExpression,
            PyCallExpression callExpression,
            String calleeText,
            PyTargetExpression resolvedTarget,
            AnalysisResult result) {

        try {
            LOG.debug("Handling attribute call: " + calleeText);

            // 首先检查所有PSI元素的有效性
            if (!callExpression.isValid()) {
                LOG.debug("CallExpression PSI element is invalid in handleAttributeCall");
                return createFallbackAttributeCallResult(calleeText, callExpression);
            }

            if (!resolvedTarget.isValid()) {
                LOG.debug("ResolvedTarget PSI element is invalid in handleAttributeCall");
                return createFallbackAttributeCallResult(calleeText, callExpression);
            }

            // 解析调用文本，提取属性名和对象引用
            String[] parts = calleeText.split("\\.");
            if (parts.length >= 2) {
                String objectRef = parts[0]; // 如 "self" 或 "tokenizer"
                String attributeName = parts[parts.length - 1]; // 如 "hf_model" 或 "prepare_for_model"

                // 先检查是否是参数调用
                PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
                if (containingFunction != null) {
                    try {
                        // 检查PSI元素是否有效
                        if (!containingFunction.isValid()) {
                            LOG.debug("Containing function PSI element is invalid in handleAttributeCall");
                        } else {
                            PyParameterList parameterList = containingFunction.getParameterList();
                            for (PyParameter parameter : parameterList.getParameters()) {
                                try {
                                    if (parameter.getName() != null && parameter.getName().equals(objectRef)) {
                                        // 这是参数调用，使用参数调用逻辑处理
                                        LOG.debug("Detected parameter call in handleAttributeCall: " + calleeText);
                                        return PythonCallExpressionResolver.resolveTargetExpressionCall(resolvedTarget, callExpression, calleeText);
                                    }
                                } catch (Exception paramEx) {
                                    LOG.debug("Error accessing parameter in handleAttributeCall: " + paramEx.getMessage());
                                }
                            }
                        }
                    } catch (Exception e) {
                        LOG.debug("Error checking parameters in handleAttributeCall: " + e.getMessage());
                    }
                }

                // 查找包含该属性的上下文类
                try {
                    PyClass containingClass = PsiTreeUtil.getParentOfType(callExpression, PyClass.class);
                    if (containingClass != null && containingClass.isValid() && objectRef.equals("self")) {
                        // 这是一个实例属性调用
                        String className = containingClass.getName();
                        String packageName = PythonPsiUtils.getPackageName(containingClass);

                        // 构造完整的属性ID
                        String attributeFullId = packageName + "." + className + "." + attributeName;

                        // 构造属性方法的签名
                        String attributeSignature = attributeName + "()";

                        AnalysisNode attributeNode = new AnalysisNode(
                                attributeFullId,
                                AnalysisNode.NodeType.METHOD, // 可调用的属性视为方法
                                attributeName, // 显示名称
                                className, // 属性所属的类
                                packageName,
                                PythonPsiUtils.getLineNumber(callExpression), // 使用调用位置的行号
                                attributeSignature,
                                packageName,
                                PythonPsiUtils.getFilePath(containingClass),
                                Language.PYTHON.getCode()
                        );

                        LOG.debug("Created attribute node: " + attributeFullId);

                        return new PythonCallExpressionResolver.ResolveResult(attributeNode, false);
                    }
                } catch (Exception classEx) {
                    LOG.debug("Error accessing containing class in handleAttributeCall: " + classEx.getMessage());
                }
            }

            // 回退到默认的 PyTargetExpression 处理
            return PythonCallExpressionResolver.resolveTargetExpressionCall(resolvedTarget, callExpression, calleeText);

        } catch (Exception e) {
            LOG.warn("Error handling attribute call: " + calleeText + " - " + e.getMessage(), e);
            // 回退到默认处理
            return PythonCallExpressionResolver.resolveTargetExpressionCall(resolvedTarget, callExpression, calleeText);
        }
    }



    /**
     * 查找调用方节点（函数内调用、类字段调用或模块级调用）
     */
    public static AnalysisNode findCallerNode(PyCallExpression callExpression, AnalysisResult result) {
        try {
            // 首先查找是否在函数内部（优先级最高）
            PyFunction callerFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);

            if (callerFunction != null) {
                // 函数内调用
                String callerSignature = PythonPsiUtils.buildFunctionSignature(
                        callerFunction, PythonPsiUtils.getContainingClassName(callerFunction));
                return PythonStructureAnalyzer.findOrCreateFunctionNode(callerSignature, callerFunction, result);
            }

            // 检查是否在类内部（类字段定义中的调用）
            PyClass callerClass = PsiTreeUtil.getParentOfType(callExpression, PyClass.class);
            if (callerClass != null) {
                // 类字段中的调用，caller 是类节点本身
                String className = callerClass.getName();
                if (className != null) {
                    // 使用与 PythonStructureAnalyzer 相同的 ID 生成策略
                    String packageName = PythonPsiUtils.getPackageName(callerClass);
                    String classFullId = packageName + "." + className;

                    // 查找或创建类节点
                    AnalysisNode existingClassNode = result.getNodes().get(classFullId);
                    // 如果类节点不存在，使用 PythonStructureAnalyzer 的方法创建
                    return Objects.requireNonNullElseGet(existingClassNode, () -> PythonStructureAnalyzer.findOrCreateClassNode(classFullId, callerClass, result));
                }
            }

            // 模块级调用 - 创建或获取模块节点作为调用者
            return findOrCreateModuleNode(callExpression, result);

        } catch (Exception e) {
            LOG.warn("Error finding caller node for call: " + callExpression.getText() + " - " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查找或创建模块节点
     */
    private static AnalysisNode findOrCreateModuleNode(PyCallExpression callExpression, AnalysisResult result) {
        try {
            // 获取模块的完整包名路径
            String moduleFullPath = PythonPsiUtils.getPackageName(callExpression);

            // 检查是否已存在模块节点
            AnalysisNode existingModuleNode = result.getNodes().get(moduleFullPath);
            if (existingModuleNode != null) {
                return existingModuleNode;
            }

            // 创建模块节点，使用完整包名作为ID和signature
            String moduleName = PythonPsiUtils.getModuleName(callExpression);
            AnalysisNode moduleNode = new AnalysisNode(
                    // 使用完整包名作为ID
                    moduleFullPath,
                    AnalysisNode.NodeType.MODULE,
                    moduleName, // 显示名称使用模块名
                    null, // 模块没有类名
                    moduleFullPath, // 包名就是自己的全路径
                    1, // 模块从第1行开始
                    moduleFullPath, // signature也使用完整包名
                    moduleFullPath,
                    PythonPsiUtils.getFilePath(callExpression),
                    Language.PYTHON.getCode()
            );

            result.addNode(moduleNode);
            LOG.debug("Created module node: " + moduleFullPath +
                    " for file: " + PythonPsiUtils.getFilePath(callExpression));

            return moduleNode;
        } catch (Exception e) {
            LOG.warn("Error creating module node: " + e.getMessage(), e);

            // 回退方案：使用简单的模块名
            String simpleName = PythonPsiUtils.getModuleName(callExpression);
            AnalysisNode fallbackNode = new AnalysisNode(
                    simpleName,
                    AnalysisNode.NodeType.MODULE,
                    simpleName,
                    null,
                    simpleName,
                    1,
                    simpleName,
                    simpleName,
                    PythonPsiUtils.getFilePath(callExpression),
                    Language.PYTHON.getCode()
            );

            result.addNode(fallbackNode);
            return fallbackNode;
        }
    }

    /**
     * 处理 classmethod 中的 cls 调用（如 cls(...)）
     */
    private static PythonCallExpressionResolver.ResolveResult handleClassMethodCall(
            PyCallExpression callExpression,
            AnalysisResult result) {

        try {
            LOG.debug("Handling classmethod cls call: " + callExpression.getText());

            // 查找包含此调用的函数
            PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
            if (containingFunction == null) {
                LOG.debug("No containing function found for cls call");
                return createGenericClsResult(callExpression);
            }

            // 检查函数是否有 @classmethod 装饰器
            boolean isClassMethod = isClassMethodDecorated(containingFunction);
            if (!isClassMethod) {
                LOG.debug("Function is not a classmethod, treating cls as regular variable");
                return createGenericClsResult(callExpression);
            }

            // 查找包含此 classmethod 的类
            PyClass containingClass = PsiTreeUtil.getParentOfType(containingFunction, PyClass.class);
            if (containingClass == null) {
                LOG.debug("No containing class found for classmethod");
                return createGenericClsResult(callExpression);
            }

            // cls(...) 调用实际上是调用类的构造函数，找到 __init__ 方法
            String className = containingClass.getName();
            String packageName = PythonPsiUtils.getPackageName(containingClass);

            // 查找类的 __init__ 方法
            PyFunction initMethod = containingClass.findMethodByName("__init__", false, null);
            if (initMethod != null) {
                // 构造 __init__ 方法的完整ID和签名
                String initFullId = packageName + "." + className + ".__init__";
                String initSignature = PythonPsiUtils.buildFunctionSignature(initMethod, className);

                AnalysisNode initNode = new AnalysisNode(
                        initFullId,
                        AnalysisNode.NodeType.METHOD,
                        "__init__", // 显示名称
                        className, // 所属类
                        packageName,
                        PythonPsiUtils.getLineNumber(initMethod),
                        initSignature,
                        packageName,
                        PythonPsiUtils.getFilePath(containingClass),
                        Language.PYTHON.getCode()
                );

                LOG.debug("Created classmethod cls call node pointing to __init__: " + initFullId);
                return new PythonCallExpressionResolver.ResolveResult(initNode, false);
            } else {
                // 如果没有显式的 __init__ 方法，创建一个默认的构造函数节点
                String constructorFullId = packageName + "." + className + "." + className;
                String constructorSignature = className + "()";

                AnalysisNode constructorNode = new AnalysisNode(
                        constructorFullId,
                        AnalysisNode.NodeType.METHOD,
                        className, // 构造函数显示名称为类名
                        className, // 所属类
                        packageName,
                        PythonPsiUtils.getLineNumber(containingClass),
                        constructorSignature,
                        packageName,
                        PythonPsiUtils.getFilePath(containingClass),
                        Language.PYTHON.getCode()
                );

                LOG.debug("Created classmethod cls call node pointing to constructor: " + constructorFullId);
                return new PythonCallExpressionResolver.ResolveResult(constructorNode, false);
            }

        } catch (Exception e) {
            LOG.warn("Error handling classmethod cls call: " + callExpression.getText() + " - " + e.getMessage(), e);
            return createGenericClsResult(callExpression);
        }
    }

    /**
     * 检查函数是否有 @classmethod 装饰器
     */
    private static boolean isClassMethodDecorated(PyFunction function) {
        PyDecoratorList decoratorList = function.getDecoratorList();
        if (decoratorList == null) {
            return false;
        }

        for (PyDecorator decorator : decoratorList.getDecorators()) {
            PyExpression callee = decorator.getCallee();
            if (callee instanceof PyReferenceExpression) {
                String decoratorName = ((PyReferenceExpression) callee).getName();
                if ("classmethod".equals(decoratorName)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 创建通用的 cls 调用结果（当无法确定为 classmethod 时的回退）
     */
    private static PythonCallExpressionResolver.ResolveResult createGenericClsResult(PyCallExpression callExpression) {
        String clsFullId = "cls";
        String clsSignature = "cls()";

        AnalysisNode clsNode = new AnalysisNode(
                clsFullId,
                AnalysisNode.NodeType.FUNCTION,
                "cls",
                null,
                "UNKNOWN",
                PythonPsiUtils.getLineNumber(callExpression),
                clsSignature,
                "UNKNOWN",
                PythonPsiUtils.getFilePath(callExpression),
                Language.PYTHON.getCode()
        );

        return new PythonCallExpressionResolver.ResolveResult(clsNode, false);
    }

    /**
     * 检查调用表达式是否在 classmethod 中
     */
    private static boolean isInClassMethod(PyCallExpression callExpression) {
        PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
        if (containingFunction == null) {
            return false;
        }

        return isClassMethodDecorated(containingFunction);
    }

    /**
     * 创建无法直接解析的参数调用结果
     */
    private static PythonCallExpressionResolver.ResolveResult createUnresolvedParameterCallResult(PyParameter parameter, PyCallExpression callExpression, String calleeText) {
        try {
            // 获取参数名
            String parameterName = parameter.getName() != null ? parameter.getName() : "unknown_param";
            
            // 获取包含函数
            PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
            if (containingFunction == null) {
                return createFallbackParameterCallResult(parameterName, callExpression, calleeText);
            }

            // 构建完整的函数路径
            String functionFullPath = buildFunctionPath(containingFunction);
            
            // 从calleeText中提取实际的方法名
            String actualMethodName = extractMethodNameFromCalleeText(calleeText);

            // 构建ID: 完整函数路径.参数名
            String calleeFullId = functionFullPath + "." + parameterName;
            // 构建签名: 应该包含完整路径的签名
            String calleeSignature = functionFullPath + "." + parameterName + "." + actualMethodName + "()";

            // 获取当前文件的路径和模块名
            String filePath = PythonPsiUtils.getFilePath(callExpression);
            String modulePackageName = PythonPsiUtils.getPackageName(callExpression);

            AnalysisNode calleeNode = new AnalysisNode(
                    calleeFullId,
                    AnalysisNode.NodeType.FUNCTION,
                    // name 应该是实际被调用的方法名
                    actualMethodName,
                    // className 应该是 null，因为这是参数调用
                    null,
                    // packageName 应该是 null，因为这是参数调用
                    null,
                    PythonPsiUtils.getLineNumber(callExpression),
                    calleeSignature,
                    // moduleName 使用包含函数的模块名
                    modulePackageName,
                    filePath,
                    Language.PYTHON.getCode()
            );

            LOG.info("Created unresolved parameter call node: " + calleeFullId +
                    " for parameter '" + parameterName + "' calling method '" + actualMethodName + "'");

            // 参数调用标记为外部调用
            return new PythonCallExpressionResolver.ResolveResult(calleeNode, true);
        } catch (Exception e) {
            LOG.warn("Error creating unresolved parameter call result: " + e.getMessage(), e);
            return createFallbackParameterCallResult("unknown_param", callExpression, calleeText);
        }
    }

    /**
     * 构建完整的函数路径（包括模块、类、函数层级）
     */
    private static String buildFunctionPath(PyFunction function) {
        StringBuilder pathBuilder = new StringBuilder();

        // 获取模块信息
        String packageName = PythonPsiUtils.getPackageName(function);
        if (packageName != null && !packageName.isEmpty()) {
            pathBuilder.append(packageName);
        }

        // 获取类名（如果存在）
        String className = PythonPsiUtils.getContainingClassName(function);
        if (className != null && !className.isEmpty()) {
            if (!pathBuilder.isEmpty()) {
                pathBuilder.append(".");
            }
            pathBuilder.append(className);
        }

        // 添加函数名
        if (!pathBuilder.isEmpty()) {
            pathBuilder.append(".");
        }
        pathBuilder.append(function.getName() != null ? function.getName() : "unknown_function");

        return pathBuilder.toString();
    }

    /**
     * 从calleeText中提取方法名
     */
    private static String extractMethodNameFromCalleeText(String calleeText) {
        if (calleeText.contains(".")) {
            String[] parts = calleeText.split("\\.");
            return parts[parts.length - 1]; // 返回最后一部分
        }
        return calleeText;
    }

    /**
     * 创建回退的参数调用结果
     */
    private static PythonCallExpressionResolver.ResolveResult createFallbackParameterCallResult(String parameterName, PyCallExpression callExpression, String calleeText) {
        try {
            String methodName = extractMethodNameFromCalleeText(calleeText);
            String fallbackFullId = "unknown_function." + parameterName;
            String fallbackSignature = fallbackFullId + "." + methodName + "()";
            
            AnalysisNode fallbackNode = new AnalysisNode(
                    fallbackFullId,
                    AnalysisNode.NodeType.FUNCTION,
                    methodName,
                    null,
                    null,
                    PythonPsiUtils.getLineNumber(callExpression),
                    fallbackSignature,
                    "PARAMETER_CALL",
                    PythonPsiUtils.getFilePath(callExpression),
                    Language.PYTHON.getCode()
            );

            return new PythonCallExpressionResolver.ResolveResult(fallbackNode, true);
        } catch (Exception e) {
            LOG.warn("Error creating fallback parameter call result: " + e.getMessage(), e);
            
            // 最终回退方案
            AnalysisNode ultimateFallbackNode = new AnalysisNode(
                    calleeText,
                    AnalysisNode.NodeType.FUNCTION,
                    calleeText,
                    null,
                    "ULTIMATE_FALLBACK",
                    1,
                    calleeText + "()",
                    "ULTIMATE_FALLBACK",
                    "UNKNOWN",
                    Language.PYTHON.getCode()
            );

            return new PythonCallExpressionResolver.ResolveResult(ultimateFallbackNode, true);
        }
    }

    /**
     * 创建回退的属性调用结果（当PSI失效时使用）
     */
    private static PythonCallExpressionResolver.ResolveResult createFallbackAttributeCallResult(String calleeText, PyCallExpression callExpression) {
        try {
            String fallbackFullId = "FALLBACK." + calleeText;
            String fallbackSignature = calleeText + "()";
            String fallbackFilePath = "UNKNOWN";
            
            // 尝试安全地获取文件路径
            try {
                fallbackFilePath = PythonPsiUtils.getFilePath(callExpression);
            } catch (Exception e) {
                LOG.debug("Error getting file path in fallback: " + e.getMessage());
            }

            AnalysisNode fallbackNode = new AnalysisNode(
                    fallbackFullId,
                    AnalysisNode.NodeType.FUNCTION,
                    calleeText,
                    null,
                    "FALLBACK",
                    1, // 默认行号
                    fallbackSignature,
                    "FALLBACK",
                    fallbackFilePath,
                    Language.PYTHON.getCode()
            );

            return new PythonCallExpressionResolver.ResolveResult(fallbackNode, true);
        } catch (Exception e) {
            LOG.warn("Error creating fallback attribute call result: " + e.getMessage(), e);
            
            // 最终回退方案
            AnalysisNode ultimateFallbackNode = new AnalysisNode(
                    calleeText,
                    AnalysisNode.NodeType.FUNCTION,
                    calleeText,
                    null,
                    "ULTIMATE_FALLBACK",
                    1,
                    calleeText + "()",
                    "ULTIMATE_FALLBACK",
                    "UNKNOWN",
                    Language.PYTHON.getCode()
            );

            return new PythonCallExpressionResolver.ResolveResult(ultimateFallbackNode, true);
        }
    }
}