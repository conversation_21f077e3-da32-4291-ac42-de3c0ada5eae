package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.jetbrains.python.psi.PyCallExpression;
import com.jetbrains.python.psi.PyClass;
import com.jetbrains.python.psi.PyFunction;
import com.jetbrains.python.psi.PyTargetExpression;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.Language;
import com.sankuai.deepcode.astplugin.model.ModuleType;

/**
 * Python PSI元素解析工具类
 * 用于处理元素解析、项目内外判断、节点创建等通用逻辑
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonElementResolver {

    private static final Logger LOG = Logger.getInstance(PythonElementResolver.class);

    private PythonElementResolver() {
        // 工具类不允许实例化
    }

    /**
     * 检查PSI元素是否为项目内部定义的（通用方法）
     */
    public static boolean isProjectInternalElement(PsiElement element, PyCallExpression callExpression) {
        try {
            if (element == null) {
                return false;
            }

            PsiFile resolvedFile = element.getContainingFile();
            if (resolvedFile == null) {
                return false;
            }

            // 获取项目根目录
            String projectBasePath = callExpression.getProject().getBasePath();
            if (projectBasePath == null) {
                return false;
            }

            // 获取解析到的元素所在文件的路径
            String resolvedFilePath = resolvedFile.getVirtualFile() != null ?
                    resolvedFile.getVirtualFile().getPath() : null;

            if (resolvedFilePath == null) {
                return false;
            }

            return PythonPathResolver.isProjectInternalPath(resolvedFilePath, projectBasePath);

        } catch (Exception e) {
            LOG.warn("Error checking if element is project internal: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查函数是否为项目内部定义的
     */
    public static boolean isProjectInternalFunction(PyFunction resolvedFunction, PyCallExpression callExpression) {
        return isProjectInternalElement(resolvedFunction, callExpression);
    }

    /**
     * 检查类是否为项目内部定义的
     */
    public static boolean isProjectInternalClass(PyClass resolvedClass, PyCallExpression callExpression) {
        return isProjectInternalElement(resolvedClass, callExpression);
    }



    /**
     * 确定函数节点类型（嵌套函数、类方法或普通函数）
     */
    public static AnalysisNode.NodeType determineFunctionNodeType(PyFunction function) {
        if (PythonPsiUtils.isInnerFunction(function)) {
            return AnalysisNode.NodeType.INNER_FUNCTION;
        } else if (PythonPsiUtils.getContainingClassName(function) != null) {
            return AnalysisNode.NodeType.METHOD;
        } else {
            return AnalysisNode.NodeType.FUNCTION;
        }
    }

    /**
     * 构建函数签名（根据函数类型）
     */
    public static String buildFunctionSignature(PyFunction function, String className) {
        if (PythonPsiUtils.isInnerFunction(function)) {
            PyFunction containingFunction = PythonPsiUtils.getContainingFunction(function);
            return buildNestedFunctionSignature(function, containingFunction, className);
        } else {
            return PythonPsiUtils.buildFunctionSignature(function, className);
        }
    }

    /**
     * 构建嵌套函数的特殊签名，显示它在外层函数中的位置
     */
    public static String buildNestedFunctionSignature(PyFunction nestedFunction,
                                                     PyFunction containingFunction,
                                                     String className) {
        StringBuilder signature = new StringBuilder();

        // 添加类名（如果存在）
        if (className != null) {
            signature.append(className).append(".");
        }

        // 添加外层函数名
        if (containingFunction != null) {
            signature.append(containingFunction.getName()).append(".");
        }

        // 添加嵌套函数名和参数
        signature.append(nestedFunction.getName()).append("(");

        var parameterList = nestedFunction.getParameterList();
        var parameters = parameterList.getParameters();

        for (int i = 0; i < parameters.length; i++) {
            if (i > 0) {
                signature.append(", ");
            }
            signature.append(parameters[i].getName());
        }

        // 添加标识表明这是内部函数
        signature.append(") [inner]");

        return signature.toString();
    }

    /**
     * 创建函数/方法节点的通用方法
     */
    public static AnalysisNode createFunctionNode(PyFunction function, boolean isExternal,
                                                String moduleInfo, String realFilePath) {
        String className = PythonPsiUtils.getContainingClassName(function);
        AnalysisNode.NodeType nodeType = determineFunctionNodeType(function);
        String signature = buildFunctionSignature(function, className);

        // 使用新的通用模块解析器生成更准确的ID
        String fullId = isExternal ?
            PythonModuleResolver.buildFullElementId(function, className) :
            PythonPsiUtils.buildFullFunctionId(function, className);

        // 使用模块解析器获取更准确的包名和模块类型
        String packageName;
        ModuleType moduleType;
        if (isExternal) {
            packageName = moduleInfo;
            moduleType = PythonModuleResolver.inferModuleTypeFromString(moduleInfo);
        } else {
            PythonModuleResolver.ModuleInfo modInfo = PythonModuleResolver.resolveModuleInfo(function);
            packageName = modInfo.packageName();
            moduleType = modInfo.type();
        }

        return new AnalysisNode(
                fullId,
                nodeType,
                function.getName(),
                className,
                packageName,
                PythonPsiUtils.getLineNumber(function),
                signature,
                packageName,
                realFilePath,
                Language.PYTHON.getCode(),
                moduleType
        );
    }

    /**
     * 创建类节点的通用方法
     */
    public static AnalysisNode createClassNode(PyClass pyClass, boolean isExternal,
                                             String moduleInfo, String realFilePath) {
        // 使用与 PythonStructureAnalyzer 相同的 signature 生成策略
        String qualifiedName = pyClass.getQualifiedName();
        String calleeSignature = qualifiedName != null ? qualifiedName : pyClass.getName();

        // 使用新的通用模块解析器生成更准确的ID和包名
        String calleeFullId = isExternal ?
            PythonModuleResolver.buildFullClassId(pyClass) :
            PythonPsiUtils.getPackageName(pyClass) + "." + pyClass.getName();

        // 使用模块信息作为包名和模块类型
        String packageName;
        ModuleType moduleType;
        if (isExternal) {
            packageName = moduleInfo;
            moduleType = PythonModuleResolver.inferModuleTypeFromString(moduleInfo);
        } else {
            PythonModuleResolver.ModuleInfo modInfo = PythonModuleResolver.resolveModuleInfo(pyClass);
            packageName = modInfo.packageName();
            moduleType = modInfo.type();
        }

        return new AnalysisNode(
                calleeFullId,
                AnalysisNode.NodeType.CLASS,
                pyClass.getName(),
                pyClass.getName(),
                packageName,
                PythonPsiUtils.getLineNumber(pyClass),
                calleeSignature,
                packageName,
                realFilePath,
                Language.PYTHON.getCode(),
                moduleType
        );
    }

    /**
     * 从完整限定名中提取模块信息
     */
    public static String extractModuleFromQualifiedName(String qualifiedName) {
        try {
            if (qualifiedName == null || qualifiedName.isEmpty()) {
                return "UNKNOWN_MODULE";
            }

            // 对于形如 "torch._tensor.Tensor.detach" 的完整限定名
            // 提取模块部分 "torch._tensor"
            int lastDotIndex = qualifiedName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                String moduleAndClass = qualifiedName.substring(0, lastDotIndex);
                // 再找倒数第二个点，将类名也分离出来
                int secondLastDotIndex = moduleAndClass.lastIndexOf('.');
                if (secondLastDotIndex > 0) {
                    return moduleAndClass.substring(0, secondLastDotIndex);
                } else {
                    // 只有一层，就是模块名
                    return moduleAndClass;
                }
            }

            return qualifiedName;
        } catch (Exception e) {
            LOG.debug("Error extracting module from qualified name: " + qualifiedName + " - " + e.getMessage());
            return "UNKNOWN_MODULE";
        }
    }

    /**
     * 从已解析的元素中提取信息
     */
    public static String extractInfoFromResolvedElement(PsiElement element) {
        try {
            StringBuilder info = new StringBuilder();

            // 添加元素类型
            info.append("type:").append(element.getClass().getSimpleName());

            // 添加元素文本（限制长度）
            String elementText = element.getText();
            if (elementText != null && !elementText.isEmpty()) {
                String shortText = elementText.length() > 50 ?
                        elementText.substring(0, 50) + "..." : elementText;
                info.append(", text:").append(shortText);
            }

            // 如果是 PyTargetExpression，尝试获取更多信息
            if (element instanceof PyTargetExpression targetExpr) {
                String qualifiedName = targetExpr.getQualifiedName();
                if (qualifiedName != null && !qualifiedName.isEmpty()) {
                    info.append(", qualified:").append(qualifiedName);
                }
            }

            // 添加所在文件信息
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null) {
                info.append(", file:").append(containingFile.getName());
            }

            return info.toString();
        } catch (Exception e) {
            LOG.debug("Error extracting info from resolved element: " + e.getMessage());
            return "";
        }
    }
}