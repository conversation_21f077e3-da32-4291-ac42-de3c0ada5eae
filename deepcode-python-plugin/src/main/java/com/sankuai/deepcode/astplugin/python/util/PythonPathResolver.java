package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Python 路径解析通用工具类
 * 统一处理第三方库路径、项目路径、相对路径等逻辑
 * 
 * <AUTHOR>
 */
public final class PythonPathResolver {
    
    private static final Logger LOG = Logger.getInstance(PythonPathResolver.class);
    
    // 第三方库路径标识符
    private static final String[] THIRD_PARTY_PATTERNS = {
        "site-packages", "venv", ".venv", "virtualenv", 
        "__pycache__", ".cache", "node_modules"
    };
    
    // 标准库路径标识符
    private static final String[] STDLIB_PATTERNS = {
        "/lib/python", "/Library/Frameworks/Python.framework",
        "\\Lib\\", "\\lib\\python", "typeshed", "stubs", "python_stubs"
    };
    
    private PythonPathResolver() {
        // 工具类不允许实例化
    }
    
    /**
     * 检查路径是否为第三方库路径
     */
    public static boolean isThirdPartyPath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        
        for (String pattern : THIRD_PARTY_PATTERNS) {
            if (filePath.contains(pattern)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查路径是否为标准库路径
     */
    public static boolean isStandardLibraryPath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        
        // 特殊处理内置模块文件
        if (filePath.endsWith("builtins.py") || filePath.endsWith("builtins.pyi")) {
            return true;
        }
        
        // 检查是否为已知的标准库模块文件
        if (isKnownStandardLibraryFile(filePath)) {
            return true;
        }
        
        for (String pattern : STDLIB_PATTERNS) {
            if (filePath.contains(pattern) && !filePath.contains("site-packages")) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查文件名是否为已知的标准库模块
     */
    private static boolean isKnownStandardLibraryFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        
        // 提取文件名（不包含路径）
        String fileName = extractFileNameFromPath(filePath);

        // 移除文件扩展名
        String moduleName = fileName;
        if (fileName.endsWith(".py")) {
            moduleName = fileName.substring(0, fileName.length() - 3);
        } else if (fileName.endsWith(".pyi")) {
            moduleName = fileName.substring(0, fileName.length() - 4);
        }
        
        // 使用 IntelliJ 的 Python 插件 API 检查是否为标准库模块
        return PythonModulePatternMatcher.isPythonStandardLibraryModule(moduleName);
    }
    
    /**
     * 检查路径是否为项目内部路径
     */
    public static boolean isProjectInternalPath(String filePath, String projectBasePath) {
        if (filePath == null || projectBasePath == null) {
            return false;
        }
        
        // 检查文件是否在项目目录下
        boolean isInProjectDir = filePath.startsWith(projectBasePath);
        
        // 排除第三方库目录
        boolean isThirdParty = isThirdPartyPath(filePath);
        
        return isInProjectDir && !isThirdParty;
    }
    
    /**
     * 从第三方库完整路径中提取相对路径
     */
    public static String extractThirdPartyRelativePath(String fullPath) {
        try {
            // 查找site-packages位置
            int sitePackagesIndex = fullPath.lastIndexOf("site-packages");
            if (sitePackagesIndex != -1) {
                String afterSitePackages = fullPath.substring(sitePackagesIndex + "site-packages".length());
                
                // 移除开头的路径分隔符
                if (afterSitePackages.startsWith("/") || afterSitePackages.startsWith("\\")) {
                    afterSitePackages = afterSitePackages.substring(1);
                }
                
                if (!afterSitePackages.isEmpty()) {
                    LOG.debug("Extracted third-party relative path: " + afterSitePackages);
                    return afterSitePackages;
                }
            }
        } catch (Exception e) {
            LOG.debug("Error extracting third-party relative path from " + fullPath + ": " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 获取项目相对路径
     */
    public static String getProjectRelativePath(VirtualFile virtualFile, Project project) {
        if (virtualFile == null || project == null) {
            return null;
        }
        
        String projectBasePath = project.getBasePath();
        if (projectBasePath == null) {
            return virtualFile.getName();
        }
        
        return getProjectRelativePath(virtualFile.getPath(), projectBasePath);
    }
    
    /**
     * 获取项目相对路径（基于路径字符串）
     */
    public static String getProjectRelativePath(String fullPath, String projectBasePath) {
        if (fullPath == null || projectBasePath == null) {
            return extractFileNameFromPath(fullPath);
        }
        
        try {
            // 对于第三方库，提取相对路径
            if (isThirdPartyPath(fullPath)) {
                String relativePath = extractThirdPartyRelativePath(fullPath);
                return relativePath != null ? relativePath : extractFileNameFromPath(fullPath);
            }
            
            // 对于项目内文件，返回项目相对路径
            if (fullPath.startsWith(projectBasePath)) {
                String relativePath = fullPath.substring(projectBasePath.length());
                // 移除开头的路径分隔符
                if (relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                    relativePath = relativePath.substring(1);
                }
                return relativePath.isEmpty() ? extractFileNameFromPath(fullPath) : relativePath;
            }
            
            return extractFileNameFromPath(fullPath);
            
        } catch (Exception e) {
            LOG.warn("Error getting project relative path for " + fullPath + ": " + e.getMessage(), e);
            return extractFileNameFromPath(fullPath);
        }
    }
    
    /**
     * 从完整路径中提取文件名
     */
    public static String extractFileNameFromPath(String fullPath) {
        if (fullPath == null || fullPath.isEmpty()) {
            return "UNKNOWN_FILE";
        }
        
        try {
            int lastSlash = Math.max(fullPath.lastIndexOf('/'), fullPath.lastIndexOf('\\'));
            return lastSlash >= 0 ? fullPath.substring(lastSlash + 1) : fullPath;
        } catch (Exception e) {
            LOG.debug("Error extracting file name from path: " + fullPath + " - " + e.getMessage());
            return "UNKNOWN_FILE";
        }
    }
    
    /**
     * 从第三方库路径中提取包名
     */
    public static String extractPackageFromThirdPartyPath(String fullPath) {
        try {
            String relativePath = extractThirdPartyRelativePath(fullPath);
            if (relativePath == null || relativePath.isEmpty()) {
                return "UNKNOWN_PACKAGE";
            }
            
            // 转换为Python包格式
            String[] parts = relativePath.replace('\\', '/').split("/");
            if (parts.length > 0) {
                // 返回第一个部分作为包名
                return parts[0];
            }
            
            return "UNKNOWN_PACKAGE";
        } catch (Exception e) {
            LOG.debug("Error extracting package from third-party path: " + fullPath + " - " + e.getMessage());
            return "UNKNOWN_PACKAGE";
        }
    }
    
    /**
     * 构建Python包路径（将文件系统路径转换为Python包格式）
     */
    public static String buildPythonPackagePath(String directoryPath) {
        if (directoryPath == null || directoryPath.isEmpty()) {
            return "";
        }
        
        try {
            // 标准化路径分隔符
            String normalizedPath = directoryPath.replace('\\', '/');
            
            // 分割路径并过滤无效部分
            String[] parts = normalizedPath.split("/");
            StringBuilder packageBuilder = new StringBuilder();
            boolean first = true;
            
            for (String part : parts) {
                // 跳过常见的非包目录和空部分
                if (isValidPackagePart(part)) {
                    if (!first) {
                        packageBuilder.append(".");
                    }
                    packageBuilder.append(part);
                    first = false;
                }
            }
            
            return packageBuilder.toString();
        } catch (Exception e) {
            LOG.debug("Error building Python package path from: " + directoryPath + " - " + e.getMessage());
            return "";
        }
    }
    
    /**
     * 检查路径部分是否为有效的包名部分
     */
    private static boolean isValidPackagePart(String part) {
        return part != null && !part.isEmpty() && 
               !"src".equals(part) && !"main".equals(part) && !"python".equals(part) &&
               !".".equals(part) && !"..".equals(part) && !"__pycache__".equals(part);
    }
    
    /**
     * 计算项目内文件的完整包路径
     */
    public static String calculateProjectPackagePath(VirtualFile virtualFile, Project project) {
        try {
            String projectBasePath = project.getBasePath();
            if (projectBasePath == null || virtualFile == null) {
                return "";
            }
            
            Path projectPath = Paths.get(projectBasePath);
            Path filePath = Paths.get(virtualFile.getPath());
            
            // 获取相对于项目根目录的路径
            Path relativePath = projectPath.relativize(filePath);
            
            // 获取目录路径（移除文件名）
            Path parentPath = relativePath.getParent();
            if (parentPath == null) {
                return "";
            }
            
            // 转换为Python包格式
            return buildPythonPackagePath(parentPath.toString());
            
        } catch (Exception e) {
            LOG.debug("Error calculating project package path for " + virtualFile.getPath() + ": " + e.getMessage());
            return "";
        }
    }
}