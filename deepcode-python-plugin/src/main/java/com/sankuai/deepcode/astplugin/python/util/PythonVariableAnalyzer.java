package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.psi.PsiElement;
import com.intellij.psi.util.PsiTreeUtil;
import com.jetbrains.python.psi.*;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.Language;

import java.util.Collection;

/**
 * Python 变量分析工具类
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonVariableAnalyzer {

    private PythonVariableAnalyzer() {
        // 工具类不允许实例化
    }

    /**
     * 分析模块级变量
     */
    public static void analyzeModuleVariables(PyFile pythonFile, AnalysisResult result) {
        // 查找模块级的赋值语句
        Collection<PyAssignmentStatement> assignments = PsiTreeUtil.findChildrenOfType(
                pythonFile, PyAssignmentStatement.class);

        for (PyAssignmentStatement assignment : assignments) {
            // 只处理模块级变量，排除类和函数内部的变量
            if (isModuleLevelVariable(assignment)) {
                analyzeVariableAssignment(assignment, result);
            }
        }
    }

    /**
     * 检查赋值语句是否在模块级别（不在类或函数内部）
     */
    public static boolean isModuleLevelVariable(PyAssignmentStatement assignment) {
        // 检查赋值语句是否在模块级别（不在类或函数内部）
        PsiElement parent = assignment.getParent();
        while (parent != null) {
            if (parent instanceof PyFunction || parent instanceof PyClass) {
                // 在函数或类内部
                return false;
            }
            if (parent instanceof PyFile) {
                // 在模块级别
                return true;
            }
            parent = parent.getParent();
        }
        return false;
    }

    /**
     * 分析变量赋值语句
     */
    public static void analyzeVariableAssignment(PyAssignmentStatement assignment, AnalysisResult result) {
        PyExpression[] targets = assignment.getTargets();

        for (PyExpression target : targets) {
            if (target instanceof PyTargetExpression targetExpr) {
                String variableName = targetExpr.getName();
                if (variableName != null) {

                    AnalysisNode variableNode = new AnalysisNode(
                            variableName,
                            AnalysisNode.NodeType.FIELD,
                            variableName,
                            // 模块级变量没有类名
                            null,
                            PythonPsiUtils.getPackageName(assignment),
                            PythonPsiUtils.getLineNumber(assignment),
                            variableName,
                            PythonPsiUtils.getPackageName(assignment),
                            PythonPsiUtils.getFilePath(assignment),
                            Language.PYTHON.getCode()
                    );

                    result.addNode(variableNode);
                }
            }
        }
    }
}