package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.projectRoots.Sdk;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.jetbrains.python.psi.PyClass;
import com.jetbrains.python.psi.PyFile;
import com.jetbrains.python.psi.PyFunction;
import com.sankuai.deepcode.astplugin.model.ModuleType;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Python 模块解析通用工具类
 * 统一处理模块名、包名、完整ID等解析逻辑
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonModuleResolver {

    private static final Logger LOG = Logger.getInstance(PythonModuleResolver.class);

    private PythonModuleResolver() {
        // 工具类不允许实例化
    }

    /**
     * 模块信息记录
     */
    public record ModuleInfo(ModuleType type, String moduleName, String packageName, String filePath) {
        public ModuleInfo(ModuleType type, String moduleName, String packageName, String filePath) {
            this.type = type != null ? type : ModuleType.UNKNOWN;
            this.moduleName = moduleName != null ? moduleName : "UNKNOWN_MODULE";
            this.packageName = packageName != null ? packageName : "UNKNOWN_PACKAGE";
            this.filePath = filePath != null ? filePath : "UNKNOWN_PATH";
        }

        /**
         * 获取格式化的模块信息
         */
        public String getFormattedModuleInfo() {
            return switch (type) {
                case STANDARD_LIBRARY -> packageName;  // 直接使用包名，不添加前缀
                case THIRD_PARTY -> packageName;
                case PROJECT, UNKNOWN -> packageName;
            };
        }
    }

    /**
     * 从PSI元素解析模块信息
     */
    public static ModuleInfo resolveModuleInfo(PsiElement element) {
        try {
            VirtualFile virtualFile = getVirtualFile(element);
            if (virtualFile == null) {
                return new ModuleInfo(ModuleType.UNKNOWN, "NO_FILE", "NO_FILE", "NO_PATH");
            }

            Project project = element.getProject();
            String filePath = virtualFile.getPath();

            // 优先尝试PSI-based解析
            ModuleInfo psiBasedInfo = resolvePsiBasedModuleInfo(element, virtualFile, project);
            if (psiBasedInfo.type() != ModuleType.UNKNOWN) {
                return psiBasedInfo;
            }

            // 回退到文件路径-based解析
            ModuleType moduleType = determineModuleType(filePath, project);

            return switch (moduleType) {
                case STANDARD_LIBRARY -> resolveStandardLibraryInfo(element, virtualFile);
                case THIRD_PARTY -> resolveThirdPartyInfo(element, virtualFile);
                case PROJECT -> resolveProjectInfo(element, virtualFile, project);
                case UNKNOWN -> new ModuleInfo(ModuleType.UNKNOWN, "UNKNOWN_MODULE", "UNKNOWN_PACKAGE", filePath);
            };

        } catch (Exception e) {
            LOG.warn("Error resolving module info for element: " + e.getMessage(), e);
            return new ModuleInfo(ModuleType.UNKNOWN, "ERROR_MODULE", "ERROR_PACKAGE", "ERROR_PATH");
        }
    }

    /**
     * 基于PSI解析模块信息 - 利用PyCharm内置的模块解析能力
     */
    private static ModuleInfo resolvePsiBasedModuleInfo(PsiElement element, VirtualFile virtualFile, Project project) {
        try {
            // 对于Python文件，尝试获取其限定名
            if (element.getContainingFile() instanceof PyFile pyFile) {
                // 尝试获取模块的限定名
                String qualifiedName = pyFile.getName();
                if (qualifiedName.endsWith(".py")) {
                    qualifiedName = qualifiedName.substring(0, qualifiedName.length() - 3);
                } else if (qualifiedName.endsWith(".pyi")) {
                    qualifiedName = qualifiedName.substring(0, qualifiedName.length() - 4);
                }

                // 检查是否为已知标准库模块
                if (PythonModulePatternMatcher.isPythonStandardLibraryModule(qualifiedName, project)) {
                    LOG.debug("PSI-based resolution: {} is standard library module", qualifiedName);
                    return new ModuleInfo(ModuleType.STANDARD_LIBRARY, qualifiedName, qualifiedName, qualifiedName);
                }

                // 对于已解析的元素，尝试从其所属SDK获取信息
                if (project != null) {
                    ModuleInfo sdkBasedInfo = resolveSdkBasedModuleInfo(qualifiedName, virtualFile.getPath(), project);
                    if (sdkBasedInfo.type() != ModuleType.UNKNOWN) {
                        return sdkBasedInfo;
                    }
                }
            }

            return new ModuleInfo(ModuleType.UNKNOWN, "UNKNOWN_MODULE", "UNKNOWN_PACKAGE", virtualFile.getPath());

        } catch (Exception e) {
            LOG.debug("Error in PSI-based module resolution: " + e.getMessage());
            return new ModuleInfo(ModuleType.UNKNOWN, "UNKNOWN_MODULE", "UNKNOWN_PACKAGE", virtualFile.getPath());
        }
    }

    /**
     * 基于SDK解析模块信息
     */
    private static ModuleInfo resolveSdkBasedModuleInfo(String moduleName, String filePath, Project project) {
        try {
            // 获取项目SDK
            Sdk pythonSdk = PythonModulePatternMatcher.getPythonSdk(project);
            if (pythonSdk == null) {
                return new ModuleInfo(ModuleType.UNKNOWN, moduleName, "UNKNOWN_PACKAGE", filePath);
            }

            String sdkHomePath = pythonSdk.getHomePath();
            if (sdkHomePath == null) {
                return new ModuleInfo(ModuleType.UNKNOWN, moduleName, "UNKNOWN_PACKAGE", filePath);
            }

            // 检查文件路径是否在SDK的标准库路径下
            Path sdkPath = Paths.get(sdkHomePath);
            Path filePaths = Paths.get(filePath);

            // 检查是否在SDK的lib目录下
            if (filePaths.toString().contains(sdkPath.toString()) &&
                    (filePaths.toString().contains("lib/python") || filePaths.toString().contains("Lib\\"))) {
                LOG.debug("SDK-based resolution: {} is standard library module (path: {})", moduleName, filePath);
                return new ModuleInfo(ModuleType.STANDARD_LIBRARY, moduleName, moduleName, moduleName);
            }

            return new ModuleInfo(ModuleType.UNKNOWN, moduleName, "UNKNOWN_PACKAGE", filePath);

        } catch (Exception e) {
            LOG.debug("Error in SDK-based module resolution: " + e.getMessage());
            return new ModuleInfo(ModuleType.UNKNOWN, moduleName, "UNKNOWN_PACKAGE", filePath);
        }
    }

    /**
     * 确定模块类型
     */
    public static ModuleType determineModuleType(String filePath, Project project) {
        if (filePath == null) {
            return ModuleType.UNKNOWN;
        }

        // 检查是否为标准库
        if (PythonPathResolver.isStandardLibraryPath(filePath)) {
            return ModuleType.STANDARD_LIBRARY;
        }

        // 检查是否为第三方库
        if (PythonPathResolver.isThirdPartyPath(filePath)) {
            return ModuleType.THIRD_PARTY;
        }

        // 检查是否为项目内部
        String projectBasePath = project != null ? project.getBasePath() : null;
        if (PythonPathResolver.isProjectInternalPath(filePath, projectBasePath)) {
            return ModuleType.PROJECT;
        }

        return ModuleType.UNKNOWN;
    }

    /**
     * 解析标准库信息
     */
    private static ModuleInfo resolveStandardLibraryInfo(PsiElement element, VirtualFile virtualFile) {
        String moduleName = extractModuleName(element);
        // 直接使用模块名作为包名，不添加PYTHON_STDLIB前缀
        return new ModuleInfo(ModuleType.STANDARD_LIBRARY, moduleName, moduleName, moduleName);
    }

    /**
     * 解析第三方库信息
     */
    private static ModuleInfo resolveThirdPartyInfo(PsiElement element, VirtualFile virtualFile) {
        String filePath = virtualFile.getPath();
        String packageName = PythonPathResolver.extractPackageFromThirdPartyPath(filePath);
        String relativePath = PythonPathResolver.extractThirdPartyRelativePath(filePath);
        String moduleName = extractModuleName(element);

        return new ModuleInfo(ModuleType.THIRD_PARTY, moduleName, packageName,
                relativePath != null ? relativePath : virtualFile.getName());
    }

    /**
     * 解析项目模块信息
     */
    private static ModuleInfo resolveProjectInfo(PsiElement element, VirtualFile virtualFile, Project project) {
        String packagePath = PythonPathResolver.calculateProjectPackagePath(virtualFile, project);
        String moduleName = extractModuleName(element);

        // 构建完整的模块路径
        String fullModuleName;
        if (packagePath.isEmpty()) {
            fullModuleName = moduleName;
        } else {
            // 特殊处理 __init__.py 文件：__init__.py 代表包本身，不应该在路径中显示 __init__
            if ("__init__".equals(moduleName)) {
                // 直接使用包路径，不加 .__init__
                fullModuleName = packagePath;
            } else {
                fullModuleName = packagePath + "." + moduleName;
            }
        }

        String relativePath = PythonPathResolver.getProjectRelativePath(virtualFile, project);

        return new ModuleInfo(ModuleType.PROJECT, moduleName, fullModuleName,
                relativePath != null ? relativePath : virtualFile.getName());
    }

    /**
     * 提取模块名
     */
    public static String extractModuleName(PsiElement element) {
        PsiFile containingFile = element.getContainingFile();
        if (containingFile instanceof PyFile) {
            return extractModuleNameFromFileName(containingFile.getName());
        }
        return "Unknown";
    }

    /**
     * 从文件名提取模块名
     */
    public static String extractModuleNameFromFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "Unknown";
        }

        // 处理不同的Python文件扩展名
        if (fileName.endsWith(".py")) {
            return fileName.substring(0, fileName.length() - 3);
        } else if (fileName.endsWith(".pyi") || fileName.endsWith(".pyw")) {
            return fileName.substring(0, fileName.length() - 4);
        } else {
            // 其他情况，移除最后一个点后的扩展名
            int lastDot = fileName.lastIndexOf('.');
            return lastDot > 0 ? fileName.substring(0, lastDot) : fileName;
        }
    }

    /**
     * 获取PSI元素的VirtualFile
     */
    private static VirtualFile getVirtualFile(PsiElement element) {
        if (element == null) {
            return null;
        }

        PsiFile containingFile = element.getContainingFile();
        return containingFile != null ? containingFile.getVirtualFile() : null;
    }

    /**
     * 构建完整的函数/方法ID
     */
    public static String buildFullElementId(PyFunction function, String className) {
        ModuleInfo moduleInfo = resolveModuleInfo(function);
        StringBuilder fullId = new StringBuilder();

        // 添加模块路径
        fullId.append(moduleInfo.packageName());

        // 添加类名（如果存在）
        if (className != null && !className.isEmpty()) {
            fullId.append(".").append(className);
        }

        // 处理嵌套函数的情况
        if (PythonPsiUtils.isInnerFunction(function)) {
            PyFunction containingFunction = PythonPsiUtils.getContainingFunction(function);
            if (containingFunction != null) {
                fullId.append(".").append(containingFunction.getName());
            }
        }

        // 添加函数名
        fullId.append(".").append(function.getName());

        return fullId.toString();
    }

    /**
     * 构建完整的类ID
     */
    public static String buildFullClassId(PyClass pyClass) {
        ModuleInfo moduleInfo = resolveModuleInfo(pyClass);
        return moduleInfo.packageName() + "." + pyClass.getName();
    }

    /**
     * 根据模块信息字符串推断 ModuleType
     */
    public static ModuleType inferModuleTypeFromString(String moduleInfo) {
        if (moduleInfo == null || moduleInfo.isEmpty()) {
            return ModuleType.UNKNOWN;
        }

        // 根据模块信息的前缀判断类型
        if (moduleInfo.startsWith("PYTHON_STDLIB.")) {
            return ModuleType.STANDARD_LIBRARY;
        } else if (moduleInfo.startsWith("THIRD_PARTY.")) {
            return ModuleType.THIRD_PARTY;
        } else if (moduleInfo.startsWith("PROJECT.")) {
            return ModuleType.PROJECT;
        } else if ("builtins".equals(moduleInfo) || "BUILTIN".equals(moduleInfo)) {
            return ModuleType.STANDARD_LIBRARY;
        } else {
            // 其他情况，根据内容特征判断
            if (moduleInfo.contains("site-packages") || moduleInfo.matches("^[a-z]+$")) {
                return ModuleType.THIRD_PARTY;
            } else {
                return ModuleType.UNKNOWN;
            }
        }
    }
}