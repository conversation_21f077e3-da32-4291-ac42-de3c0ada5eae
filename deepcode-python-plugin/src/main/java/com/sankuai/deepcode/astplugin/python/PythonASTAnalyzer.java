package com.sankuai.deepcode.astplugin.python;

import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiFile;
import com.jetbrains.python.psi.PyFile;
import com.sankuai.deepcode.astplugin.analyzer.ASTAnalyzer;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.Language;
import com.sankuai.deepcode.astplugin.python.util.*;

/**
 * Python语言专用的AST分析器
 * 重构后的版本，使用静态工具类来处理具体分析任务
 *
 * <AUTHOR>
 */
public class PythonASTAnalyzer implements ASTAnalyzer {

    private static final Logger LOG = Logger.getInstance(PythonASTAnalyzer.class);

    @Override
    public boolean supports(PsiFile psiFile) {
        if (psiFile == null) {
            return false;
        }

        // 检查文件类型
        boolean isPyFile = psiFile instanceof PyFile;

        // 检查文件扩展名
        String fileName = psiFile.getName();
        boolean hasCorrectExtension = fileName.endsWith(".py");

        // 检查语言ID
        String languageId = psiFile.getLanguage().getID();
        boolean isCorrectLanguage = "Python".equals(languageId);

        boolean supports = isPyFile || hasCorrectExtension || isCorrectLanguage;

        LOG.info("Python analyzer supports check for file: " + fileName +
                ", isPyFile: " + isPyFile +
                ", hasCorrectExtension: " + hasCorrectExtension +
                ", languageId: " + languageId +
                ", isCorrectLanguage: " + isCorrectLanguage +
                ", result: " + supports);

        return supports;
    }

    @Override
    public Language getSupportedLanguage() {
        return Language.PYTHON;
    }

    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        if (!supports(psiFile)) {
            AnalysisResult result = new AnalysisResult(psiFile.getName(), "Python");
            result.addError("File is not a Python file");
            return result;
        }

        return ReadAction.compute(() -> {
            try {
                LOG.info("Starting Python AST analysis for file: " + psiFile.getName());

                PyFile pythonFile = (PyFile) psiFile;
                AnalysisResult result = new AnalysisResult(pythonFile.getName(), Language.PYTHON);

                // 首先创建模块节点
                try {
                    PythonStructureAnalyzer.analyzeModule(pythonFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze module: " + e.getMessage());
                    result.addError("Module analysis failed: " + e.getMessage());
                }

                // 分析导入语句
                try {
                    PythonImportAnalyzer.analyzeImports(pythonFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze imports: " + e.getMessage());
                    result.addError("Import analysis failed: " + e.getMessage());
                }

                // 分析模块级变量
                try {
                    PythonVariableAnalyzer.analyzeModuleVariables(pythonFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze module variables: " + e.getMessage());
                    result.addError("Variable analysis failed: " + e.getMessage());
                }

                // 分析类结构（只解析第一层）
                try {
                    PythonStructureAnalyzer.analyzeTopLevelClasses(pythonFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze top-level classes: " + e.getMessage());
                    result.addError("Class analysis failed: " + e.getMessage());
                }

                // 分析函数（只解析第一层）
                try {
                    PythonStructureAnalyzer.analyzeTopLevelFunctions(pythonFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze top-level functions: " + e.getMessage());
                    result.addError("Function analysis failed: " + e.getMessage());
                }

                // 分析调用关系
                try {
                    PythonCallAnalyzer.analyzeCallRelations(pythonFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze call relations: " + e.getMessage());
                    result.addError("Call analysis failed: " + e.getMessage());
                }

                // 更新统计信息
                try {
                    PythonStatisticsAnalyzer.updateStatistics(pythonFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to update statistics: " + e.getMessage());
                    result.addError("Statistics analysis failed: " + e.getMessage());
                }

                LOG.info("Python AST analysis completed. Nodes: " + result.getNodes().size() +
                        ", Relations: " + result.getCallRelations().size() +
                        (result.getErrors().isEmpty() ? " (no errors)" : ", Errors: " + result.getErrors().size()));

                return result;

            } catch (Exception e) {
                LOG.error("Error during Python AST analysis", e);
                AnalysisResult errorResult = new AnalysisResult(psiFile.getName(), Language.PYTHON);
                errorResult.addError("Analysis failed: " + e.getMessage());
                return errorResult;
            }
        });
    }
}