package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.util.PsiTreeUtil;
import com.jetbrains.python.psi.PyClass;
import com.jetbrains.python.psi.PyFile;
import com.jetbrains.python.psi.PyFunction;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.Language;

import java.util.List;

/**
 * Python 结构分析工具类 (类和函数分析)
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonStructureAnalyzer {

    private static final Logger LOG = Logger.getInstance(PythonStructureAnalyzer.class);

    private PythonStructureAnalyzer() {
        // 工具类不允许实例化
    }

    /**
     * 分析模块本身，创建模块节点
     */
    public static void analyzeModule(PyFile pythonFile, AnalysisResult result) {
        try {
            // 获取模块的完整包名路径
            String moduleFullPath = PythonPsiUtils.getPackageName(pythonFile);

            // 检查是否已存在模块节点（避免重复创建）
            if (result.getNodes().containsKey(moduleFullPath)) {
                return;
            }

            // 创建模块节点，使用完整包名作为ID和signature
            String moduleName = PythonPsiUtils.getModuleName(pythonFile);
            AnalysisNode moduleNode = new AnalysisNode(
                    // 使用完整包名作为ID
                    moduleFullPath,
                    AnalysisNode.NodeType.MODULE,
                    // 显示名称使用模块名
                    moduleName,
                    // 模块没有类名
                    null,
                    // 包名就是自己的全路径
                    moduleFullPath,
                    // 模块从第1行开始
                    1,
                    // signature也使用完整包名
                    moduleFullPath,
                    moduleFullPath,
                    PythonPsiUtils.getFilePath(pythonFile),
                    Language.PYTHON.getCode()
            );

            result.addNode(moduleNode);
            LOG.info("Created module node: " + moduleFullPath +
                    " for file: " + pythonFile.getName());
        } catch (Exception e) {
            LOG.warn("Error creating module node for file: " + pythonFile.getName() + " - " + e.getMessage(), e);
        }
    }

    /**
     * 分析顶层类结构（只解析第一层）
     */
    public static void analyzeTopLevelClasses(PyFile pythonFile, AnalysisResult result) {
        List<PyClass> classes = pythonFile.getTopLevelClasses();

        for (PyClass pyClass : classes) {
            analyzeTopLevelClass(pyClass, result);
        }
    }

    /**
     * 分析顶层函数（只解析第一层）
     */
    public static void analyzeTopLevelFunctions(PyFile pythonFile, AnalysisResult result) {
        List<PyFunction> functions = pythonFile.getTopLevelFunctions();

        for (PyFunction function : functions) {
            analyzeTopLevelFunction(function, result);
        }
    }

    /**
     * 分析单个顶层类
     */
    public static void analyzeTopLevelClass(PyClass pyClass, AnalysisResult result) {
        try {
            String className = pyClass.getName();
            if (className == null) {
                return;
            }

            // 统一使用 PythonPsiUtils.getPackageName() + "." + className 作为ID
            // 这样与 findOrCreateClassNode 方法保持一致，避免重复创建
            String packageName = PythonPsiUtils.getPackageName(pyClass);
            String classFullId = packageName + "." + className;

            // 检查是否已存在该类节点（避免重复创建）
            if (result.getNodes().containsKey(classFullId)) {
                LOG.debug("Class node already exists: " + classFullId);
                return;
            }

            // 提前获取所有PSI相关信息，避免后续访问失效的PSI元素
            String qualifiedName;
            int lineNumber;
            String filePath;
            PyFunction[] methods;

            try {
                qualifiedName = pyClass.getQualifiedName();
                lineNumber = PythonPsiUtils.getLineNumber(pyClass);
                filePath = PythonPsiUtils.getFilePath(pyClass);
                methods = pyClass.getMethods();
            } catch (Exception e) {
                LOG.warn("Failed to access PSI properties for class: " + className + " - " + e.getMessage());
                result.addError("Analysis failed: Element: " + pyClass.getClass().getSimpleName() +
                        " because: " + e.getMessage());
                return;
            }

            String signature = qualifiedName != null ? qualifiedName : className;

            AnalysisNode classNode = new AnalysisNode(
                    classFullId,
                    AnalysisNode.NodeType.CLASS,
                    className,
                    className,
                    packageName,
                    lineNumber,
                    signature,
                    packageName,
                    filePath,
                    Language.PYTHON.getCode()
            );

            result.addNode(classNode);
            LOG.debug("Created class node: " + classFullId + " with signature: " + signature);

            // 只分析类中的第一层方法，不分析嵌套类或嵌套函数
            for (PyFunction method : methods) {
                try {
                    analyzeClassMethod(method, result, className);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze method: " + method.getName() + " in class: " + className + " - " + e.getMessage());
                    result.addError("Analysis failed: Method analysis failed for " + method.getName() + ": " + e.getMessage());
                }
            }
        } catch (Exception e) {
            LOG.error("Error analyzing top-level class: " + e.getMessage(), e);
            result.addError("Analysis failed: Element: " + pyClass.getClass().getSimpleName() +
                    " because: " + e.getMessage());
        }
    }

    /**
     * 分析单个顶层函数
     */
    public static void analyzeTopLevelFunction(PyFunction function, AnalysisResult result) {
        try {
            String functionName = function.getName();
            if (functionName == null) {
                return;
            }

            // 提前获取所有PSI相关信息，避免后续访问失效的PSI元素
            String signature;
            String fullId;
            String packageName;
            int lineNumber;
            String filePath;

            try {
                signature = PythonPsiUtils.buildFunctionSignature(function, null);
                fullId = PythonPsiUtils.buildFullFunctionId(function, null);
                packageName = PythonPsiUtils.getPackageName(function);
                lineNumber = PythonPsiUtils.getLineNumber(function);
                filePath = PythonPsiUtils.getFilePath(function);
            } catch (Exception e) {
                LOG.warn("Failed to access PSI properties for function: " + functionName + " - " + e.getMessage());
                result.addError("Analysis failed: Function " + functionName + " because: " + e.getMessage());
                return;
            }

            AnalysisNode functionNode = new AnalysisNode(
                    fullId,
                    AnalysisNode.NodeType.FUNCTION,
                    functionName,
                    // 顶层函数没有类名
                    null,
                    packageName,
                    lineNumber,
                    signature,
                    packageName,
                    filePath,
                    Language.PYTHON.getCode()
            );

            result.addNode(functionNode);

            // 分析嵌套函数
            try {
                analyzeNestedFunctions(function, result);
            } catch (Exception e) {
                LOG.warn("Failed to analyze nested functions for: " + functionName + " - " + e.getMessage());
                result.addError("Analysis failed: Nested function analysis failed for " + functionName + ": " + e.getMessage());
            }
        } catch (Exception e) {
            LOG.error("Error analyzing top-level function: " + e.getMessage(), e);
            result.addError("Analysis failed: Element: " + function.getClass().getSimpleName() +
                    " because: " + e.getMessage());
        }
    }

    /**
     * 分析类方法
     */
    public static void analyzeClassMethod(PyFunction method, AnalysisResult result, String className) {
        try {
            String methodName = method.getName();
            if (methodName == null) {
                return;
            }

            // 提前获取所有PSI相关信息，避免后续访问失效的PSI元素
            String signature;
            String fullId;
            String packageName;
            int lineNumber;
            String filePath;

            try {
                signature = PythonPsiUtils.buildFunctionSignature(method, className);
                fullId = PythonPsiUtils.buildFullFunctionId(method, className);
                packageName = PythonPsiUtils.getPackageName(method);
                lineNumber = PythonPsiUtils.getLineNumber(method);
                filePath = PythonPsiUtils.getFilePath(method);
            } catch (Exception e) {
                LOG.warn("Failed to access PSI properties for method: " + methodName + " in class: " + className + " - " + e.getMessage());
                result.addError("Analysis failed: Method " + methodName + " in " + className + " because: " + e.getMessage());
                return;
            }

            AnalysisNode methodNode = new AnalysisNode(
                    fullId,
                    AnalysisNode.NodeType.METHOD,
                    methodName,
                    className,
                    packageName,
                    lineNumber,
                    signature,
                    packageName,
                    filePath,
                    Language.PYTHON.getCode()
            );

            result.addNode(methodNode);

            // 分析嵌套函数
            try {
                analyzeNestedFunctions(method, result);
            } catch (Exception e) {
                LOG.warn("Failed to analyze nested functions for method: " + methodName + " in class: " + className + " - " + e.getMessage());
                result.addError("Analysis failed: Nested function analysis failed for method " + methodName + ": " + e.getMessage());
            }
        } catch (Exception e) {
            LOG.error("Error analyzing class method: " + e.getMessage(), e);
            result.addError("Analysis failed: Element: " + method.getClass().getSimpleName() +
                    " because: " + e.getMessage());
        }
    }

    /**
     * 查找或创建函数节点
     */
    public static AnalysisNode findOrCreateFunctionNode(String signature, PyFunction function, AnalysisResult result) {
        String className = PythonPsiUtils.getContainingClassName(function);
        String fullId = PythonPsiUtils.buildFullFunctionId(function, className);

        AnalysisNode existingNode = result.getNodes().get(fullId);
        if (existingNode != null) {
            return existingNode;
        }

        // 确定节点类型：检查是否为嵌套函数、类方法或普通函数
        AnalysisNode.NodeType nodeType;
        String finalSignature = signature;

        if (PythonPsiUtils.isInnerFunction(function)) {
            // 嵌套函数
            nodeType = AnalysisNode.NodeType.INNER_FUNCTION;
            PyFunction containingFunction = PythonPsiUtils.getContainingFunction(function);
            finalSignature = buildNestedFunctionSignature(function, containingFunction, className);
        } else if (className != null) {
            // 类方法
            nodeType = AnalysisNode.NodeType.METHOD;
        } else {
            // 普通顶层函数
            nodeType = AnalysisNode.NodeType.FUNCTION;
        }

        AnalysisNode node = new AnalysisNode(
                fullId,
                nodeType,
                function.getName(),
                className,
                PythonPsiUtils.getModuleName(function),
                PythonPsiUtils.getLineNumber(function),
                finalSignature,
                PythonPsiUtils.getModuleName(function),
                PythonPsiUtils.getFilePath(function),
                Language.PYTHON.getCode()
        );

        result.addNode(node);

        // 如果是嵌套函数，记录日志
        if (nodeType == AnalysisNode.NodeType.INNER_FUNCTION) {
            PyFunction containingFunction = PythonPsiUtils.getContainingFunction(function);
            LOG.info("Created inner function node: " + fullId + " in " +
                    (containingFunction != null ? containingFunction.getName() : "unknown"));
        }

        return node;
    }

    /**
     * 查找或创建类节点
     */
    public static AnalysisNode findOrCreateClassNode(String classFullId, PyClass pyClass, AnalysisResult result) {
        AnalysisNode existingNode = result.getNodes().get(classFullId);
        if (existingNode != null) {
            return existingNode;
        }

        String className = pyClass.getName();
        // 使用与 analyzeTopLevelClass 相同的 signature 生成策略
        String qualifiedName = pyClass.getQualifiedName();
        String signature = qualifiedName != null ? qualifiedName : className;

        AnalysisNode classNode = new AnalysisNode(
                classFullId,
                AnalysisNode.NodeType.CLASS,
                className,
                className,
                PythonPsiUtils.getPackageName(pyClass),
                PythonPsiUtils.getLineNumber(pyClass),
                signature,
                PythonPsiUtils.getPackageName(pyClass),
                PythonPsiUtils.getFilePath(pyClass),
                Language.PYTHON.getCode()
        );

        result.addNode(classNode);
        LOG.debug("Created class node via findOrCreate: " + classFullId + " with signature: " + signature);
        return classNode;
    }

    /**
     * 分析嵌套函数（递归分析函数内部定义的函数）
     */
    public static void analyzeNestedFunctions(PyFunction outerFunction, AnalysisResult result) {
        try {
            // 查找外层函数内定义的所有嵌套函数
            PyFunction[] nestedFunctions = PsiTreeUtil.getChildrenOfType(outerFunction, PyFunction.class);

            if (nestedFunctions != null) {
                for (PyFunction nestedFunction : nestedFunctions) {
                    analyzeIndividualNestedFunction(nestedFunction, result);
                }
            }
        } catch (Exception e) {
            LOG.warn("Error analyzing nested functions in: " + outerFunction.getName() + " - " + e.getMessage(), e);
        }
    }

    /**
     * 分析单个嵌套函数
     */
    public static void analyzeIndividualNestedFunction(PyFunction nestedFunction, AnalysisResult result) {
        String functionName = nestedFunction.getName();
        if (functionName == null) {
            return;
        }

        // 确认这确实是一个嵌套函数
        if (!PythonPsiUtils.isInnerFunction(nestedFunction)) {
            return;
        }

        // 获取外层函数和类信息
        PyFunction containingFunction = PythonPsiUtils.getContainingFunction(nestedFunction);
        String className = PythonPsiUtils.getContainingClassName(nestedFunction);

        // 为嵌套函数构建特殊的签名，包含外层函数信息
        String signature = buildNestedFunctionSignature(nestedFunction, containingFunction, className);
        String fullId = PythonPsiUtils.buildFullFunctionId(nestedFunction, className);

        AnalysisNode nestedFunctionNode = new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.INNER_FUNCTION,
                functionName,
                className,
                PythonPsiUtils.getPackageName(nestedFunction),
                PythonPsiUtils.getLineNumber(nestedFunction),
                signature,
                PythonPsiUtils.getPackageName(nestedFunction),
                PythonPsiUtils.getFilePath(nestedFunction),
                Language.PYTHON.getCode()
        );

        result.addNode(nestedFunctionNode);
        LOG.info("Added inner function: " + fullId + " in " +
                (containingFunction != null ? containingFunction.getName() : "unknown"));

        // 递归分析更深层的嵌套函数
        analyzeNestedFunctions(nestedFunction, result);
    }

    /**
     * 构建嵌套函数的特殊签名，显示它在外层函数中的位置
     */
    private static String buildNestedFunctionSignature(PyFunction nestedFunction,
                                                       PyFunction containingFunction,
                                                       String className) {
        StringBuilder signature = new StringBuilder();

        // 添加类名（如果存在）
        if (className != null) {
            signature.append(className).append(".");
        }

        // 添加外层函数名
        if (containingFunction != null) {
            signature.append(containingFunction.getName()).append(".");
        }

        // 添加嵌套函数名和参数
        signature.append(nestedFunction.getName()).append("(");

        var parameterList = nestedFunction.getParameterList();
        var parameters = parameterList.getParameters();

        for (int i = 0; i < parameters.length; i++) {
            if (i > 0) {
                signature.append(", ");
            }
            signature.append(parameters[i].getName());
        }

        // 添加标识表明这是内部函数
        signature.append(") [inner]");

        return signature.toString();
    }
}