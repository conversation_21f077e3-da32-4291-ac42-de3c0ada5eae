package com.sankuai.deepcode.astplugin.python.export;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.roots.ProjectRootManager;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.sankuai.deepcode.astplugin.analyzer.AnalyzerService;
import com.sankuai.deepcode.astplugin.export.ExportOption;
import com.sankuai.deepcode.astplugin.export.ExportService;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.python.export.exporter.AnalysisFileResult;
import com.sankuai.deepcode.astplugin.python.export.exporter.PythonExporterFactory;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Python 语言导出服务实现
 *
 * <AUTHOR>
 */
public class PythonExportService implements ExportService {

    @Override
    public @NotNull String getLanguageName() {
        return "Python";
    }

    @Override
    public @NotNull List<ExportOption> getSupportedExportOptions() {
        return Arrays.asList(PythonExporterFactory.getSupportedExportOptions());
    }

    @Override
    public void exportProject(@NotNull Project project, @NotNull ExportOption exportOption, @NotNull VirtualFile outputDir) {
        // 第一步：弹出窗口让用户选择导出目录（这个参数已经由调用方处理了）
        ProgressManager.getInstance().run(new Task.Backgroundable(project, "Exporting Python AST Report", true) {
            @Override
            public void run(@NotNull ProgressIndicator progressIndicator) {
                try {
                    // 第二步：进行整体的报告分析
                    List<AnalysisFileResult> analysisFileResults = performCompleteAnalysis(project, progressIndicator);

                    if (progressIndicator.isCanceled()) {
                        return;
                    }

                    if (analysisFileResults.isEmpty()) {
                        ApplicationManager.getApplication().invokeLater(() ->
                                Messages.showInfoMessage("项目中没有找到 Python 文件", "信息"));
                        return;
                    }

                    // 第三步：依据用户选择的不同 exportOption 组装导出数据
                    progressIndicator.setText("正在组装导出数据...");
                    PythonExportDataAssembler.assembleAndExport(project, analysisFileResults, exportOption, outputDir.getPath());

                    if (progressIndicator.isCanceled()) {
                        return;
                    }

                    // 第四步：组装完成后统一弹出窗口提醒用户是否要打开
                    final int totalFiles = analysisFileResults.size();
                    ApplicationManager.getApplication().invokeLater(() -> {
                        int result = Messages.showYesNoDialog(
                                project,
                                String.format("Python AST %s 导出成功！\n\n分析了 %d 个 Python 文件。\n\n是否要打开包含文件夹？",
                                        exportOption.getDisplayName(), totalFiles),
                                "导出成功",
                                "打开文件夹",
                                "关闭",
                                Messages.getInformationIcon()
                        );

                        if (result == Messages.YES) {
                            openFolder(outputDir);
                        }
                    });

                } catch (Exception ex) {
                    ApplicationManager.getApplication().invokeLater(() ->
                            Messages.showErrorDialog("导出项目报告失败: " + ex.getMessage(), "导出错误"));
                }
            }
        });
    }

    @Override
    public boolean hasLanguageFilesInProject(@NotNull Project project) {
        return !findPythonFiles(project).isEmpty();
    }

    @Override
    public int getLanguageFileCount(@NotNull Project project) {
        return findPythonFiles(project).size();
    }

    /**
     * 执行完整的分析过程
     */
    private List<AnalysisFileResult> performCompleteAnalysis(@NotNull Project project, @NotNull ProgressIndicator progressIndicator) {
        progressIndicator.setText("正在扫描 Python 文件...");

        List<VirtualFile> pythonFiles = findPythonFiles(project);

        if (pythonFiles.isEmpty()) {
            return new ArrayList<>();
        }

        progressIndicator.setText("正在分析 " + pythonFiles.size() + " 个 Python 文件...");
        progressIndicator.setIndeterminate(false);

        return analyzeFilesInParallel(project, pythonFiles, progressIndicator);
    }

    private List<VirtualFile> findPythonFiles(Project project) {
        List<VirtualFile> pythonFiles = new ArrayList<>();
        VirtualFile[] contentRoots = ProjectRootManager.getInstance(project).getContentRoots();

        for (VirtualFile contentRoot : contentRoots) {
            collectPythonFiles(contentRoot, pythonFiles, contentRoot);
        }

        return pythonFiles;
    }

    private void collectPythonFiles(VirtualFile directory, List<VirtualFile> pythonFiles, VirtualFile projectRoot) {
        if (directory.isDirectory()) {
            String dirName = directory.getName().toLowerCase();
            String dirPath = directory.getPath();

            if (shouldSkipDirectory(dirName, dirPath, projectRoot)) {
                return;
            }

            for (VirtualFile child : directory.getChildren()) {
                if (child.isDirectory()) {
                    collectPythonFiles(child, pythonFiles, projectRoot);
                } else if ("py".equals(child.getExtension()) && !shouldSkipFile(child)) {
                    pythonFiles.add(child);
                }
            }
        }
    }

    private boolean shouldSkipDirectory(String dirName, String dirPath, VirtualFile projectRoot) {
        // Skip virtual environments
        if (dirName.equals("venv") || dirName.equals("env") || dirName.equals(".venv") ||
                dirName.equals(".env") || dirName.equals("virtualenv")) {
            return true;
        }

        // Skip site-packages and other Python library directories
        if (dirName.equals("site-packages") || dirName.equals("dist-packages") ||
                dirName.equals("lib64")) {
            return true;
        }

        // Skip conda environments
        if (dirName.equals("anaconda") || dirName.equals("anaconda3") ||
                dirName.equals("miniconda") || dirName.equals("miniconda3") ||
                dirPath.contains("/anaconda") || dirPath.contains("/miniconda")) {
            return true;
        }

        // Skip common build/cache directories
        if (dirName.equals("__pycache__") || dirName.equals(".pytest_cache") ||
                dirName.equals("build") || dirName.equals("dist") || dirName.equals(".tox") ||
                dirName.equals("node_modules") || dirName.equals(".git")) {
            return true;
        }

        // Skip hidden directories starting with dot (except .vscode, .idea which might contain project files)
        if (dirName.startsWith(".") && !dirName.equals(".vscode") && !dirName.equals(".idea")) {
            return true;
        }

        return false;
    }

    private boolean shouldSkipFile(VirtualFile file) {
        String fileName = file.getName();
        // Skip test files if they're obviously third-party
        if (fileName.startsWith("test_") && file.getPath().contains("site-packages")) {
            return true;
        }
        return false;
    }

    private List<AnalysisFileResult> analyzeFilesInParallel(Project project, List<VirtualFile> pythonFiles, ProgressIndicator progressIndicator) {
        final int THREAD_POOL_SIZE = 10;
        List<AnalysisFileResult> records = Collections.synchronizedList(new ArrayList<>());
        AtomicInteger completedCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);

        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        AnalyzerService analyzerService = AnalyzerService.getInstance();

        try {
            List<Future<Void>> futures = new ArrayList<>();

            for (VirtualFile pythonFile : pythonFiles) {
                Future<Void> future = executorService.submit(() -> {
                    try {
                        if (progressIndicator.isCanceled()) {
                            return null;
                        }

                        int completed = completedCount.incrementAndGet();
                        progressIndicator.setText("正在分析 " + pythonFile.getName() + " (" + completed + "/" + pythonFiles.size() + ")");
                        progressIndicator.setFraction((double) completed / pythonFiles.size());

                        PsiFile psiFile = ReadAction.compute(() -> PsiManager.getInstance(project).findFile(pythonFile));
                        if (psiFile != null) {
                            AnalysisResult result = analyzerService.analyze(psiFile);
                            if (result != null) {
                                AnalysisFileResult fileResult = new AnalysisFileResult(pythonFile.getPath(), result);
                                records.add(fileResult);
                            }
                        }
                    } catch (Exception ex) {
                        int errors = errorCount.incrementAndGet();
                        System.err.println("Error analyzing file " + pythonFile.getPath() + ": " + ex.getMessage());
                        if (errors <= 5) {
                            ex.printStackTrace();
                        }
                    }
                    return null;
                });
                futures.add(future);
            }

            for (Future<Void> future : futures) {
                try {
                    if (progressIndicator.isCanceled()) {
                        future.cancel(true);
                        continue;
                    }
                    future.get(30, TimeUnit.SECONDS);
                } catch (TimeoutException e) {
                    System.err.println("Analysis timeout for a file, continuing...");
                    future.cancel(true);
                } catch (Exception e) {
                    if (!progressIndicator.isCanceled()) {
                        System.err.println("Error waiting for analysis completion: " + e.getMessage());
                    }
                }
            }

        } finally {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 分析完成，错误统计在控制台已经显示过了

        return records;
    }

    private void openFolder(VirtualFile outputDir) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("mac")) {
                Runtime.getRuntime().exec("open " + outputDir.getPath());
            } else if (os.contains("win")) {
                Runtime.getRuntime().exec("explorer " + outputDir.getPath());
            } else {
                Runtime.getRuntime().exec("xdg-open " + outputDir.getPath());
            }
        } catch (IOException ex) {
            Messages.showWarningDialog("无法打开文件夹: " + ex.getMessage(), "警告");
        }
    }
}
