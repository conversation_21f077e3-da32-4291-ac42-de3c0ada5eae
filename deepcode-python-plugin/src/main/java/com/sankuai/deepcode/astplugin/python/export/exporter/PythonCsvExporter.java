package com.sankuai.deepcode.astplugin.python.export.exporter;

import com.intellij.openapi.project.Project;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Python CSV 导出器
 * 负责将分析数据导出为CSV格式
 *
 * <AUTHOR>
 */
public class PythonCsvExporter implements PythonDataExporter {

    @Override
    public void export(Project project,
                       List<AnalysisFileResult> analysisFileResults,
                       String outputDirPath,
                       String timestamp) throws IOException {
        String fileName = String.format("Python_AST_Statistics_%s.csv", timestamp);
        String filePath = outputDirPath + "/" + fileName;

        try (FileWriter writer = new FileWriter(filePath)) {
            // 写入CSV头部
            writer.append(Stream.of(
                    "filePath", "moduleName", "modulePath", "moduleCall",
                    "funcCount", "functionInnerFunc", "functionCall", "functionInnerFuncCall",
                    "classCount", "classFuncCall", "methodCount", "methodInnerMethod", "methodCall", "methodInnerMethodCall",
                    "imports", "importResolved"
            ).collect(Collectors.joining(",", "", "\n")));

            // 写入数据
            String projectBasePath = project.getBasePath();
            for (AnalysisFileResult fileResult : analysisFileResults) {
                // 直接从 AnalysisFileResult 提取数据
                String relativeFilePath = fileResult.getRelativeFilePath(projectBasePath);
                String moduleName = fileResult.getModuleName(projectBasePath);
                String modulePath = fileResult.getModulePath(projectBasePath);

                AnalysisResult result = fileResult.analysisResult();
                // 使用提取的方法计算各项统计数据
                // 直接写入CSV数据
                StringJoiner joiner = new StringJoiner(",")
                        // 写入文件路径
                        .add(escapeCsv(relativeFilePath))
                        // 写入模块名称
                        .add(escapeCsv(moduleName))
                        // 写入模块路径
                        .add(escapeCsv(modulePath))
                        // 写入模块调用数量
                        .add(String.join("/",
                                String.valueOf(calculateModuleCallCount(result)),
                                String.valueOf(calculateModuleCallCountInternal(result)),
                                String.valueOf(calculateModuleCallCountExternal(result))
                        ))
                        // 写入函数数量
                        .add(String.valueOf(calculateFunctionCount(result)))
                        // 写入内嵌函数数量
                        .add(String.valueOf(calculateInnerFunctionCount(result)))
                        // 写入函数调用数量
                        .add(String.join("/",
                                String.valueOf(calculateFunctionCallCount(result)),
                                String.valueOf(calculateFunctionCallCountInternal(result)),
                                String.valueOf(calculateFunctionCallCountExternal(result))
                        ))
                        // 写入内嵌函数调用数量
                        .add(String.valueOf(calculateInnerFunctionCallCount(result)))
                        // 写入类数量
                        .add(String.valueOf(calculateClassCount(result)))
                        // 写入类调用数量
                        .add(String.valueOf(calculateClassCallCount(result)))
                        // 写入方法数量
                        .add(String.valueOf(calculateMethodCount(result)))
                        // 写入内嵌方法数量
                        .add(String.valueOf(calculateInnerMethodCount(result)))
                        // 写入方法调用数量
                        .add(String.join("/",
                                String.valueOf(calculateMethodCallCount(result)),
                                String.valueOf(calculateMethodCallCountInternal(result)),
                                String.valueOf(calculateMethodCallCountExternal(result))
                        ))
                        // 写入内嵌方法调用数量
                        .add(String.valueOf(calculateInnerMethodCallCount(result)))
                        // 写入导入数量
                        .add(String.join("/",
                                String.valueOf(calculateImportsCount(result)),
                                String.valueOf(calculateImportsCountInternal(result)),
                                String.valueOf(calculateImportsCountExternal(result))
                        ))
                        .add(String.valueOf(calculateImportResolvedCount(result)));
                writer.append(joiner.toString()).append("\n");
            }
        }
    }

    @Override
    public String getExporterName() {
        return "CSV Statistics";
    }

    @Override
    public String getFileExtension() {
        return "csv";
    }

    // =================== 统计计算方法 ===================

    /**
     * 计算模块级调用数量
     */
    private int calculateModuleCallCount(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.MODULE;
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    private int calculateModuleCallCountInternal(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.MODULE && !relation.isExternal();
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    private int calculateModuleCallCountExternal(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.MODULE && relation.isExternal();
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    /**
     * 计算函数数量（不包括类方法）
     */
    private int calculateFunctionCount(AnalysisResult result) {
        return (int) result.getNodes().values().stream()
                .filter(node -> node.getType() == AnalysisNode.NodeType.FUNCTION)
                .filter(node -> node.getClassName() == null || node.getClassName().isEmpty())
                .count();
    }

    /**
     * 计算内嵌函数数量（在函数内部定义的函数）
     */
    private int calculateInnerFunctionCount(AnalysisResult result) {
        return (int) result.getNodes().values().stream()
                .filter(node -> node.getType() == AnalysisNode.NodeType.INNER_FUNCTION)
                .filter(node -> node.getClassName() == null || node.getClassName().isEmpty())
                .count();
    }

    /**
     * 计算函数调用数量
     */
    private int calculateFunctionCallCount(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.FUNCTION &&
                            (caller.getClassName() == null || caller.getClassName().isEmpty());
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    private int calculateFunctionCallCountInternal(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.FUNCTION &&
                            (caller.getClassName() == null || caller.getClassName().isEmpty()) &&
                            !relation.isExternal();
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    private int calculateFunctionCallCountExternal(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.FUNCTION &&
                            (caller.getClassName() == null || caller.getClassName().isEmpty()) &&
                            relation.isExternal();
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    /**
     * 计算内嵌函数调用数量
     */
    private int calculateInnerFunctionCallCount(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.INNER_FUNCTION &&
                            (caller.getClassName() == null || caller.getClassName().isEmpty());
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    /**
     * 计算类数量
     */
    private int calculateClassCount(AnalysisResult result) {
        return (int) result.getNodes().values().stream()
                .filter(node -> node.getType() == AnalysisNode.NodeType.CLASS)
                .count();
    }

    /**
     * 计算类级别的函数调用数量
     */
    private int calculateClassCallCount(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.CLASS;
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    /**
     * 计算方法数量（类中的方法）
     */
    private int calculateMethodCount(AnalysisResult result) {
        return (int) result.getNodes().values().stream()
                .filter(node -> node.getType() == AnalysisNode.NodeType.METHOD)
                .filter(node -> node.getClassName() != null && !node.getClassName().isEmpty())
                .count();
    }

    /**
     * 计算内嵌方法数量（方法内部定义的函数）
     */
    private int calculateInnerMethodCount(AnalysisResult result) {
        return (int) result.getNodes().values().stream()
                .filter(node -> node.getType() == AnalysisNode.NodeType.INNER_FUNCTION)
                .filter(node -> node.getClassName() != null && !node.getClassName().isEmpty())
                .count();
    }

    /**
     * 计算方法调用数量
     */
    private int calculateMethodCallCount(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.METHOD &&
                            caller.getClassName() != null && !caller.getClassName().isEmpty();
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    private int calculateMethodCallCountInternal(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.METHOD &&
                            caller.getClassName() != null && !caller.getClassName().isEmpty() &&
                            !relation.isExternal();
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    private int calculateMethodCallCountExternal(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.METHOD &&
                            caller.getClassName() != null && !caller.getClassName().isEmpty() &&
                            relation.isExternal();
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    /**
     * 计算内嵌方法调用数量
     */
    private int calculateInnerMethodCallCount(AnalysisResult result) {
        return result.getCallRelations().stream()
                .filter(relation -> {
                    AnalysisNode caller = relation.getCaller();
                    return caller.getType() == AnalysisNode.NodeType.INNER_FUNCTION &&
                            caller.getClassName() != null && !caller.getClassName().isEmpty();
                })
                .mapToInt(CallRelation::getCallCount)
                .sum();
    }

    /**
     * 计算导入数量
     */
    private int calculateImportsCount(AnalysisResult result) {
        return result.getImports().size();
    }

    private int calculateImportsCountInternal(AnalysisResult result) {
        return (int) result.getImports().stream()
                .filter(importInfo -> !importInfo.isExternal())
                .count();
    }

    private int calculateImportsCountExternal(AnalysisResult result) {
        return (int) result.getImports().stream()
                .filter(ImportInfo::isExternal)
                .count();
    }

    /**
     * 计算已解析的导入数量
     */
    private int calculateImportResolvedCount(AnalysisResult result) {
        return result.getImports().stream()
                .mapToInt(importInfo -> importInfo.getResolvedClasses().size())
                .sum();
    }
}