package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.jetbrains.python.psi.*;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.Language;
import com.sankuai.deepcode.astplugin.model.ModuleType;

/**
 * Python 调用表达式解析工具类
 * 专门处理不同类型PSI元素的解析、节点创建等逻辑
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonCallExpressionResolver {

    private static final Logger LOG = Logger.getInstance(PythonCallExpressionResolver.class);

    private PythonCallExpressionResolver() {
        // 工具类不允许实例化
    }

    /**
     * 解析结果容器
     */
    public record ResolveResult(AnalysisNode calleeNode, boolean isExternal) {
    }

    /**
     * 解析函数调用（PyFunction类型）
     */
    public static ResolveResult resolveFunctionCall(PyFunction resolvedFunction, PyCallExpression callExpression, AnalysisResult result) {
        PsiFile resolvedFile = resolvedFunction.getContainingFile();
        PsiFile callerFile = callExpression.getContainingFile();
        boolean isSameFile = resolvedFile != null && resolvedFile.equals(callerFile);
        boolean isProjectInternal = PythonElementResolver.isProjectInternalFunction(resolvedFunction, callExpression);

        if (isSameFile) {
            // 同文件内的函数调用 - 添加到节点列表
            String calleeSignature = PythonElementResolver.buildFunctionSignature(
                    resolvedFunction, PythonPsiUtils.getContainingClassName(resolvedFunction));
            AnalysisNode calleeNode = PythonStructureAnalyzer.findOrCreateFunctionNode(
                    calleeSignature, resolvedFunction, result);
            return new ResolveResult(calleeNode, false);
        } else if (isProjectInternal) {
            // 项目内部但不同文件的函数调用
            return createProjectInternalFunctionNode(resolvedFunction);
        } else {
            // 外部函数调用（第三方库等）
            return createExternalFunctionNode(resolvedFunction);
        }
    }

    /**
     * 解析类调用（PyClass类型）
     */
    public static ResolveResult resolveClassCall(PyClass resolvedClass, PyCallExpression callExpression, AnalysisResult result) {
        PsiFile resolvedFile = resolvedClass.getContainingFile();
        PsiFile callerFile = callExpression.getContainingFile();
        boolean isSameFile = resolvedFile != null && resolvedFile.equals(callerFile);
        boolean isProjectInternal = PythonElementResolver.isProjectInternalClass(resolvedClass, callExpression);

        if (isSameFile) {
            // 同文件内的类构造函数调用
            String classFullId = PythonPsiUtils.getPackageName(resolvedClass) + "." + resolvedClass.getName();
            AnalysisNode calleeNode = PythonStructureAnalyzer.findOrCreateClassNode(classFullId, resolvedClass, result);
            return new ResolveResult(calleeNode, false);
        } else if (isProjectInternal) {
            // 项目内部但不同文件的类构造函数调用
            return createProjectInternalClassNode(resolvedClass);
        } else {
            // 外部类构造函数调用（第三方库等）
            return createExternalClassNode(resolvedClass);
        }
    }

    /**
     * 解析目标表达式（PyTargetExpression类型）
     */
    public static ResolveResult resolveTargetExpressionCall(PyTargetExpression resolvedTarget, PyCallExpression callExpression, String calleeText) {
        LOG.debug("=== STARTING resolveTargetExpressionCall ===");
        LOG.debug("resolvedTarget.getText(): " + resolvedTarget.getText());
        LOG.debug("resolvedTarget.getName(): " + resolvedTarget.getName());
        LOG.debug("callExpression.getText(): " + callExpression.getText());
        LOG.debug("calleeText: " + calleeText);
        
        // 首先检查是否为函数参数调用
        LOG.debug("Checking isFunctionParameterCall...");
        boolean isParameterCall = isFunctionParameterCall(resolvedTarget, callExpression);
        LOG.debug("isFunctionParameterCall result: " + isParameterCall);
        
        if (isParameterCall) {
            LOG.debug("Taking parameter call path via isFunctionParameterCall");
            return createParameterCallResult(resolvedTarget, callExpression, calleeText);
        }

        // 额外检查：如果calleeText以resolvedTarget的名称开头，并且包含点号，可能是参数调用
        String targetName = resolvedTarget.getName();
        LOG.debug("Additional parameter call check: targetName=" + targetName + ", calleeText=" + calleeText);
        
        if (targetName != null && calleeText.startsWith(targetName + ".")) {
            LOG.debug("calleeText starts with targetName, checking if targetName is a parameter...");
            
            // 检查targetName是否是当前函数的参数
            PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
            LOG.debug("containingFunction: " + (containingFunction != null ? containingFunction.getName() : "null"));
            
            if (containingFunction != null) {
                try {
                    PyParameterList parameterList = containingFunction.getParameterList();
                    LOG.debug("Checking parameters in function: " + containingFunction.getName());
                    
                    for (PyParameter parameter : parameterList.getParameters()) {
                        String paramName = parameter.getName();
                        LOG.debug("Found parameter: " + paramName);
                        
                        if (paramName != null && paramName.equals(targetName)) {
                            // 这确实是参数调用，使用参数调用逻辑处理
                            LOG.debug("MATCH! Detected parameter call via calleeText analysis: " + calleeText);
                            return createParameterCallResult(resolvedTarget, callExpression, calleeText);
                        }
                    }
                    LOG.debug("No matching parameter found for targetName: " + targetName);
                } catch (Exception e) {
                    LOG.warn("Error in additional parameter call check: " + e.getMessage());
                }
            }
        } else {
            LOG.debug("calleeText does not start with targetName, continuing with non-parameter call logic");
        }

        // 获取目标表达式的上下文信息
        String resolvedQualifiedName = resolvedTarget.getQualifiedName();
        boolean isProjectInternal = PythonElementResolver.isProjectInternalElement(resolvedTarget, callExpression);

        // 构建节点信息
        String calleeSignature = PythonPsiUtils.extractMethodNameFromCall(calleeText) + "()";
        String calleeFullId;
        String calleeDisplayName;
        String moduleInfo;

        if (resolvedQualifiedName != null && !resolvedQualifiedName.isEmpty()) {
            // 使用完整限定名
            calleeFullId = resolvedQualifiedName;
            // 保持原始调用文本
            calleeDisplayName = calleeText;
            moduleInfo = PythonElementResolver.extractModuleFromQualifiedName(resolvedQualifiedName);
        } else {
            // 回退到基本信息
            calleeFullId = calleeText;
            calleeDisplayName = calleeText;
            moduleInfo = PythonPsiUtils.getPackageName(resolvedTarget);
        }

        // 获取文件路径
        String realFilePath = PythonPsiUtils.getFilePath(resolvedTarget);
        if (realFilePath == null || realFilePath.isEmpty()) {
            PsiFile resolvedFile = resolvedTarget.getContainingFile();
            realFilePath = resolvedFile != null && resolvedFile.getVirtualFile() != null ?
                    resolvedFile.getVirtualFile().getPath() : "UNKNOWN";
        }

        // 简化第三方库路径显示
        realFilePath = PythonFilePathResolver.simplifyDisplayPath(realFilePath);

        // 直接使用模块信息作为包名
        String packageName = moduleInfo;

        LOG.info("=== Creating NON-PARAMETER call node ===");
        LOG.info("calleeFullId: " + calleeFullId);
        LOG.info("calleeDisplayName: " + calleeDisplayName);
        LOG.info("calleeSignature: " + calleeSignature);
        LOG.info("moduleInfo: " + moduleInfo);
        LOG.info("packageName: " + packageName);
        LOG.info("realFilePath: " + realFilePath);
        LOG.info("isProjectInternal: " + isProjectInternal);

        // 创建属性方法节点
        AnalysisNode calleeNode = new AnalysisNode(
                calleeFullId,
                AnalysisNode.NodeType.FUNCTION,
                calleeDisplayName,
                // PyTargetExpression 通常没有明确的类名
                null,
                // 使用修正后的包名
                packageName,
                PythonPsiUtils.getLineNumber(resolvedTarget),
                calleeSignature,
                // moduleName 字段也使用修正后的包名
                packageName,
                realFilePath,
                Language.PYTHON.getCode()
        );

        LOG.info("=== Successfully created NON-PARAMETER call AnalysisNode ===");
        LOG.info("  id: " + calleeNode.getId());
        LOG.info("  name: " + calleeNode.getName());
        LOG.info("  signature: " + calleeNode.getSignature());
        LOG.info("  className: " + calleeNode.getClassName());
        LOG.info("  packageName: " + calleeNode.getPackageName());
        LOG.info("  moduleName: " + calleeNode.getModuleName());
        LOG.info("  filePath: " + calleeNode.getFilePath());

        String qualifiedInfo = resolvedQualifiedName != null ? " (qualified: " + resolvedQualifiedName + ")" : "";
        LOG.info("Created " + (isProjectInternal ? "internal" : "external") + " PyTargetExpression node: " + calleeFullId +
                " from module: " + moduleInfo + " at path: " + realFilePath + qualifiedInfo);

        return new ResolveResult(calleeNode, !isProjectInternal);
    }

    /**
     * 检查是否为通用参数调用
     */
    private static boolean isGenericParameterCall(PsiElement resolved, PyCallExpression callExpression, String calleeText) {
        try {
            // 查找包含调用表达式的函数
            PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
            if (containingFunction == null) {
                return false;
            }

            // 检查参数名是否匹配
            PyParameterList parameterList = containingFunction.getParameterList();
            for (PyParameter parameter : parameterList.getParameters()) {
                if (parameter.getName() != null && parameter.getName().equals(calleeText)) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            LOG.debug("Error checking generic parameter call: " + e.getMessage());
            return false;
        }
    }

    /**
     * 创建通用参数调用结果
     */
    private static ResolveResult createGenericParameterCallResult(PsiElement resolved, PyCallExpression callExpression, String calleeText) {
        try {
            // 获取包含函数
            PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
            if (containingFunction == null) {
                return createFallbackParameterCallResult(calleeText, callExpression);
            }

            // 安全地获取函数信息，避免PSI失效问题
            String functionName;
            String containingFunctionClassName = null;
            String modulePackageName = "PARAMETER_CALL";
            
            try {
                // 检查PSI元素是否有效
                if (!containingFunction.isValid()) {
                    LOG.warn("Containing function PSI element is invalid, using fallback");
                    return createFallbackParameterCallResult(calleeText, callExpression);
                }
                
                functionName = containingFunction.getName() != null ? containingFunction.getName() : "unknown_function";
                
                // 安全地获取包含函数的类名和包名
                try {
                    containingFunctionClassName = PythonPsiUtils.getContainingClassName(containingFunction);
                    modulePackageName = PythonPsiUtils.getPackageName(callExpression);
                } catch (Exception e) {
                    LOG.debug("Failed to get class/package info from containing function, using callExpression context: " + e.getMessage());
                    // 如果从包含函数获取信息失败，尝试从调用表达式获取
                    modulePackageName = PythonPsiUtils.getPackageName(callExpression);
                }
            } catch (Exception e) {
                LOG.warn("Error accessing containing function, using fallback: " + e.getMessage());
                return createFallbackParameterCallResult(calleeText, callExpression);
            }
            
            // 构建完整的函数路径作为ID前缀
            String functionFullPath = buildFunctionPath(containingFunction);
            
            // 从calleeText中提取参数名和方法名
            // 例如: "tokenizer.prepare_for_model" -> 参数名="tokenizer", 方法名="prepare_for_model"
            String parameterName;
            String actualMethodName;
            if (calleeText.contains(".")) {
                String[] parts = calleeText.split("\\.", 2);
                parameterName = parts[0];
                actualMethodName = parts[1];
            } else {
                parameterName = calleeText;
                actualMethodName = calleeText;
            }
            
            // 构建ID: 完整函数路径.参数名
            String calleeFullId = functionFullPath + "." + parameterName;
            // 构建签名: 应该包含完整路径的签名
            String calleeSignature = functionFullPath + "." + parameterName + "." + actualMethodName + "()";
            
            // 获取当前文件的路径
            String filePath = PythonPsiUtils.getFilePath(callExpression);

            AnalysisNode calleeNode = new AnalysisNode(
                    calleeFullId,
                    AnalysisNode.NodeType.FUNCTION,
                    // name 应该是实际被调用的方法名
                    actualMethodName,
                    // className 应该是 null，因为这是参数调用
                    null,
                    // packageName 应该是 null，因为这是参数调用
                    null,
                    PythonPsiUtils.getLineNumber(callExpression),
                    calleeSignature,
                    // moduleName 使用包含函数的模块名
                    modulePackageName,
                    filePath,
                    Language.PYTHON.getCode()
            );

            LOG.info("Created generic parameter call node: " + calleeFullId +
                    " for parameter '" + parameterName + "' calling method '" + actualMethodName + 
                    "' in function '" + functionFullPath + "'");

            // 参数调用标记为外部调用
            return new ResolveResult(calleeNode, true);
        } catch (Exception e) {
            LOG.warn("Error creating generic parameter call result: " + e.getMessage(), e);
            return createFallbackParameterCallResult(calleeText, callExpression);
        }
    }

    /**
     * 检查是否为函数参数调用
     */
    private static boolean isFunctionParameterCall(PyTargetExpression resolvedTarget, PyCallExpression callExpression) {
        try {
            // 查找包含调用表达式的函数
            PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
            if (containingFunction == null) {
                return false;
            }

            // 检查resolvedTarget的名称是否是该函数的参数
            String targetName = resolvedTarget.getName();
            if (targetName == null) {
                return false;
            }

            PyParameterList parameterList = containingFunction.getParameterList();
            for (PyParameter parameter : parameterList.getParameters()) {
                if (parameter.getName() != null && parameter.getName().equals(targetName)) {
                    // 这是一个函数参数，返回true表示它是参数调用
                    LOG.debug("Found parameter call: target '" + targetName + "' is parameter of function '" + containingFunction.getName() + "'");
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            LOG.debug("Error checking if parameter call: " + e.getMessage());
            return false;
        }
    }

    /**
     * 创建参数调用的结果
     */
    private static ResolveResult createParameterCallResult(PyTargetExpression resolvedTarget, PyCallExpression callExpression, String calleeText) {
        LOG.info("=== STARTING createParameterCallResult ===");
        LOG.info("resolvedTarget: " + resolvedTarget);
        LOG.info("callExpression: " + callExpression.getText());
        LOG.info("calleeText: " + calleeText);
        
        try {
            // 获取包含函数
            PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
            LOG.info("containingFunction: " + (containingFunction != null ? containingFunction.getName() : "null"));
            
            if (containingFunction == null) {
                LOG.info("containingFunction is null, using fallback");
                return createFallbackParameterCallResult(calleeText, callExpression);
            }

            // 获取参数名
            String parameterName = resolvedTarget.getName() != null ? resolvedTarget.getName() : calleeText;
            LOG.info("parameterName: " + parameterName);

            // 安全地获取函数信息，避免PSI失效问题
            String functionName;
            String containingClassName = null;
            String modulePackageName = "PARAMETER_CALL";
            
            try {
                // 检查PSI元素是否有效
                if (!containingFunction.isValid()) {
                    LOG.warn("Containing function PSI element is invalid, using fallback");
                    return createFallbackParameterCallResult(calleeText, callExpression);
                }
                
                functionName = containingFunction.getName() != null ? containingFunction.getName() : "unknown_function";
                
                // 安全地获取类名和包名，优先使用调用表达式的上下文
                try {
                    containingClassName = PythonPsiUtils.getContainingClassName(containingFunction);
                    modulePackageName = PythonPsiUtils.getPackageName(callExpression);
                } catch (Exception e) {
                    LOG.debug("Failed to get class/package info from containing function, using callExpression context: " + e.getMessage());
                    // 如果从包含函数获取信息失败，尝试从调用表达式获取
                    modulePackageName = PythonPsiUtils.getPackageName(callExpression);
                }
            } catch (Exception e) {
                LOG.warn("Error accessing containing function, using fallback: " + e.getMessage());
                return createFallbackParameterCallResult(calleeText, callExpression);
            }

            // 构建完整的函数路径作为ID前缀
            String functionFullPath = buildFunctionPath(containingFunction);
            LOG.info("functionFullPath: " + functionFullPath);

            // 从calleeText中提取实际的方法名
            String actualMethodName = PythonPsiUtils.extractMethodNameFromCall(calleeText);
            LOG.info("actualMethodName: " + actualMethodName);

            // 构建ID: 完整函数路径.参数名
            String calleeFullId = functionFullPath + "." + parameterName;
            // 构建签名: 应该包含完整路径的签名
            String calleeSignature = functionFullPath + "." + parameterName + "." + actualMethodName + "()";
            LOG.info("calleeFullId: " + calleeFullId);
            LOG.info("calleeSignature: " + calleeSignature);

            // 获取当前文件的路径
            String filePath = PythonPsiUtils.getFilePath(callExpression);
            LOG.info("filePath: " + filePath);
            LOG.info("modulePackageName: " + modulePackageName);

            AnalysisNode calleeNode = new AnalysisNode(
                    calleeFullId,
                    AnalysisNode.NodeType.FUNCTION,
                    // name 应该是实际被调用的方法名
                    actualMethodName,
                    // className 应该是 null，因为这是参数调用
                    null,
                    // packageName 应该是 null，因为这是参数调用
                    null,
                    PythonPsiUtils.getLineNumber(callExpression),
                    calleeSignature,
                    // moduleName 使用包含函数的模块名
                    modulePackageName,
                    filePath,
                    Language.PYTHON.getCode()
            );

            LOG.info("Successfully created parameter call AnalysisNode:");
            LOG.info("  id: " + calleeNode.getId());
            LOG.info("  name: " + calleeNode.getName());
            LOG.info("  signature: " + calleeNode.getSignature());
            LOG.info("  className: " + calleeNode.getClassName());
            LOG.info("  packageName: " + calleeNode.getPackageName());
            LOG.info("  moduleName: " + calleeNode.getModuleName());
            LOG.info("  filePath: " + calleeNode.getFilePath());

            // 参数调用标记为外部调用
            return new ResolveResult(calleeNode, true);
        } catch (Exception e) {
            LOG.warn("Error creating parameter call result: " + e.getMessage(), e);
            return createFallbackParameterCallResult(calleeText, callExpression);
        }
    }

    /**
     * 构建完整的函数路径（包括模块、类、函数层级）
     */
    private static String buildFunctionPath(PyFunction function) {
        StringBuilder pathBuilder = new StringBuilder();

        // 获取模块信息
        String packageName = PythonPsiUtils.getPackageName(function);
        if (packageName != null && !packageName.isEmpty()) {
            pathBuilder.append(packageName);
        }

        // 获取类名（如果存在）
        String className = PythonPsiUtils.getContainingClassName(function);
        if (className != null && !className.isEmpty()) {
            if (!pathBuilder.isEmpty()) {
                pathBuilder.append(".");
            }
            pathBuilder.append(className);
        }

        // 添加函数名
        if (!pathBuilder.isEmpty()) {
            pathBuilder.append(".");
        }
        pathBuilder.append(function.getName() != null ? function.getName() : "unknown_function");

        return pathBuilder.toString();
    }

    /**
     * 创建回退的参数调用结果
     */
    private static ResolveResult createFallbackParameterCallResult(String calleeText, PyCallExpression callExpression) {
        String calleeDisplayName = PythonPsiUtils.extractMethodNameFromCall(calleeText);
        
        // 获取参数名
        String parameterName = calleeText;
        if (calleeText.contains(".")) {
            String[] parts = calleeText.split("\\.", 2);
            parameterName = parts[0];
        }
        
        // 尝试从调用表达式获取包含函数的信息
        String unknownCalleeFullId = "unknown_function." + parameterName;
        String unknownSignature = calleeDisplayName + "()";
        String moduleName = "PARAMETER_CALL";
        
        try {
            PyFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, PyFunction.class);
            if (containingFunction != null && containingFunction.isValid()) {
                try {
                    // 构建完整的函数路径
                    String functionFullPath = buildFunctionPath(containingFunction);
                    unknownCalleeFullId = functionFullPath + "." + parameterName;
                    unknownSignature = functionFullPath + "." + parameterName + "." + calleeDisplayName + "()";
                    
                    // 使用包含函数的模块名
                    moduleName = PythonPsiUtils.getPackageName(callExpression);
                } catch (Exception e) {
                    LOG.debug("Failed to get info from containing function in fallback, using defaults: " + e.getMessage());
                    // 使用调用表达式的包信息作为回退
                    moduleName = PythonPsiUtils.getPackageName(callExpression);
                }
            }
        } catch (Exception e) {
            LOG.debug("Error in fallback parameter call result creation: " + e.getMessage());
            // 保持默认值
        }
        
        AnalysisNode fallbackNode = new AnalysisNode(
                unknownCalleeFullId,
                AnalysisNode.NodeType.FUNCTION,
                calleeDisplayName,
                // className 应该是 null，因为这是参数调用
                null,
                // packageName 应该是 null，因为这是参数调用
                null,
                PythonPsiUtils.getLineNumber(callExpression),
                unknownSignature,
                moduleName,
                PythonPsiUtils.getFilePath(callExpression),
                Language.PYTHON.getCode(),
                ModuleType.UNKNOWN
        );

        return new ResolveResult(fallbackNode, true);
    }

    /**
     * 解析其他类型的元素
     */
    public static ResolveResult resolveGenericElement(PsiElement resolved, String calleeText, PyCallExpression callExpression) {
        LOG.info("Resolved to " + resolved.getClass().getSimpleName() + " element: " + resolved +
                " for call: " + callExpression.getText());

        // 特殊处理：如果这是一个PyNamedParameterImpl，检查是否为函数参数调用
        if (resolved.getClass().getSimpleName().contains("Parameter") ||
                resolved.getClass().getSimpleName().contains("PyNamedParameterImpl")) {

            // 检查是否为函数参数调用
            if (isGenericParameterCall(resolved, callExpression, calleeText)) {
                return createGenericParameterCallResult(resolved, callExpression, calleeText);
            }
        }

        // 尝试从解析出的元素中提取信息
        String resolvedElementInfo = PythonElementResolver.extractInfoFromResolvedElement(resolved);
        String calleeSignature = PythonPsiUtils.extractMethodNameFromCall(calleeText) + "()";
        String calleeFullId = calleeText;
        String moduleInfo = "RESOLVED_" + resolved.getClass().getSimpleName();

        // 获取文件路径
        PsiFile resolvedFile = resolved.getContainingFile();
        String realFilePath = resolvedFile != null && resolvedFile.getVirtualFile() != null ?
                resolvedFile.getVirtualFile().getPath() : "UNKNOWN";

        // 检查是否为项目内部
        boolean isProjectInternal = PythonElementResolver.isProjectInternalElement(resolved, callExpression);

        // 直接使用模块信息作为包名
        String packageName = moduleInfo;

        AnalysisNode calleeNode = new AnalysisNode(
                calleeFullId,
                AnalysisNode.NodeType.FUNCTION,
                calleeText,
                null,
                // 使用修正后的包名
                packageName,
                PythonPsiUtils.getLineNumber(resolved),
                calleeSignature,
                // moduleName 字段也使用修正后的包名
                packageName,
                realFilePath,
                Language.PYTHON.getCode()
        );

        String additionalInfo = !resolvedElementInfo.isEmpty() ? " info: " + resolvedElementInfo : "";
        LOG.info("Created " + (isProjectInternal ? "internal" : "external") + " resolved element node: " + calleeFullId +
                " (type: " + resolved.getClass().getSimpleName() + ") at path: " + realFilePath + additionalInfo);

        return new ResolveResult(calleeNode, !isProjectInternal);
    }

    /**
     * 创建外部方法节点 - 当无法直接解析调用时使用
     */
    public static AnalysisNode createExternalMethodNode(PyCallExpression callExpression, String calleeText) {
        try {
            LOG.info("Creating external method node for: " + calleeText);

            // 尝试从调用表达式中提取更多信息
            var externalInfo = PythonModulePatternMatcher.extractInfoFromCallExpression(callExpression, calleeText);

            // 构建完整的函数ID和显示名称
            String calleeFullId;
            String calleeDisplayName;

            calleeDisplayName = calleeText;
            if (!"UNKNOWN_MODULE".equals(externalInfo.moduleInfo()) &&
                    !"EXTRACTED_FROM_CALL".equals(externalInfo.filePath())) {
                // 如果成功推断出模块信息，使用完整路径
                calleeFullId = externalInfo.moduleInfo() + "." + calleeText;
                // 保持原始调用文本，如 "os.listdir"
            } else {
                // 对于从调用本身提取的模块信息，避免重复
                calleeFullId = calleeText;
            }

            // 尝试从调用位置的文件路径推断更准确的文件路径
            String realFilePath = externalInfo.filePath();
            if ("INFERRED_FROM_IMPORTS".equals(realFilePath) || "EXTRACTED_FROM_CALL".equals(realFilePath)) {
                // 对于推断出的调用，尝试从当前文件的导入信息中获取更准确的路径
                realFilePath = PythonFilePathResolver.inferFilePathFromCallExpression(callExpression, calleeText, externalInfo.moduleInfo());
            }

            String calleeSignature = PythonPsiUtils.extractMethodNameFromCall(calleeText) + "()";

            // 当模块信息为 "PYTHON_STDLIB.builtins" 时，确保包名也设置为相同值
            String packageName = externalInfo.moduleInfo();
            ModuleType moduleType = PythonModuleResolver.inferModuleTypeFromString(externalInfo.moduleInfo());

            AnalysisNode externalNode = new AnalysisNode(
                    calleeFullId,
                    AnalysisNode.NodeType.FUNCTION,
                    // 使用完整的调用路径作为显示名称
                    calleeDisplayName,
                    externalInfo.moduleInfo(),
                    // 使用修正后的包名
                    packageName,
                    PythonPsiUtils.getLineNumber(callExpression),
                    calleeSignature,
                    // moduleName 字段也使用修正后的包名
                    packageName,
                    realFilePath,
                    Language.PYTHON.getCode(),
                    moduleType
            );

            LOG.info("Created external method node: " + calleeFullId +
                    " (display: " + calleeDisplayName + ") from module: " + externalInfo.moduleInfo() +
                    " with file path: " + realFilePath);

            return externalNode;

        } catch (Exception e) {
            LOG.warn("Error creating external method node: " + e.getMessage(), e);

            // 回退方案：创建基本的未知方法节点
            String unknownFullId = "UNKNOWN." + calleeText;
            String unknownSignature = PythonPsiUtils.extractMethodNameFromCall(calleeText) + "()";
            return new AnalysisNode(
                    unknownFullId,
                    AnalysisNode.NodeType.FUNCTION,
                    // 保持原始调用文本
                    calleeText,
                    "UNKNOWN",
                    "UNKNOWN",
                    PythonPsiUtils.getLineNumber(callExpression),
                    unknownSignature,
                    "UNKNOWN_MODULE",
                    "UNKNOWN_PATH",
                    Language.PYTHON.getCode(),
                    ModuleType.UNKNOWN
            );
        }
    }

    /**
     * 创建项目内部函数节点
     */
    private static ResolveResult createProjectInternalFunctionNode(PyFunction resolvedFunction) {
        String realFilePath = PythonPsiUtils.getFilePath(resolvedFunction);
        String moduleInfo = PythonPsiUtils.getPackageName(resolvedFunction);

        AnalysisNode calleeNode = PythonElementResolver.createFunctionNode(resolvedFunction, false, moduleInfo, realFilePath);

        LOG.info("Resolved project-internal Python function: " + calleeNode.getSignature() +
                " from file: " + (resolvedFunction.getContainingFile() != null ? resolvedFunction.getContainingFile().getName() : "unknown") +
                " at path: " + realFilePath +
                " (not added to current module's node list)");

        return new ResolveResult(calleeNode, false);
    }

    /**
     * 创建外部函数节点
     */
    private static ResolveResult createExternalFunctionNode(PyFunction resolvedFunction) {
        var externalInfo = PythonModuleTypeResolver.resolveExternalMethodInfo(resolvedFunction);
        String realFilePath = PythonPsiUtils.getFilePath(resolvedFunction);

        if (realFilePath == null || realFilePath.isEmpty()) {
            realFilePath = PythonFilePathResolver.getResolvedFunctionFilePath(resolvedFunction, externalInfo);
        }

        AnalysisNode calleeNode = PythonElementResolver.createFunctionNode(resolvedFunction, true, externalInfo.moduleInfo(), realFilePath);

        LOG.info("Resolved external Python method: " + calleeNode.getId() +
                " from module: " + externalInfo.moduleInfo() +
                " at real path: " + realFilePath +
                " (defined at line " + PythonPsiUtils.getLineNumber(resolvedFunction) + ")");

        return new ResolveResult(calleeNode, true);
    }

    /**
     * 创建项目内部类节点
     */
    private static ResolveResult createProjectInternalClassNode(PyClass resolvedClass) {
        String realFilePath = PythonPsiUtils.getFilePath(resolvedClass);
        String moduleInfo = PythonPsiUtils.getPackageName(resolvedClass);

        AnalysisNode calleeNode = PythonElementResolver.createClassNode(resolvedClass, false, moduleInfo, realFilePath);

        LOG.info("Resolved project-internal Python class: " + calleeNode.getSignature() +
                " from file: " + (resolvedClass.getContainingFile() != null ? resolvedClass.getContainingFile().getName() : "unknown") +
                " at path: " + realFilePath +
                " (not added to current module's node list)");

        return new ResolveResult(calleeNode, false);
    }

    /**
     * 创建外部类节点
     */
    private static ResolveResult createExternalClassNode(PyClass resolvedClass) {
        var externalInfo = PythonModuleTypeResolver.resolveExternalMethodInfo(resolvedClass);
        String realFilePath = PythonPsiUtils.getFilePath(resolvedClass);

        if (realFilePath == null || realFilePath.isEmpty()) {
            realFilePath = PythonFilePathResolver.getResolvedClassFilePath(resolvedClass, externalInfo);
        }

        AnalysisNode calleeNode = PythonElementResolver.createClassNode(resolvedClass, true, externalInfo.moduleInfo(), realFilePath);

        LOG.info("Resolved external Python class: " + calleeNode.getId() +
                " from module: " + externalInfo.moduleInfo() +
                " at real path: " + realFilePath +
                " (defined at line " + PythonPsiUtils.getLineNumber(resolvedClass) + ")");

        return new ResolveResult(calleeNode, true);
    }
}