package com.sankuai.deepcode.astplugin.python.export.exporter;

import com.intellij.openapi.project.Project;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;

import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * Python 调用关系 导出器
 * 负责将函数调用详情导出为 CSV 格式
 *
 * <AUTHOR>
 */
public class PythonCallRelationExporter implements PythonDataExporter {

    @Override
    public void export(Project project,
                       List<AnalysisFileResult> analysisFileResults,
                       String outputDirPath,
                       String timestamp) throws IOException {
        String fileName = String.format("Python_Call_Relations_%s.csv", timestamp);
        String filePath = outputDirPath + "/" + fileName;

        try (FileWriter writer = new FileWriter(filePath)) {
            // 写入CSV头部
            StringJoiner header = new StringJoiner(",")
                    .add("filePath")
                    .add("callerId")
                    .add("callerName")
                    .add("callerDefineLine")
                    .add("callerType")
                    .add("calleeId")
                    .add("calleeName")
                    .add("calleeSize")
                    .add("calleeLines")
                    .add("isExternal");
            writer.append(header.toString()).append("\n");

            String projectBasePath = project.getBasePath();

            // 遍历所有文件的分析结果
            for (AnalysisFileResult fileResult : analysisFileResults) {
                String relativeFilePath = fileResult.getRelativeFilePath(projectBasePath);
                AnalysisResult result = fileResult.analysisResult();

                // 处理该文件中的所有调用关系
                for (CallRelation relation : result.getCallRelations()) {
                    writeCallRelationRecord(writer, relativeFilePath, relation);
                }
            }
        }
    }

    /**
     * 写入单个调用关系记录
     */
    private void writeCallRelationRecord(FileWriter writer, String filePath, CallRelation relation) throws IOException {
        AnalysisNode caller = relation.getCaller();
        AnalysisNode callee = relation.getCallee();

        // 收集所有调用行号
        List<CallRelation.CallInstance> instances = relation.getAllCallInstances();
        String calleeLines = instances.stream()
                .map(instance -> String.valueOf(instance.getLineNumber()))
                .collect(Collectors.joining("/"));

        // 如果没有调用实例，使用主要调用行号
        if (calleeLines.isEmpty()) {
            calleeLines = String.valueOf(relation.getCallLineNumber());
        }

        StringJoiner joiner = new StringJoiner(",");

        // filePath: 文件路径
        joiner.add(escapeCsv(filePath));

        // callerId: 调用方的 ID
        joiner.add(escapeCsv(caller.getId()));

        // callerName: 调用方的名称
        joiner.add(escapeCsv(caller.getName()));

        // callerDefineLine: 调用方定义开始的行号
        joiner.add(String.valueOf(caller.getLineNumber()));

        // callerType: 调用方类型（MODULE、CLASS、METHOD、INNER_FUNCTION 等）
        joiner.add(escapeCsv(caller.getType().name()));

        // calleeId: 被调用方 ID
        joiner.add(escapeCsv(callee.getId()));

        // calleeName: 被调用方的名称
        joiner.add(escapeCsv(callee.getName()));

        // calleeSize: 被调用次数
        joiner.add(String.valueOf(relation.getCallCount()));

        // calleeLines: 被调用的行号（以 / 连接起来）
        joiner.add(escapeCsv(calleeLines));

        // isExternal: 是否外部调用
        joiner.add(String.valueOf(relation.isExternal()));

        writer.append(joiner.toString()).append("\n");
    }

    @Override
    public String getExporterName() {
        return "CSV Call Relations";
    }

    @Override
    public String getFileExtension() {
        return "csv";
    }
}