package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.projectRoots.Sdk;
import com.intellij.openapi.roots.ProjectRootManager;
import com.intellij.openapi.util.SystemInfo;
import com.intellij.psi.PsiElement;
import com.intellij.psi.util.PsiTreeUtil;
import com.jetbrains.python.psi.*;
import com.jetbrains.python.sdk.PythonSdkUtil;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Python 模块模式匹配工具类
 * 充分利用 IntelliJ Platform API 和 Python SDK 进行最智能、最优雅的识别
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonModulePatternMatcher {

    private static final Logger LOG = Logger.getInstance(PythonModulePatternMatcher.class);

    // 缓存，提高性能
    private static final ConcurrentHashMap<String, Boolean> MODULE_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Project, Sdk> PROJECT_SDK_CACHE = new ConcurrentHashMap<>();

    private PythonModulePatternMatcher() {
        // 工具类不允许实例化
    }

    /**
     * 检查模块名是否为Python标准库模块 - 使用 IntelliJ Platform API 智能判断
     */
    public static boolean isPythonStandardLibraryModule(String moduleName) {
        return isPythonStandardLibraryModule(moduleName, null);
    }

    /**
     * 检查模块名是否为Python标准库模块 - 带项目上下文的智能判断
     */
    public static boolean isPythonStandardLibraryModule(String moduleName, Project project) {
        if (moduleName == null || moduleName.isEmpty()) {
            return false;
        }

        String cacheKey = moduleName.toLowerCase() + ":" + (project != null ? project.hashCode() : "global");
        return MODULE_CACHE.computeIfAbsent(cacheKey, key -> isStandardLibraryByIntellijApi(moduleName, project));
    }

    /**
     * 检查是否是Python内置函数 - 使用 IntelliJ Platform API 智能判断
     */
    public static boolean isPythonBuiltinFunction(String methodName) {
        return isPythonBuiltinFunction(methodName, null);
    }

    /**
     * 检查是否是Python内置函数 - 带项目上下文的智能判断
     */
    public static boolean isPythonBuiltinFunction(String methodName, Project project) {
        if (methodName == null || methodName.isEmpty()) {
            return false;
        }

        String cacheKey = "builtin:" + methodName.toLowerCase() + ":" + (project != null ? project.hashCode() : "global");
        return MODULE_CACHE.computeIfAbsent(cacheKey, key -> isBuiltinFunctionByIntellijApi(methodName, project));
    }

    /**
     * 使用 IntelliJ Platform API 判断是否为标准库模块
     */
    private static boolean isStandardLibraryByIntellijApi(String moduleName, Project project) {
        try {
            // 获取项目的 Python SDK
            Sdk pythonSdk = getPythonSdk(project);
            if (pythonSdk == null) {
                LOG.debug("No Python SDK found for project, fallback to heuristic");
                return isStandardLibraryByHeuristic(moduleName);
            }

            // 获取 SDK 主目录
            String sdkHomePath = pythonSdk.getHomePath();
            if (sdkHomePath == null) {
                LOG.debug("Python SDK home path is null, fallback to heuristic");
                return isStandardLibraryByHeuristic(moduleName);
            }

            // 智能检查标准库路径
            return checkStandardLibraryInSdk(moduleName, sdkHomePath);

        } catch (Exception e) {
            LOG.debug("Error checking standard library for {}: {}", moduleName, e.getMessage());
            return isStandardLibraryByHeuristic(moduleName);
        }
    }

    /**
     * 使用 IntelliJ Platform API 判断是否为内置函数
     */
    private static boolean isBuiltinFunctionByIntellijApi(String functionName, Project project) {
        try {
            // 尝试从 IDE 的 Python stub 中查找
            String builtinsPath = findBuiltinsStubPath();
            if (builtinsPath != null) {
                return checkFunctionInBuiltinsStub(builtinsPath, functionName);
            }

            // 回退到启发式判断
            return isBuiltinFunctionByHeuristic(functionName);

        } catch (Exception e) {
            LOG.debug("Error checking builtin function for {}: {}", functionName, e.getMessage());
            return isBuiltinFunctionByHeuristic(functionName);
        }
    }

    /**
     * 获取项目的 Python SDK - 使用 IntelliJ Platform API
     */
    public static Sdk getPythonSdk(Project project) {
        if (project == null) {
            return null;
        }

        return PROJECT_SDK_CACHE.computeIfAbsent(project, proj -> {
            try {
                // 使用 ProjectRootManager 获取项目 SDK
                Sdk projectSdk = ProjectRootManager.getInstance(proj).getProjectSdk();

                // 检查是否为 Python SDK
                if (projectSdk != null && PythonSdkUtil.isPythonSdk(projectSdk)) {
                    return projectSdk;
                }

                // 回退：从所有已配置的 SDK 中查找第一个 Python SDK
                return PythonSdkUtil.getAllSdks().stream()
                        .filter(PythonSdkUtil::isPythonSdk)
                        .findFirst()
                        .orElse(null);

            } catch (Exception e) {
                LOG.debug("Error finding Python SDK for project: " + e.getMessage());
                return null;
            }
        });
    }

    /**
     * 在 SDK 中检查标准库模块
     */
    private static boolean checkStandardLibraryInSdk(String moduleName, String sdkHomePath) {
        try {
            // 根据 SDK 路径智能推断标准库位置
            Path sdkPath = Paths.get(sdkHomePath);

            // 查找标准库目录
            Path[] possibleStdLibPaths = {
                    // 对于 virtualenv
                    sdkPath.getParent().resolve("lib"),
                    // 对于系统 Python
                    sdkPath.resolve("lib"),
                    // 智能查找
                    findPythonLibPath(sdkPath)
            };

            for (Path stdlibPath : possibleStdLibPaths) {
                if (stdlibPath != null && Files.exists(stdlibPath)) {
                    // 查找 python3.x 目录
                    if (checkModuleInStdlibPath(moduleName, stdlibPath)) {
                        LOG.debug("Found standard library module: {} in {}", moduleName, stdlibPath);
                        return true;
                    }
                }
            }

            return false;

        } catch (Exception e) {
            LOG.debug("Error checking standard library in SDK for {}: {}", moduleName, e.getMessage());
            return false;
        }
    }

    /**
     * 智能查找 Python lib 路径
     */
    private static Path findPythonLibPath(Path sdkPath) {
        try {
            // 遍历查找包含 "python" 的 lib 目录
            Path parentDir = sdkPath.getParent();
            if (parentDir != null && Files.exists(parentDir)) {
                return Files.walk(parentDir, 3)
                        .filter(Files::isDirectory)
                        .filter(path -> path.getFileName().toString().contains("python"))
                        .filter(path -> path.toString().contains("lib"))
                        .findFirst()
                        .orElse(null);
            }
        } catch (Exception e) {
            LOG.debug("Error finding Python lib path: " + e.getMessage());
        }
        return null;
    }

    /**
     * 在标准库路径中检查模块
     */
    private static boolean checkModuleInStdlibPath(String moduleName, Path stdlibPath) {
        try {
            // 直接在 lib 目录下查找
            if (checkModuleExists(moduleName, stdlibPath)) {
                return true;
            }

            // 在子目录中查找（如 python3.x 目录）
            return Files.list(stdlibPath)
                    .filter(Files::isDirectory)
                    .filter(path -> path.getFileName().toString().startsWith("python"))
                    .anyMatch(pythonDir -> checkModuleExists(moduleName, pythonDir));

        } catch (Exception e) {
            LOG.debug("Error checking module in stdlib path: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查模块是否存在于指定路径
     */
    private static boolean checkModuleExists(String moduleName, Path basePath) {
        if (!Files.exists(basePath)) {
            return false;
        }

        // 检查 .py 文件
        Path moduleFile = basePath.resolve(moduleName + ".py");
        if (Files.exists(moduleFile)) {
            return true;
        }

        // 检查 .pyi 文件（类型标注文件）
        Path moduleStubFile = basePath.resolve(moduleName + ".pyi");
        if (Files.exists(moduleStubFile)) {
            return true;
        }

        // 检查模块目录
        Path moduleDir = basePath.resolve(moduleName);
        return Files.exists(moduleDir) && Files.isDirectory(moduleDir);
    }

    /**
     * 智能查找 IDE 缓存的 builtins.py 路径
     */
    private static String findBuiltinsStubPath() {
        try {
            String userHome = System.getProperty("user.home");

            // 构建可能的缓存路径
            String[] basePaths = SystemInfo.isMac ?
                    new String[]{userHome + "/Library/Caches/JetBrains/"} :
                    SystemInfo.isLinux ?
                            new String[]{userHome + "/.cache/JetBrains/"} :
                            new String[]{System.getenv("APPDATA") + "/JetBrains/", userHome + "/.cache/JetBrains/"};

            for (String basePath : basePaths) {
                if (basePath == null) {
                    continue;
                }

                // 查找所有 JetBrains 产品的缓存目录
                File baseDir = new File(basePath);
                if (baseDir.exists() && baseDir.isDirectory()) {
                    File[] productDirs = baseDir.listFiles(file ->
                            file.isDirectory() &&
                                    (file.getName().startsWith("PyCharm") ||
                                            file.getName().startsWith("IntelliJIdea") ||
                                            file.getName().contains("Python")));

                    if (productDirs != null) {
                        for (File productDir : productDirs) {
                            String builtinsPath = findBuiltinsInProductDir(productDir);
                            if (builtinsPath != null) {
                                LOG.debug("Found builtins stub at: " + builtinsPath);
                                return builtinsPath;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Error finding builtins stub path: " + e.getMessage());
        }
        return null;
    }

    /**
     * 在产品目录中查找 builtins.py
     */
    private static String findBuiltinsInProductDir(File productDir) {
        try {
            // 查找 python_stubs 目录
            File stubsDir = new File(productDir, "python_stubs");
            if (stubsDir.exists() && stubsDir.isDirectory()) {
                // 在所有子目录中查找 builtins.py
                File[] subDirs = stubsDir.listFiles(File::isDirectory);
                if (subDirs != null) {
                    for (File subdir : subDirs) {
                        File builtinsFile = new File(subdir, "builtins.py");
                        if (builtinsFile.exists()) {
                            return builtinsFile.getPath();
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Error searching in product directory: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从 builtins stub 文件中检查函数
     */
    private static boolean checkFunctionInBuiltinsStub(String builtinsPath, String functionName) {
        try {
            Path path = Paths.get(builtinsPath);
            if (Files.exists(path)) {
                String content = Files.readString(path);
                // 查找函数定义模式
                String[] patterns = {
                        "def " + functionName + "(",
                        "class " + functionName + "(",
                        // 类型注解形式
                        functionName + ": ",
                };

                for (String pattern : patterns) {
                    if (content.contains(pattern)) {
                        LOG.debug("Found builtin function {} in stub", functionName);
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Error reading builtins stub: " + e.getMessage());
        }
        return false;
    }

    /**
     * 启发式标准库判断（回退方案）
     */
    private static boolean isStandardLibraryByHeuristic(String moduleName) {
        // 核心标准库模块的精简列表，用于无法访问文件系统时的回退
        Set<String> coreModules = Set.of(
                "os", "sys", "re", "json", "time", "datetime", "math", "random",
                "collections", "itertools", "functools", "pathlib", "logging",
                "urllib", "http", "email", "xml", "socket", "ssl", "threading"
        );
        return coreModules.contains(moduleName.toLowerCase());
    }

    /**
     * 启发式内置函数判断（回退方案）
     */
    private static boolean isBuiltinFunctionByHeuristic(String functionName) {
        // 核心内置函数的精简列表
        Set<String> coreFunctions = Set.of(
                "print", "input", "len", "type", "isinstance", "int", "str", "float",
                "list", "dict", "set", "tuple", "range", "enumerate", "zip", "open",
                "abs", "max", "min", "sum", "all", "any", "sorted", "reversed"
        );
        return coreFunctions.contains(functionName.toLowerCase());
    }

    /**
     * 检查是否为 Python 内置类型
     */
    private static boolean isPythonBuiltinType(String typeName) {
        if (typeName == null || typeName.isEmpty()) {
            return false;
        }

        // Python 内置类型列表
        Set<String> builtinTypes = Set.of(
                "list", "dict", "set", "tuple", "str", "int", "float", "bool",
                "bytes", "bytearray", "complex", "frozenset", "memoryview",
                "object", "slice", "type", "range", "enumerate", "zip", "map", "filter"
        );
        return builtinTypes.contains(typeName.toLowerCase());
    }

    /**
     * 检查是否为已知的子模块 - 使用智能文件系统验证
     */
    public static boolean isKnownSubModule(String parentModule, String subModule) {
        return isKnownSubModule(parentModule, subModule, null);
    }

    /**
     * 检查是否为已知的子模块 - 带项目上下文的智能验证
     */
    public static boolean isKnownSubModule(String parentModule, String subModule, Project project) {
        if (parentModule == null || subModule == null) {
            return false;
        }

        try {
            // 尝试通过 SDK 进行文件系统验证
            Sdk pythonSdk = getPythonSdk(project);
            if (pythonSdk != null && pythonSdk.getHomePath() != null) {
                if (checkSubModuleInSdk(parentModule, subModule, pythonSdk.getHomePath())) {
                    return true;
                }
            }
        } catch (Exception e) {
            LOG.debug("Error checking submodule {}.{}: {}", parentModule, subModule, e.getMessage());
        }

        // 回退到硬编码的已知模式
        return switch (parentModule.toLowerCase()) {
            case "os" -> "path".equals(subModule);
            case "urllib" -> Set.of("parse", "request", "error", "response").contains(subModule);
            case "xml" -> Set.of("etree", "dom", "sax", "parsers").contains(subModule);
            case "email" -> Set.of("mime", "message", "utils").contains(subModule);
            case "http" -> Set.of("client", "server", "cookies").contains(subModule);
            default -> false;
        };
    }

    /**
     * 在 SDK 中检查子模块
     */
    private static boolean checkSubModuleInSdk(String parentModule, String subModule, String sdkHomePath) {
        try {
            Path sdkPath = Paths.get(sdkHomePath);

            // 智能查找 Python lib 路径
            Path libPath = findPythonLibPath(sdkPath);
            if (libPath == null) {
                return false;
            }

            // 查找父模块目录
            Path parentPath = libPath.resolve(parentModule);
            if (!Files.exists(parentPath) || !Files.isDirectory(parentPath)) {
                return false;
            }

            // 检查子模块存在性
            Path subModulePath = parentPath.resolve(subModule + ".py");
            Path subModuleDir = parentPath.resolve(subModule);

            return Files.exists(subModulePath) ||
                    (Files.exists(subModuleDir) && Files.isDirectory(subModuleDir));

        } catch (Exception e) {
            LOG.debug("Error checking submodule in SDK: " + e.getMessage());
            return false;
        }
    }

    /**
     * 从导入语句推断模块 - 使用 PSI 和文件系统智能分析
     */
    public static String inferModuleFromImports(PyCallExpression callExpression, String calleeText) {
        try {
            var containingFile = (PyFile) callExpression.getContainingFile();
            if (containingFile == null) {
                return null;
            }

            // 提取调用的根标识符
            String rootIdentifier = calleeText.split("\\.")[0];

            // 优先通过 PSI 解析获取精确信息
            String psiResolvedModule = resolveThroughPSI(callExpression, rootIdentifier);
            if (psiResolvedModule != null) {
                return psiResolvedModule;
            }

            // 回退到导入语句分析
            return analyzeImportStatements(containingFile, rootIdentifier);

        } catch (Exception e) {
            LOG.warn("Error inferring module from imports: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 通过 PSI 解析获取精确的模块信息
     */
    private static String resolveThroughPSI(PyCallExpression callExpression, String rootIdentifier) {
        try {
            // 尝试解析调用表达式中的引用
            PyExpression callee = callExpression.getCallee();
            if (callee instanceof PyReferenceExpression refExpr) {
                // 尝试解析引用
                PsiElement resolved = refExpr.getReference().resolve();
                if (resolved != null) {
                    // 使用 PythonModuleResolver 获取模块信息
                    PythonModuleResolver.ModuleInfo moduleInfo = PythonModuleResolver.resolveModuleInfo(resolved);
                    return moduleInfo.packageName();
                }
            }
        } catch (Exception e) {
            LOG.debug("PSI resolution failed for: {}, fallback to string matching", rootIdentifier);
        }

        return null;
    }

    /**
     * 分析导入语句获取模块信息
     */
    private static String analyzeImportStatements(PyFile containingFile, String rootIdentifier) {
        // 分析普通 import 语句
        var imports = PsiTreeUtil.findChildrenOfType(containingFile, PyImportStatement.class);
        for (PyImportStatement importStatement : imports) {
            PyImportElement[] importElements = importStatement.getImportElements();
            for (PyImportElement importElement : importElements) {
                String importedName = importElement.getVisibleName();
                String asName = importElement.getAsName();

                if ((asName != null && asName.equals(rootIdentifier)) ||
                        (importedName != null && importedName.equals(rootIdentifier))) {
                    return importedName;
                }
            }
        }

        // 分析 from import 语句
        var fromImports = PsiTreeUtil.findChildrenOfType(containingFile, PyFromImportStatement.class);
        for (PyFromImportStatement fromImport : fromImports) {
            PyImportElement[] importElements = fromImport.getImportElements();
            for (PyImportElement importElement : importElements) {
                String importedName = importElement.getVisibleName();
                String asName = importElement.getAsName();

                if ((asName != null && asName.equals(rootIdentifier)) ||
                        (importedName != null && importedName.equals(rootIdentifier))) {
                    PyReferenceExpression importSource = fromImport.getImportSource();
                    if (importSource != null) {
                        return importSource.getText();
                    }
                }
            }
        }

        return null;
    }

    /**
     * 基于方法名模式推断模块 - 使用智能文件系统检查
     */
    public static String inferModuleFromMethodPattern(String calleeText) {
        if (calleeText.contains(".")) {
            String[] parts = calleeText.split("\\.");
            String rootPart = parts[0];

            // 检查是否为内置类型的方法调用
            if (isPythonBuiltinType(rootPart)) {
                return "builtins";
            }

            // 使用智能标准库检查
            if (isPythonStandardLibraryModule(rootPart)) {
                if (parts.length >= 3) {
                    String subModule = parts[1];
                    if (isKnownSubModule(rootPart, subModule)) {
                        return rootPart + "." + subModule;
                    }
                }
                return rootPart;
            }
        }

        // 检查是否为内置函数
        String methodName = extractMethodName(calleeText);
        if (isPythonBuiltinFunction(methodName)) {
            return "builtins";
        }

        return null;
    }

    /**
     * 从调用文本中提取方法名
     */
    private static String extractMethodName(String calleeText) {
        return PythonPsiUtils.extractMethodNameFromCall(calleeText);
    }

    /**
     * 从调用表达式中提取信息 - 最智能的综合分析
     */
    public static PythonModuleTypeResolver.ExternalMethodInfo extractInfoFromCallExpression(
            PyCallExpression callExpression, String calleeText) {
        try {
            // 优先使用 PSI 解析和导入上下文
            String inferredModule = inferModuleFromImports(callExpression, calleeText);
            if (inferredModule != null) {
                return new PythonModuleTypeResolver.ExternalMethodInfo(
                        inferredModule, "INFERRED_FROM_IMPORTS");
            }

            // 智能模式匹配分析
            if (calleeText.contains(".")) {
                String[] parts = calleeText.split("\\.");
                if (parts.length >= 2) {
                    String rootModule = parts[0];

                    // 使用智能标准库检查
                    if (isPythonStandardLibraryModule(rootModule)) {
                        String modulePath = buildIntelligentModulePath(parts);
                        return new PythonModuleTypeResolver.ExternalMethodInfo(
                                modulePath, "STANDARD_LIBRARY_CALL");
                    } else {
                        String modulePath = buildIntelligentModulePath(parts);
                        return new PythonModuleTypeResolver.ExternalMethodInfo(
                                modulePath, "EXTRACTED_FROM_CALL");
                    }
                }
            }

            // 基于方法名模式推断
            String patternModule = inferModuleFromMethodPattern(calleeText);
            if (patternModule != null) {
                return new PythonModuleTypeResolver.ExternalMethodInfo(
                        patternModule, "INFERRED_FROM_PATTERN");
            }

            return new PythonModuleTypeResolver.ExternalMethodInfo(
                    "UNKNOWN_MODULE", "UNKNOWN_PATH");

        } catch (Exception e) {
            LOG.warn("Error extracting info from call expression: " + e.getMessage(), e);
            return new PythonModuleTypeResolver.ExternalMethodInfo(
                    "EXTRACT_ERROR", "ERROR_PATH");
        }
    }

    /**
     * 智能构建模块路径 - 基于文件系统验证
     */
    private static String buildIntelligentModulePath(String[] parts) {
        if (parts.length < 2) {
            return parts[0];
        }

        String rootModule = parts[0];

        // 智能检查子模块结构
        if (parts.length >= 3) {
            String subModule = parts[1];
            if (isKnownSubModule(rootModule, subModule)) {
                return rootModule + "." + subModule;
            }
        }

        // 默认策略：对于标准库保留完整路径，其他情况返回根模块
        if (isPythonStandardLibraryModule(rootModule) && parts.length > 2) {
            return parts[0] + "." + parts[1];
        }

        return parts[0];
    }
}