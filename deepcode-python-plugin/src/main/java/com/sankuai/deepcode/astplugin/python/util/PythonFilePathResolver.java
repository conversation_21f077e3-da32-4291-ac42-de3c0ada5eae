package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.jetbrains.python.psi.*;

import java.util.Collection;

/**
 * Python 文件路径解析工具类
 * 用于处理文件路径简化、第三方库路径处理、导入分析等
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonFilePathResolver {

    private static final Logger LOG = Logger.getInstance(PythonFilePathResolver.class);

    private PythonFilePathResolver() {
        // 工具类不允许实例化
    }

    /**
     * 获取被解析函数的文件路径（回退策略，只在PSI无法获取时使用）
     */
    public static String getResolvedFunctionFilePath(PyFunction resolvedFunction, Object externalInfo) {
        return getResolvedElementFilePath(resolvedFunction, externalInfo);
    }

    /**
     * 获取被解析类的文件路径（回退策略，只在PSI无法获取时使用）
     */
    public static String getResolvedClassFilePath(PyClass resolvedClass, Object externalInfo) {
        return getResolvedElementFilePath(resolvedClass, externalInfo);
    }

    /**
     * 获取PSI元素的文件路径（通用方法）
     */
    public static String getResolvedElementFilePath(PsiElement element, Object externalInfo) {
        try {
            // 策略1: 从VirtualFile直接获取路径
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null && containingFile.getVirtualFile() != null) {
                String fullPath = containingFile.getVirtualFile().getPath();
                LOG.debug("Fallback: Got full path from VirtualFile: " + fullPath);

                String simplifiedPath = simplifyThirdPartyLibPath(fullPath);
                if (simplifiedPath != null) {
                    return simplifiedPath;
                }

                // 返回文件名
                String fileName = containingFile.getName();
                LOG.debug("Fallback: Using file name: " + fileName);
                return fileName;
            }

            // 策略2: 回退到externalInfo
            String externalInfoPath = getFilePathFromExternalInfo(externalInfo);
            if (externalInfoPath != null) {
                return externalInfoPath;
            }

        } catch (Exception e) {
            LOG.warn("Error in fallback file path resolution: " + e.getMessage(), e);
        }

        // 最终回退
        LOG.debug("Fallback: Unable to determine file path, using UNKNOWN");
        return "UNKNOWN";
    }

    /**
     * 简化第三方库路径，提取site-packages后的相对路径
     */
    public static String simplifyThirdPartyLibPath(String fullPath) {
        try {
            if (fullPath.contains("site-packages")) {
                int sitePackagesIndex = fullPath.lastIndexOf("site-packages");
                if (sitePackagesIndex != -1) {
                    String afterSitePackages = fullPath.substring(sitePackagesIndex + "site-packages".length());
                    if (afterSitePackages.startsWith("/") || afterSitePackages.startsWith("\\")) {
                        afterSitePackages = afterSitePackages.substring(1);
                    }
                    if (!afterSitePackages.isEmpty()) {
                        LOG.debug("Simplified third-party lib path: " + afterSitePackages);
                        return afterSitePackages;
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Error simplifying third-party lib path: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从externalInfo对象中获取文件路径
     */
    private static String getFilePathFromExternalInfo(Object externalInfo) {
        try {
            if (externalInfo != null) {
                java.lang.reflect.Method filePathMethod = externalInfo.getClass().getMethod("filePath");
                Object filePathObj = filePathMethod.invoke(externalInfo);
                if (filePathObj instanceof String filePath && !filePath.isEmpty()) {
                    LOG.debug("Fallback: Using external info file path: " + filePath);
                    return filePath;
                }
            }
        } catch (Exception e) {
            LOG.debug("Fallback: Could not get file path from external info: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从调用表达式推断文件路径
     */
    public static String inferFilePathFromCallExpression(PyCallExpression callExpression, String calleeText, String moduleInfo) {
        try {
            // 获取当前文件
            PsiFile currentFile = callExpression.getContainingFile();
            if (!(currentFile instanceof PyFile pyFile)) {
                return moduleInfo + ".py";
            }

            // 查找导入语句来确定真实的文件路径
            String pathFromImports = findPathFromImportStatements(pyFile, calleeText, moduleInfo);
            if (pathFromImports != null) {
                return pathFromImports;
            }

            // 查找 from xxx import xxx 语句
            String pathFromFromImports = findPathFromFromImportStatements(pyFile, calleeText, moduleInfo);
            if (pathFromFromImports != null) {
                return pathFromFromImports;
            }

            // 基于模块名构建合理的文件路径
            if (moduleInfo != null && !"UNKNOWN_MODULE".equals(moduleInfo)) {
                String inferredPath = moduleInfo.replace(".", "/") + ".py";
                LOG.debug("Constructed file path from module info: " + inferredPath + " for call: " + calleeText);
                return inferredPath;
            }

        } catch (Exception e) {
            LOG.debug("Error inferring file path from call expression: " + e.getMessage());
        }

        return "UNKNOWN_PATH";
    }

    /**
     * 从import语句中查找路径
     */
    private static String findPathFromImportStatements(PyFile pyFile, String calleeText, String moduleInfo) {
        Collection<PyImportElement> importElements = PsiTreeUtil.findChildrenOfType(pyFile, PyImportElement.class);
        for (PyImportElement importElement : importElements) {
            // 使用multiResolve()替代废弃的resolve()方法
            var resolveResults = importElement.multiResolve();
            if (!resolveResults.isEmpty()) {
                PsiElement resolved = resolveResults.get(0).getElement();
                if (resolved != null) {
                    String importedName = importElement.getVisibleName();
                    if (importedName != null && calleeText.endsWith(importedName)) {
                        // 尝试从解析的导入中获取真实路径
                        String realPath = PythonPsiUtils.getFilePath(resolved);
                        if (realPath != null && !realPath.isEmpty() && !"INFERRED_FROM_IMPORTS".equals(realPath)) {
                            LOG.debug("Inferred real file path from import: " + realPath + " for call: " + calleeText);
                            return realPath;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 从from import语句中查找路径
     */
    private static String findPathFromFromImportStatements(PyFile pyFile, String calleeText, String moduleInfo) {
        Collection<PyFromImportStatement> fromImportStatements = PsiTreeUtil.findChildrenOfType(pyFile, PyFromImportStatement.class);
        for (PyFromImportStatement fromImport : fromImportStatements) {
            PyReferenceExpression importSource = fromImport.getImportSource();
            if (importSource != null) {
                String importSourceText = importSource.getText();
                if (moduleInfo.startsWith(importSourceText)) {
                    fromImport.getImportElements();
                    for (PyImportElement importElement : fromImport.getImportElements()) {
                        String importedName = importElement.getVisibleName();
                        if (importedName != null && calleeText.endsWith(importedName)) {
                            // 使用multiResolve()替代废弃的resolve()方法
                            var resolveResults = importElement.multiResolve();
                            if (!resolveResults.isEmpty()) {
                                PsiElement resolved = resolveResults.get(0).getElement();
                                if (resolved != null) {
                                    String realPath = PythonPsiUtils.getFilePath(resolved);
                                    if (realPath != null && !realPath.isEmpty() && !"INFERRED_FROM_IMPORTS".equals(realPath)) {
                                        LOG.debug("Inferred real file path from 'from import': " + realPath + " for call: " + calleeText);
                                        return realPath;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 简化显示路径（对第三方库路径进行特殊处理）
     */
    public static String simplifyDisplayPath(String realFilePath) {
        if (realFilePath != null && realFilePath.contains("site-packages")) {
            String simplified = simplifyThirdPartyLibPath(realFilePath);
            if (simplified != null) {
                return simplified;
            }
        }
        return realFilePath;
    }
}