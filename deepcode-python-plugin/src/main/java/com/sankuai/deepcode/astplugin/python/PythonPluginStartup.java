package com.sankuai.deepcode.astplugin.python;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import com.sankuai.deepcode.astplugin.export.ExportRegistry;
import com.sankuai.deepcode.astplugin.python.export.PythonExportService;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 插件启动组件 - 确保Python分析器在插件启动时正确注册
 *
 * <AUTHOR>
 */
public class PythonPluginStartup implements ProjectActivity {

    private static final Logger LOG = Logger.getInstance(PythonPluginStartup.class);

    @Nullable
    @Override
    public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        // 在后台线程中初始化分析器服务
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                LOG.info("🚀 Python Plugin startup - initializing analyzer service");

                // 获取或创建PythonAnalyzerService实例，这会触发分析器注册
                PythonAnalyzerService pythonService = ApplicationManager.getApplication()
                        .getService(PythonAnalyzerService.class);

                // 确保分析器已注册
                if (pythonService != null) {
                    pythonService.ensureAnalyzerRegistered();
                    LOG.info("✅ Python analyzer registered successfully");
                } else {
                    LOG.error("❌ Failed to get PythonAnalyzerService instance");
                }

                // 注册Python导出服务
                try {
                    ExportRegistry exportRegistry = ExportRegistry.getInstance();
                    PythonExportService pythonExportService = new PythonExportService();
                    exportRegistry.registerExportService(pythonExportService);
                    LOG.info("✅ Python export service registered successfully");
                } catch (Exception e) {
                    LOG.error("❌ Failed to register Python export service", e);
                }

                LOG.info("✅ Python Plugin startup completed successfully");

            } catch (Exception e) {
                LOG.error("💥 Error during Python plugin startup", e);
            }
        });
        return Unit.INSTANCE;
    }
}