package com.sankuai.deepcode.astplugin.python.export.exporter;

import com.intellij.openapi.project.Project;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Python HTML 导出器
 * 负责将分析数据导出为完整的HTML报告
 *
 * <AUTHOR>
 */
public class PythonHtmlExporter implements PythonDataExporter {

    @Override
    public void export(Project project,
                      List<AnalysisFileResult> analysisFileResults,
                      String outputDirPath,
                      String timestamp) throws IOException {
        String fileName = String.format("Python_Full_Report_%s.html", timestamp);
        String filePath = outputDirPath + "/" + fileName;

        try (FileWriter writer = new FileWriter(filePath)) {
            // HTML 文档头部
            writeHtmlHeader(writer);

            // 报告概览
            writeSummary(writer, project, analysisFileResults);

            // 详细数据表格
            writeDetailTable(writer, project, analysisFileResults);

            // HTML 文档尾部
            writeHtmlFooter(writer);
        }
    }

    @Override
    public String getExporterName() {
        return "HTML Full Report";
    }

    @Override
    public String getFileExtension() {
        return "html";
    }

    /**
     * 写入HTML头部
     */
    private void writeHtmlHeader(FileWriter writer) throws IOException {
        writer.append("<!DOCTYPE html>\n");
        writer.append("<html lang=\"zh-CN\">\n");
        writer.append("<head>\n");
        writer.append("    <meta charset=\"UTF-8\">\n");
        writer.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        writer.append("    <title>Python AST 分析完整报告</title>\n");
        writeHtmlStyles(writer);
        writer.append("</head>\n");
        writer.append("<body>\n");
        writer.append("    <h1>Python AST 分析完整报告</h1>\n");
    }

    /**
     * 写入CSS样式
     */
    private void writeHtmlStyles(FileWriter writer) throws IOException {
        writer.append("    <style>\n");
        writer.append("        body { \n");
        writer.append("            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; \n");
        writer.append("            margin: 0; padding: 20px; \n");
        writer.append("            background-color: #f5f5f5;\n");
        writer.append("            line-height: 1.6;\n");
        writer.append("        }\n");
        writer.append("        h1 { \n");
        writer.append("            color: #2c3e50; \n");
        writer.append("            text-align: center;\n");
        writer.append("            margin-bottom: 30px;\n");
        writer.append("            padding-bottom: 10px;\n");
        writer.append("            border-bottom: 3px solid #3498db;\n");
        writer.append("        }\n");
        writer.append("        h2 { \n");
        writer.append("            color: #34495e; \n");
        writer.append("            margin-top: 30px;\n");
        writer.append("        }\n");
        writer.append("        table { \n");
        writer.append("            border-collapse: collapse; \n");
        writer.append("            width: 100%; \n");
        writer.append("            margin: 20px 0;\n");
        writer.append("            background-color: white;\n");
        writer.append("            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n");
        writer.append("            border-radius: 8px;\n");
        writer.append("            overflow: hidden;\n");
        writer.append("        }\n");
        writer.append("        th, td { \n");
        writer.append("            border: 1px solid #ddd; \n");
        writer.append("            padding: 12px 15px; \n");
        writer.append("            text-align: left;\n");
        writer.append("        }\n");
        writer.append("        th { \n");
        writer.append("            background-color: #3498db; \n");
        writer.append("            color: white;\n");
        writer.append("            font-weight: 600;\n");
        writer.append("            text-transform: uppercase;\n");
        writer.append("            font-size: 0.9em;\n");
        writer.append("        }\n");
        writer.append("        tr:nth-child(even) {\n");
        writer.append("            background-color: #f8f9fa;\n");
        writer.append("        }\n");
        writer.append("        tr:hover {\n");
        writer.append("            background-color: #e3f2fd;\n");
        writer.append("        }\n");
        writer.append("        .summary { \n");
        writer.append("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
        writer.append("            color: white;\n");
        writer.append("            padding: 25px; \n");
        writer.append("            border-radius: 10px; \n");
        writer.append("            margin: 20px 0;\n");
        writer.append("            box-shadow: 0 4px 15px rgba(0,0,0,0.2);\n");
        writer.append("        }\n");
        writer.append("        .summary p {\n");
        writer.append("            margin: 8px 0;\n");
        writer.append("            font-size: 1.1em;\n");
        writer.append("        }\n");
        writer.append("        .stat-grid {\n");
        writer.append("            display: grid;\n");
        writer.append("            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n");
        writer.append("            gap: 15px;\n");
        writer.append("            margin-top: 15px;\n");
        writer.append("        }\n");
        writer.append("        .stat-item {\n");
        writer.append("            background-color: rgba(255,255,255,0.2);\n");
        writer.append("            padding: 15px;\n");
        writer.append("            border-radius: 8px;\n");
        writer.append("            text-align: center;\n");
        writer.append("        }\n");
        writer.append("        .stat-number {\n");
        writer.append("            font-size: 2em;\n");
        writer.append("            font-weight: bold;\n");
        writer.append("            display: block;\n");
        writer.append("        }\n");
        writer.append("        .stat-label {\n");
        writer.append("            font-size: 0.9em;\n");
        writer.append("            opacity: 0.9;\n");
        writer.append("        }\n");
        writer.append("    </style>\n");
    }

    /**
     * 写入概览信息
     */
    private void writeSummary(FileWriter writer, Project project, List<AnalysisFileResult> analysisFileResults) throws IOException {
        writer.append("    <div class=\"summary\">\n");
        writer.append("        <h2>📊 分析概览</h2>\n");
        writer.append("        <p><strong>生成时间:</strong> ").append(LocalDateTime.now().toString()).append("</p>\n");

        // 计算总的统计数据
        int totalFiles = analysisFileResults.size();
        int totalNodes = analysisFileResults.stream().mapToInt(f -> f.analysisResult().getNodes().size()).sum();
        int totalCalls = analysisFileResults.stream().mapToInt(f -> f.analysisResult().getCallRelations().size()).sum();
        int totalImports = analysisFileResults.stream().mapToInt(f -> f.analysisResult().getImports().size()).sum();

        writer.append("        <div class=\"stat-grid\">\n");
        writer.append("            <div class=\"stat-item\">\n");
        writer.append("                <span class=\"stat-number\">").append(String.valueOf(totalFiles)).append("</span>\n");
        writer.append("                <span class=\"stat-label\">分析文件总数</span>\n");
        writer.append("            </div>\n");
        writer.append("            <div class=\"stat-item\">\n");
        writer.append("                <span class=\"stat-number\">").append(String.valueOf(totalNodes)).append("</span>\n");
        writer.append("                <span class=\"stat-label\">总节点数</span>\n");
        writer.append("            </div>\n");
        writer.append("            <div class=\"stat-item\">\n");
        writer.append("                <span class=\"stat-number\">").append(String.valueOf(totalCalls)).append("</span>\n");
        writer.append("                <span class=\"stat-label\">总调用关系</span>\n");
        writer.append("            </div>\n");
        writer.append("            <div class=\"stat-item\">\n");
        writer.append("                <span class=\"stat-number\">").append(String.valueOf(totalImports)).append("</span>\n");
        writer.append("                <span class=\"stat-label\">总导入数</span>\n");
        writer.append("            </div>\n");
        writer.append("        </div>\n");
        writer.append("    </div>\n");
    }

    /**
     * 写入详细数据表格
     */
    private void writeDetailTable(FileWriter writer, Project project, List<AnalysisFileResult> analysisFileResults) throws IOException {
        writer.append("    <h2>📋 详细分析数据</h2>\n");
        writer.append("    <table>\n");
        writer.append("        <thead>\n");
        writer.append("            <tr>\n");
        writer.append("                <th>文件路径</th>\n");
        writer.append("                <th>模块名</th>\n");
        writer.append("                <th>AST节点数</th>\n");
        writer.append("                <th>调用关系数</th>\n");
        writer.append("                <th>导入数</th>\n");
        writer.append("                <th>错误数</th>\n");
        writer.append("            </tr>\n");
        writer.append("        </thead>\n");
        writer.append("        <tbody>\n");

        String projectBasePath = project.getBasePath();
        for (AnalysisFileResult fileResult : analysisFileResults) {
            String relativeFilePath = fileResult.getRelativeFilePath(projectBasePath);
            String moduleName = fileResult.getModuleName(projectBasePath);

            writer.append("            <tr>\n");
            writer.append("                <td>").append(escapeHtml(relativeFilePath)).append("</td>\n");
            writer.append("                <td>").append(escapeHtml(moduleName)).append("</td>\n");
            writer.append("                <td>").append(String.valueOf(fileResult.analysisResult().getNodes().size())).append("</td>\n");
            writer.append("                <td>").append(String.valueOf(fileResult.analysisResult().getCallRelations().size())).append("</td>\n");
            writer.append("                <td>").append(String.valueOf(fileResult.analysisResult().getImports().size())).append("</td>\n");
            writer.append("                <td>").append(String.valueOf(fileResult.analysisResult().getErrors().size())).append("</td>\n");
            writer.append("            </tr>\n");
        }

        writer.append("        </tbody>\n");
        writer.append("    </table>\n");
    }

    /**
     * 写入HTML尾部
     */
    private void writeHtmlFooter(FileWriter writer) throws IOException {
        writer.append("    <div style=\"text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;\">\n");
        writer.append("        <p>由 DeepCode AST Plugin 生成</p>\n");
        writer.append("    </div>\n");
        writer.append("</body>\n");
        writer.append("</html>\n");
    }

    /**
     * 转义HTML内容
     */
    private static String escapeHtml(String value) {
        if (value == null) {
            return "";
        }
        return value.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;");
    }
}