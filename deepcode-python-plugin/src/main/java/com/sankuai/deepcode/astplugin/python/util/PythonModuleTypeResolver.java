package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.projectRoots.Sdk;
import com.intellij.openapi.vfs.VirtualFile;
import com.jetbrains.python.psi.PyClass;
import com.jetbrains.python.psi.PyFunction;
import com.sankuai.deepcode.astplugin.model.ModuleType;

/**
 * Python 模块类型解析工具类
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonModuleTypeResolver {

    private static final Logger LOG = Logger.getInstance(PythonModuleTypeResolver.class);

    private PythonModuleTypeResolver() {
        // 工具类不允许实例化
    }


    /**
     * 外部方法信息记录
     */
    public record ExternalMethodInfo(String moduleInfo, String filePath) {
        public ExternalMethodInfo(String moduleInfo, String filePath) {
            this.moduleInfo = moduleInfo != null ? moduleInfo : "UNKNOWN_MODULE";
            this.filePath = filePath != null ? filePath : "UNKNOWN_PATH";
        }
    }

    /**
     * 查找项目的Python SDK
     */
    public static Sdk findProjectPythonSdk(Project project) {
        try {
            // 简化版本的SDK查找，如果PythonSdkUtil不可用
            // 暂时返回null，让方法能优雅降级到字符串匹配
            return null;
        } catch (Exception e) {
            LOG.debug("Error finding Python SDK: " + e.getMessage());
            return null;
        }
    }

    /**
     * 使用PSI能力准确判断模块类型
     *
     * @deprecated 使用 {@link PythonModuleResolver#determineModuleType(String, Project)} 替代
     */
    @Deprecated
    public static ModuleType determineModuleType(VirtualFile virtualFile, Project project, Sdk sdk) {
        if (virtualFile == null) {
            return ModuleType.UNKNOWN;
        }

        // 委托给新的通用解析器
        return PythonModuleResolver.determineModuleType(virtualFile.getPath(), project);
    }

    /**
     * 解析外部类的模块和路径信息
     */
    public static ExternalMethodInfo resolveExternalMethodInfo(PyClass pyClass) {
        try {
            LOG.info("Resolving external Python class info for: " + pyClass.getName());

            // 使用新的通用模块解析器
            PythonModuleResolver.ModuleInfo moduleInfo = PythonModuleResolver.resolveModuleInfo(pyClass);

            return new ExternalMethodInfo(
                    moduleInfo.getFormattedModuleInfo(),
                    moduleInfo.filePath()
            );

        } catch (Exception e) {
            LOG.warn("Error resolving external class info for " + pyClass.getName() + ": " + e.getMessage(), e);
            return new ExternalMethodInfo("ERROR_RESOLVING", "ERROR_PATH");
        }
    }

    /**
     * 解析外部方法的模块和路径信息
     */
    public static ExternalMethodInfo resolveExternalMethodInfo(PyFunction function) {
        try {
            LOG.info("Resolving external Python method info for: " + function.getName());

            // 使用新的通用模块解析器
            PythonModuleResolver.ModuleInfo moduleInfo = PythonModuleResolver.resolveModuleInfo(function);

            return new ExternalMethodInfo(
                    moduleInfo.getFormattedModuleInfo(),
                    moduleInfo.filePath()
            );

        } catch (Exception e) {
            LOG.warn("Error resolving external method info for " + function.getName() + ": " + e.getMessage(), e);
            return new ExternalMethodInfo("ERROR_RESOLVING", "ERROR_PATH");
        }
    }
}