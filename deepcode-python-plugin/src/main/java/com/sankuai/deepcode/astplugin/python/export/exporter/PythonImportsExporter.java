package com.sankuai.deepcode.astplugin.python.export.exporter;

import com.intellij.openapi.project.Project;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * Python 文本导出器
 * 负责将分析数据导出为自定义文本格式
 *
 * <AUTHOR>
 */
public class PythonImportsExporter implements PythonDataExporter {

    @Override
    public void export(Project project,
                       List<AnalysisFileResult> analysisFileResults,
                       String outputDirPath,
                       String timestamp) throws IOException {
        // 导出CSV格式
        exportCsvFormat(project, analysisFileResults, outputDirPath, timestamp);
    }

    @Override
    public String getExporterName() {
        return "Imports Export (CSV)";
    }

    @Override
    public String getFileExtension() {
        return "csv";
    }

    /**
     * 导出CSV格式
     */
    private void exportCsvFormat(Project project,
                                 List<AnalysisFileResult> analysisFileResults,
                                 String outputDirPath,
                                 String timestamp) throws IOException {
        String fileName = String.format("Python_Imports_Export_%s.csv", timestamp);
        String filePath = outputDirPath + "/" + fileName;

        try (FileWriter writer = new FileWriter(filePath)) {
            // 写入CSV头部
            writer.append("filePath,lineNumber,importModule,isExternal\n");

            // 收集所有导入记录并按行号排序
            List<ImportRecord> importRecords = new ArrayList<>();
            String projectBasePath = project.getBasePath();

            for (AnalysisFileResult fileResult : analysisFileResults) {
                String relativeFilePath = fileResult.getRelativeFilePath(projectBasePath);
                List<ImportInfo> imports = fileResult.analysisResult().getImports();

                for (ImportInfo importInfo : imports) {
                    List<String> resolvedClasses = importInfo.getResolvedClasses();

                    if (resolvedClasses.isEmpty()) {
                        // 如果没有resolvedClasses，使用导入语句本身作为importModule
                        String importModule = extractImportModule(importInfo.getStatement());
                        importRecords.add(new ImportRecord(
                                relativeFilePath,
                                importInfo.getLineNumber(),
                                importModule,
                                importInfo.isExternal()
                        ));
                    } else {
                        // 如果有resolvedClasses，为每个类创建一条记录
                        for (String resolvedClass : resolvedClasses) {
                            importRecords.add(new ImportRecord(
                                    relativeFilePath,
                                    importInfo.getLineNumber(),
                                    resolvedClass,
                                    importInfo.isExternal()
                            ));
                        }
                    }
                }
            }

            // 按行号升序排列
            importRecords.sort(Comparator.comparingInt(ImportRecord::lineNumber));

            // 写入CSV数据
            for (ImportRecord record : importRecords) {
                writer.append(String.format("%s,%d,%s,%s\n",
                        escapeCsv(record.filePath()),
                        record.lineNumber(),
                        escapeCsv(record.importModule()),
                        record.isExternal() ? "true" : "false"
                ));
            }
        }
    }

    /**
     * 从导入语句中提取模块名
     */
    private String extractImportModule(String statement) {
        if (statement == null || statement.trim().isEmpty()) {
            return "";
        }

        String trimmed = statement.trim();

        // 处理 "from xxx import yyy" 格式
        if (trimmed.startsWith("from ") && trimmed.contains(" import ")) {
            int fromIndex = trimmed.indexOf("from ") + 5;
            int importIndex = trimmed.indexOf(" import ");
            if (fromIndex < importIndex) {
                return trimmed.substring(fromIndex, importIndex).trim();
            }
        }

        // 处理 "import xxx" 格式
        if (trimmed.startsWith("import ")) {
            String importPart = trimmed.substring(7).trim();
            // 处理 "import xxx as yyy" 的情况
            if (importPart.contains(" as ")) {
                importPart = importPart.substring(0, importPart.indexOf(" as ")).trim();
            }
            // 处理多个导入的情况，取第一个
            if (importPart.contains(",")) {
                importPart = importPart.substring(0, importPart.indexOf(",")).trim();
            }
            return importPart;
        }

        return trimmed;
    }

    /**
     * 导入记录数据类
     */
    private record ImportRecord(String filePath, int lineNumber, String importModule, boolean isExternal) {
    }
}