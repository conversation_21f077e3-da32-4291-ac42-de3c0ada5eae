package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiNamedElement;
import com.intellij.psi.util.PsiTreeUtil;
import com.jetbrains.python.psi.*;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Python 导入语句分析工具类
 * 充分利用 IDEA PSI 和 PythonModuleResolver 来判断导入类型
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public final class PythonImportAnalyzer {
    private static final Logger LOG = Logger.getInstance(PythonImportAnalyzer.class);

    private PythonImportAnalyzer() {
        // 工具类不允许实例化
    }

    /**
     * 分析导入语句
     */
    public static void analyzeImports(PyFile pythonFile, AnalysisResult result) {
        // 分析from语句
        Collection<PyFromImportStatement> fromImports = PsiTreeUtil.findChildrenOfType(pythonFile, PyFromImportStatement.class);
        for (PyFromImportStatement fromImport : fromImports) {
            analyzeFromImportStatement(fromImport, result);
        }

        // 分析普通import语句
        Collection<PyImportStatement> imports = PsiTreeUtil.findChildrenOfType(pythonFile, PyImportStatement.class);
        for (PyImportStatement importStatement : imports) {
            analyzeImportStatement(importStatement, result);
        }
    }

    /**
     * 分析from import语句
     */
    public static void analyzeFromImportStatement(PyFromImportStatement fromImport, AnalysisResult result) {
        String importText = fromImport.getText();
        int lineNumber = PythonPsiUtils.getLineNumber(fromImport);
        String filePath = PythonAnalyzerUtils.getFilePath(fromImport.getContainingFile());

        ImportInfo.ImportType type = fromImport.isStarImport() ?
                ImportInfo.ImportType.WILDCARD : ImportInfo.ImportType.FROM_IMPORT;

        // 获取来源模块名 - 处理所有情况
        PyReferenceExpression importSource = fromImport.getImportSource();
        String sourceModuleFromPSI = importSource != null ? importSource.getText() : "";

        // 检查是否为相对导入：使用 fromImport.getRelativeLevel() 或检查完整文本
        int relativeLevel = fromImport.getRelativeLevel();
        boolean isRelativeImport = relativeLevel > 0;

        // 构建完整的源模块名（包含点号前缀）
        String sourceModule;
        if (isRelativeImport) {
            // 相对导入：添加对应数量的点号前缀
            sourceModule = ".".repeat(relativeLevel) + sourceModuleFromPSI;
        } else {
            sourceModule = sourceModuleFromPSI != null ? sourceModuleFromPSI : "unknown_module";
        }

        List<String> resolvedClasses = new ArrayList<>();
        if (fromImport.isStarImport()) {
            LOG.info("这是一个通配符导入: from " + sourceModule + " import *");

            // 处理相对导入的情况
            String resolvedSourceModule = resolveRelativeImportPath(fromImport, sourceModule);
            resolvedClasses.add("All symbols from " + resolvedSourceModule);
        } else {

            // 解析相对导入路径
            String resolvedSourceModule = resolveRelativeImportPath(fromImport, sourceModule);

            PyImportElement[] importElements = fromImport.getImportElements();
            LOG.info("导入元素数量: " + importElements.length);

            for (PyImportElement element : importElements) {
                // 获取原始的导入名称（不是别名）
                String originalImportedName = getOriginalFromImportName(element);
                String alias = element.getAsName();

                LOG.info("处理导入元素: 原始名='" + originalImportedName + "', 别名='" + alias + "'");

                if (originalImportedName == null) {
                    LOG.warn("无法获取原始导入名称，跳过此元素");
                    continue;
                }

                // 对于 from xx import yy [as zz] 的情况，无论是否有别名，
                // 解析的类都应该是 xx.yy，使用原始名称 yy 而不是别名 zz
                String resolvedClass;
                if (!resolvedSourceModule.isEmpty()) {
                    resolvedClass = resolvedSourceModule + "." + originalImportedName;
                } else {
                    resolvedClass = originalImportedName;
                }

                LOG.info("最终解析结果: '" + resolvedClass + "'");
                resolvedClasses.add(resolvedClass);
                LOG.debug("Resolved from-import element: {} (alias: {}) -> {}",
                        originalImportedName, element.getAsName(), resolvedClass);
            }
        }

        // 判断是否为外部导入 - 使用 IDEA PSI 能力和 PythonModuleResolver
        boolean isExternal = isPythonExternalImportWithPSI(fromImport);

        // 解析被导入的目标文件路径
        String targetFilePath = resolvePythonTargetFilePath(fromImport, sourceModule, isExternal);

        ImportInfo importInfo = new ImportInfo(importText, lineNumber, type, isExternal, resolvedClasses, filePath, targetFilePath);
        result.addImport(importInfo);
    }

    /**
     * 获取 from import 语句中的原始导入名称（不是别名）
     * 对于 from xx import yy as zz，返回 yy 而不是 zz
     *
     * @param element PyImportElement
     * @return 原始的导入名称
     */
    private static String getOriginalFromImportName(PyImportElement element) {
        try {
            // 获取完整的 from import 语句
            PyFromImportStatement fromImport = (PyFromImportStatement) element.getParent();
            String fullImportText = fromImport.getText();

            // 检查是否有别名
            String asName = element.getAsName();
            if (asName != null) {
                // 有别名的情况，从文本中解析原始名称
                String originalName = extractOriginalNameFromFullText(fullImportText, asName);
                if (originalName != null) {
                    LOG.debug("Extracted original name from full text: {} (alias: {})", originalName, asName);
                    return originalName;
                }
            }

            // 没有别名的情况，直接使用元素的名称
            String name = element.getName();
            if (name != null) {
                LOG.debug("Got name without alias: {}", name);
                return name;
            }

            // 最后的回退
            String visibleName = element.getVisibleName();
            LOG.debug("Using visible name as fallback: {}", visibleName);
            return visibleName;

        } catch (Exception e) {
            LOG.debug("Error getting original from-import name for element: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从完整的 from import 语句文本中提取原始导入名称
     * 对于 "from loguru import logger as _logger"，提取出 "logger"
     *
     * @param fullImportText 完整的 from import 语句文本
     * @param alias          别名
     * @return 原始导入名称
     */
    private static String extractOriginalNameFromFullText(String fullImportText, String alias) {
        if (fullImportText == null || alias == null) {
            return null;
        }

        try {
            // 查找 "import" 关键字
            int importIndex = fullImportText.indexOf("import");
            if (importIndex == -1) {
                return null;
            }

            // 获取 import 之后的部分
            String afterImport = fullImportText.substring(importIndex + 6).trim();

            // 查找 "as alias" 模式
            String asPattern = " as " + alias;
            int asIndex = afterImport.indexOf(asPattern);
            if (asIndex == -1) {
                return null;
            }

            // 提取原始名称（from import 后面到 as 前面的部分）
            String beforeAs = afterImport.substring(0, asIndex).trim();

            // 处理多个导入的情况，如 "from x import a as b, c as d"
            if (beforeAs.contains(",")) {
                // 找到对应的原始名称
                String[] parts = afterImport.split(",");
                for (String part : parts) {
                    part = part.trim();
                    if (part.contains(" as " + alias)) {
                        int partAsIndex = part.indexOf(" as " + alias);
                        if (partAsIndex > 0) {
                            return part.substring(0, partAsIndex).trim();
                        }
                    }
                }
            } else {
                // 单个导入的情况
                return beforeAs;
            }

        } catch (Exception e) {
            LOG.debug("Error extracting original name from full import text: {}", e.getMessage());
        }

        return null;
    }


    /**
     * 从PSI元素获取完全限定名称
     *
     * @param psiElement PSI元素
     * @return 完全限定名称
     */
    private static String getFullyQualifiedNameFromPSIElement(PsiElement psiElement) {
        try {
            // 如果是Python文件，获取模块路径
            if (psiElement instanceof PyFile pyFile) {
                return getLogicalModulePathFromPyFile(pyFile);
            }

            // 如果是类、函数等定义在文件内的元素
            PyFile containingFile = (PyFile) psiElement.getContainingFile();
            if (containingFile != null) {
                String modulePath = getLogicalModulePathFromPyFile(containingFile);

                // 尝试获取元素在模块内的名称
                String elementName = getElementNameInModule(psiElement);
                if (elementName != null && !elementName.isEmpty()) {
                    if (!modulePath.isEmpty()) {
                        return modulePath + "." + elementName;
                    } else {
                        return elementName;
                    }
                } else {
                    return modulePath;
                }
            }

        } catch (Exception e) {
            LOG.debug("Error getting fully qualified name from PSI element: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 获取PSI元素在其模块内的名称
     *
     * @param psiElement PSI元素
     * @return 元素名称
     */
    private static String getElementNameInModule(PsiElement psiElement) {
        try {
            // 如果是Python的命名元素（类、函数、变量等）
            if (psiElement instanceof com.jetbrains.python.psi.PyTargetExpression) {
                return ((com.jetbrains.python.psi.PyTargetExpression) psiElement).getName();
            }
            if (psiElement instanceof com.jetbrains.python.psi.PyFunction) {
                return ((com.jetbrains.python.psi.PyFunction) psiElement).getName();
            }
            if (psiElement instanceof com.jetbrains.python.psi.PyClass) {
                return ((com.jetbrains.python.psi.PyClass) psiElement).getName();
            }

            // 通用的命名元素处理
            if (psiElement instanceof PsiNamedElement psiNamedElement) {
                return psiNamedElement.getName();
            }

        } catch (Exception e) {
            LOG.debug("Error getting element name in module: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 解析相对导入路径，将相对路径转换为绝对模块路径
     * 优先使用PSI的解析能力，回退到路径计算
     *
     * @param importStatement 导入语句
     * @param sourceModule    原始模块名（可能包含相对路径）
     * @return 解析后的绝对模块路径
     */
    private static String resolveRelativeImportPath(PyFromImportStatement importStatement, String sourceModule) {
        if (sourceModule == null) {
            return "";
        }

        // 如果不是相对导入（不以 . 开头），直接返回
        if (!sourceModule.startsWith(".")) {
            return sourceModule;
        }

        try {
            // 方法1: 充分利用PyFromImportStatement的PSI解析能力
            String psiResolvedPath = resolveRelativeImportWithPSI(importStatement, sourceModule);
            if (psiResolvedPath != null) {
                LOG.info("PSI直接解析相对导入成功: " + sourceModule + " -> " + psiResolvedPath);
                return psiResolvedPath;
            }

            // 方法2: 尝试使用导入源的PSI解析
            PyReferenceExpression importSource = importStatement.getImportSource();
            if (importSource != null) {
                String resolvedModulePath = resolveModulePathFromPSI(importSource);
                if (resolvedModulePath != null && !resolvedModulePath.isEmpty()) {
                    LOG.debug("PSI resolved relative import: {} -> {}", sourceModule, resolvedModulePath);
                    return resolvedModulePath;
                }
            }

            // 方法3: 回退到路径计算方法
            return resolveRelativeImportPathByCalculation(importStatement, sourceModule);

        } catch (Exception e) {
            LOG.debug("Error resolving relative import path for {}: {}", sourceModule, e.getMessage());
            // 出错时回退到路径计算方法
            return resolveRelativeImportPathByCalculation(importStatement, sourceModule);
        }
    }

    /**
     * 充分利用PSI能力解析相对导入
     *
     * @param importStatement from import语句
     * @param sourceModule    原始模块名
     * @return 解析后的模块路径，如果无法解析返回null
     */
    private static String resolveRelativeImportWithPSI(PyFromImportStatement importStatement, String sourceModule) {
        try {
            // 获取当前文件
            PyFile currentFile = (PyFile) importStatement.getContainingFile();
            if (currentFile == null) {
                return null;
            }

            // 利用PyFromImportStatement的resolveImportSource方法
            PsiElement resolvedSource = importStatement.resolveImportSource();
            if (resolvedSource != null) {
                // 如果解析到了目标，获取其完全限定名
                String resolvedPath = getFullyQualifiedNameFromPSIElement(resolvedSource);
                if (resolvedPath != null && !resolvedPath.isEmpty()) {
                    LOG.info("PSI resolveImportSource成功: " + sourceModule + " -> " + resolvedPath);
                    return resolvedPath;
                }
            }

            // 尝试直接从文件路径和相对层级计算
            int relativeLevel = importStatement.getRelativeLevel();
            String sourceModuleAfterDots = sourceModule.substring(relativeLevel);

            // 获取当前文件的包路径
            String currentPackagePath = getCurrentPackagePath(currentFile);
            LOG.info("PSI解析 - 当前包路径: '" + currentPackagePath + "', 相对层级: " + relativeLevel + ", 模块后缀: '" + sourceModuleAfterDots + "'");

            // 对于项目根目录的特殊处理
            if (currentPackagePath.isEmpty()) {
                // 在项目根目录，单点相对导入应该返回空字符串
                if (relativeLevel == 1 && sourceModuleAfterDots.isEmpty()) {
                    LOG.info("项目根目录的单点相对导入，返回空字符串");
                    return "";
                }
                // 其他情况返回去掉点号的部分
                return sourceModuleAfterDots;
            }

            // 非根目录的处理
            String[] packageParts = currentPackagePath.split("\\.");
            int targetLevel = Math.max(0, packageParts.length - (relativeLevel - 1));

            StringBuilder resolvedPath = new StringBuilder();
            for (int i = 0; i < targetLevel; i++) {
                if (i > 0) {
                    resolvedPath.append(".");
                }
                resolvedPath.append(packageParts[i]);
            }

            // 追加模块后缀
            if (!sourceModuleAfterDots.isEmpty()) {
                if (!resolvedPath.isEmpty()) {
                    resolvedPath.append(".");
                }
                resolvedPath.append(sourceModuleAfterDots);
            }

            String result = resolvedPath.toString();
            LOG.info("PSI增强解析结果: " + sourceModule + " -> " + result);
            return result;

        } catch (Exception e) {
            LOG.debug("PSI enhanced relative import resolution failed: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 使用PSI解析能力获取模块的真实路径
     *
     * @param referenceExpression 引用表达式
     * @return 解析后的模块路径，如果无法解析则返回null
     */
    private static String resolveModulePathFromPSI(PyReferenceExpression referenceExpression) {
        try {
            PsiElement resolvedElement = referenceExpression.getReference().resolve();

            if (resolvedElement != null) {
                // 如果解析到的是Python文件，获取其模块路径
                if (resolvedElement instanceof PyFile) {
                    PyFile resolvedFile = (PyFile) resolvedElement;
                    return getLogicalModulePathFromPyFile(resolvedFile);
                }

                // 如果解析到的是其他类型的元素，尝试获取其所在文件的模块路径
                PyFile containingFile = (PyFile) resolvedElement.getContainingFile();
                if (containingFile != null) {
                    return getLogicalModulePathFromPyFile(containingFile);
                }
            }

        } catch (Exception e) {
            LOG.debug("Error resolving module path from PSI: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从PyFile获取模块路径
     *
     * @param pyFile Python文件
     * @return 模块路径
     */
    private static String getModulePathFromPyFile(PyFile pyFile) {
        try {
            String virtualFilePath = pyFile.getVirtualFile().getPath();
            String projectBasePath = pyFile.getProject().getBasePath();

            if (projectBasePath != null && virtualFilePath.startsWith(projectBasePath)) {
                // 计算相对于项目根目录的路径
                String relativePath = virtualFilePath.substring(projectBasePath.length());

                // 移除开头的斜杠
                if (relativePath.startsWith("/")) {
                    relativePath = relativePath.substring(1);
                }

                // 移除文件扩展名
                if (relativePath.endsWith(".py")) {
                    relativePath = relativePath.substring(0, relativePath.length() - 3);
                }

                // 如果是__init__.py文件，移除__init__部分
                if (relativePath.endsWith("/__init__")) {
                    relativePath = relativePath.substring(0, relativePath.length() - 9);
                }

                // 将路径分隔符替换为点号
                return relativePath.replace("/", ".");
            }

        } catch (Exception e) {
            LOG.debug("Error getting module path from PyFile: {}", e.getMessage());
        }

        return "";
    }

    /**
     * 从PyFile获取逻辑模块路径（使用 PythonModuleResolver，避免包含文件系统路径）
     *
     * @param pyFile Python文件
     * @return 逻辑模块路径，例如 "pydantic.main" 而不是 ".venv.lib.python3.10.site-packages.pydantic.main"
     */
    private static String getLogicalModulePathFromPyFile(PyFile pyFile) {
        try {
            // 使用 PythonModuleResolver 获取模块信息
            PythonModuleResolver.ModuleInfo moduleInfo = PythonModuleResolver.resolveModuleInfo(pyFile);

            // 根据模块类型返回不同的路径格式
            switch (moduleInfo.type()) {
                case STANDARD_LIBRARY:
                    // 对于标准库，如果是 builtins，使用 moduleName；否则使用 PYTHON_STDLIB.moduleName 的简化版本
                    if ("builtins".equals(moduleInfo.moduleName())) {
                        return "builtins";
                    } else {
                        return moduleInfo.moduleName();
                    }
                case THIRD_PARTY:
                    // 对于第三方库，尝试从文件路径推断逻辑包结构
                    return extractLogicalThirdPartyPath(pyFile);
                case PROJECT:
                    // 对于项目内部文件，使用完整的包名
                    // 特殊处理：如果当前是 __init__.py 文件，包名已经是正确的
                    return moduleInfo.packageName();
                default:
                    // 未知类型，回退到原来的方法
                    return getModulePathFromPyFile(pyFile);
            }

        } catch (Exception e) {
            LOG.debug("Error getting logical module path from PyFile: {}", e.getMessage());
            // 出错时回退到原来的方法
            return getModulePathFromPyFile(pyFile);
        }
    }

    /**
     * 从第三方库文件中提取逻辑路径
     *
     * @param pyFile Python文件
     * @return 逻辑模块路径，例如 "pydantic.main"
     */
    private static String extractLogicalThirdPartyPath(PyFile pyFile) {
        try {
            String virtualFilePath = pyFile.getVirtualFile().getPath();

            // 查找 site-packages 位置
            int sitePackagesIndex = virtualFilePath.lastIndexOf("site-packages");
            if (sitePackagesIndex != -1) {
                String afterSitePackages = virtualFilePath.substring(sitePackagesIndex + "site-packages".length());

                // 移除开头的路径分隔符
                if (afterSitePackages.startsWith("/") || afterSitePackages.startsWith("\\")) {
                    afterSitePackages = afterSitePackages.substring(1);
                }

                // 移除文件扩展名
                if (afterSitePackages.endsWith(".py")) {
                    afterSitePackages = afterSitePackages.substring(0, afterSitePackages.length() - 3);
                }

                // 如果是__init__.py文件，移除__init__部分
                if (afterSitePackages.endsWith("/__init__") || afterSitePackages.endsWith("\\__init__")) {
                    afterSitePackages = afterSitePackages.substring(0, afterSitePackages.length() - 9);
                }

                // 处理 Windows 和 Unix 路径中的 __init__ 情况
                if (afterSitePackages.endsWith(".__init__")) {
                    afterSitePackages = afterSitePackages.substring(0, afterSitePackages.length() - 9);
                }

                // 将路径分隔符替换为点号
                String logicalPath = afterSitePackages.replace("/", ".").replace("\\", ".");

                LOG.debug("Extracted logical third-party path: {} -> {}", virtualFilePath, logicalPath);
                return logicalPath;
            }

        } catch (Exception e) {
            LOG.debug("Error extracting logical third-party path: {}", e.getMessage());
        }

        // 回退到原来的方法
        return getModulePathFromPyFile(pyFile);
    }

    /**
     * 通过路径计算来解析相对导入（回退方法）
     *
     * @param importStatement 导入语句
     * @param sourceModule    原始模块名
     * @return 解析后的绝对模块路径
     */
    private static String resolveRelativeImportPathByCalculation(PyFromImportStatement importStatement, String sourceModule) {
        try {
            // 获取当前文件的包路径
            PyFile currentFile = (PyFile) importStatement.getContainingFile();
            String currentPackagePath = getCurrentPackagePath(currentFile);


            if (currentPackagePath.isEmpty()) {
                // 如果无法确定当前包路径，尝试从文件路径直接计算
                currentPackagePath = calculatePackagePathFromFilePath(currentFile);
                LOG.info("回退包路径计算结果: '" + currentPackagePath + "'");

                if (currentPackagePath.isEmpty()) {
                    // 仍然为空，返回去掉点号的模块名（去相对化）
                    String result = sourceModule.substring(countLeadingDots(sourceModule));
                    LOG.info("无法确定包路径，返回去相对化结果: '" + result + "'");
                    return result.isEmpty() ? sourceModule : result;
                }
            }

            // 计算相对路径的层级数
            int relativeLevels = countLeadingDots(sourceModule);
            String moduleAfterDots = sourceModule.substring(relativeLevels);
            LOG.info("相对层级数: " + relativeLevels);
            LOG.info("点号后的模块名: '" + moduleAfterDots + "'");

            // 根据相对层级计算目标包路径
            String[] packageParts = currentPackagePath.split("\\.");
            LOG.info("当前包路径分段: " + java.util.Arrays.toString(packageParts));

            // 处理空包路径的情况（项目根目录）
            if (packageParts.length == 1 && packageParts[0].isEmpty()) {
                LOG.info("包路径为空（项目根目录），相对层级: " + relativeLevels + ", 模块后缀: '" + moduleAfterDots + "'");

                // 对于项目根目录的相对导入处理
                if (relativeLevels == 1 && moduleAfterDots.isEmpty()) {
                    // 单点相对导入 from . import xxx，返回空字符串
                    LOG.info("项目根目录单点相对导入，返回空字符串");
                    return "";
                } else {
                    // 其他情况，直接返回去掉点号的部分
                    LOG.info("项目根目录其他相对导入，返回去点号结果: '" + moduleAfterDots + "'");
                    return moduleAfterDots;
                }
            }

            // 对于相对导入，需要根据点号数量向上回溯
            // . 表示当前包，.. 表示上级包，以此类推
            int targetLevel;
            if (relativeLevels == 1) {
                // 单点 (.) 表示同一包级别
                targetLevel = packageParts.length;
                LOG.info("单点导入，目标层级: " + targetLevel);
            } else {
                // 多点 (..) 表示向上回溯
                // .. 向上1级，... 向上2级，以此类推
                targetLevel = Math.max(0, packageParts.length - (relativeLevels - 1));
                LOG.info("多点导入，向上" + (relativeLevels - 1) + "级，目标层级: " + targetLevel);
            }

            StringBuilder resolvedPath = new StringBuilder();
            for (int i = 0; i < targetLevel; i++) {
                if (i > 0) {
                    resolvedPath.append(".");
                }
                resolvedPath.append(packageParts[i]);
            }
            LOG.info("基础解析路径: '" + resolvedPath.toString() + "'");

            // 追加相对导入的模块部分
            if (!moduleAfterDots.isEmpty()) {
                if (!resolvedPath.isEmpty()) {
                    resolvedPath.append(".");
                }
                resolvedPath.append(moduleAfterDots);
            }

            LOG.info("最终相对导入解析结果: " + sourceModule + " -> " + resolvedPath.toString());
            LOG.info("*** 相对导入计算完成 ***");
            return resolvedPath.toString();

        } catch (Exception e) {
            LOG.error("相对导入计算出错: " + sourceModule + " - " + e.getMessage(), e);
            return sourceModule; // 出错时返回原始模块名
        }
    }

    /**
     * 获取当前文件的包路径（不包含文件名）
     *
     * @param pyFile Python文件
     * @return 包路径，例如 "com.example.module"
     */
    private static String getCurrentPackagePath(PyFile pyFile) {


        try {
            // 方法1：使用 PythonModuleResolver 获取完整模块路径，然后提取包路径
            LOG.info("尝试方法1: 使用 PythonModuleResolver 解析");
            PythonModuleResolver.ModuleInfo moduleInfo = PythonModuleResolver.resolveModuleInfo(pyFile);

            if (moduleInfo != null) {


                if (moduleInfo.type() == com.sankuai.deepcode.astplugin.model.ModuleType.PROJECT) {
                    String fullModulePath = moduleInfo.packageName();
                    if (fullModulePath != null && !fullModulePath.isEmpty()) {
                        LOG.info("PSI resolved full module path: " + fullModulePath);

                        // 获取文件名（不包括扩展名）
                        String fileName = pyFile.getName();
                        String fileNameWithoutExt = fileName.endsWith(".py") ?
                                fileName.substring(0, fileName.length() - 3) : fileName;
                        LOG.info("文件名(无扩展名): " + fileNameWithoutExt);

                        // 如果是 __init__.py 文件，整个 packageName 就是包路径
                        if ("__init__".equals(fileNameWithoutExt)) {
                            LOG.info("这是 __init__.py 文件，使用完整路径作为包路径: " + fullModulePath);
                            return fullModulePath;
                        }

                        // 如果完整模块路径正好等于文件名，说明文件在项目根目录，包路径应该是空
                        if (fullModulePath.equals(fileNameWithoutExt)) {
                            LOG.info("文件在项目根目录，包路径为空: " + fileNameWithoutExt + " -> ''");
                            return "";
                        }

                        // 如果完整模块路径以文件名结尾，去掉文件名得到包路径
                        if (fullModulePath.endsWith("." + fileNameWithoutExt)) {
                            String packagePath = fullModulePath.substring(0,
                                    fullModulePath.length() - fileNameWithoutExt.length() - 1);
                            LOG.info("从完整模块路径提取包路径: " + packagePath + " (从 " + fullModulePath + ")");
                            return packagePath;
                        }

                        // 如果没有以文件名结尾，可能 fullModulePath 本身就是包路径
                        LOG.info("使用完整模块路径作为包路径: " + fullModulePath);
                        return fullModulePath;
                    }
                } else {
                    LOG.info("模块类型不是 PROJECT，跳过PSI解析");
                }
            } else {
                LOG.info("ModuleInfo 解析失败，返回 null");
            }

            // 方法2：回退到文件路径计算
            LOG.info("尝试方法2: 文件路径计算");
            String pathBasedPackage = calculatePackagePathFromFilePath(pyFile);
            LOG.info("文件路径计算结果: '" + pathBasedPackage + "'");
            if (!pathBasedPackage.isEmpty()) {
                LOG.info("使用文件路径计算的包路径: " + pathBasedPackage);
                return pathBasedPackage;
            }

            // 方法3：尝试使用 PyFile 的内置方法
            LOG.info("尝试方法3: 限定名称推断");
            String qualifiedName = getQualifiedName(pyFile);
            LOG.info("限定名称推断结果: '" + qualifiedName + "'");
            if (qualifiedName != null && !qualifiedName.isEmpty()) {
                LOG.info("使用限定名称作为包路径: " + qualifiedName);
                return qualifiedName;
            }

            LOG.warn("无法确定文件的包路径: " + pyFile.getName());
            return "";

        } catch (Exception e) {
            LOG.error("获取包路径时出错，文件: " + pyFile.getName() + ", 错误: " + e.getMessage(), e);
            return "";
        }
    }

    /**
     * 尝试获取 PyFile 的限定名称
     */
    private static String getQualifiedName(PyFile pyFile) {
        try {
            // 尝试从文件的虚拟路径推断包结构
            String virtualPath = pyFile.getVirtualFile().getPath();
            String projectPath = pyFile.getProject().getBasePath();

            if (projectPath != null && virtualPath.startsWith(projectPath)) {
                String relativePath = virtualPath.substring(projectPath.length());

                // 移除开头的斜杠
                if (relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                    relativePath = relativePath.substring(1);
                }

                // 移除文件名
                String fileName = pyFile.getName();
                if (relativePath.endsWith(fileName)) {
                    relativePath = relativePath.substring(0, relativePath.length() - fileName.length());
                    // 移除末尾的斜杠
                    if (relativePath.endsWith("/") || relativePath.endsWith("\\")) {
                        relativePath = relativePath.substring(0, relativePath.length() - 1);
                    }
                }

                // 转换为包名格式
                String packageName = relativePath.replace("/", ".").replace("\\", ".");

                // 如果为空，说明文件在根目录
                return packageName.isEmpty() ? "" : packageName;
            }

            return "";
        } catch (Exception e) {
            LOG.debug("Error getting qualified name: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 从文件路径计算包路径
     *
     * @param pyFile Python文件
     * @return 计算得到的包路径
     */
    private static String calculatePackagePathFromFilePath(PyFile pyFile) {
        try {
            String virtualFilePath = pyFile.getVirtualFile().getPath();

            // 查找项目根目录或Python源码根目录
            String projectBasePath = pyFile.getProject().getBasePath();
            if (projectBasePath != null && virtualFilePath.startsWith(projectBasePath)) {
                // 计算相对于项目根目录的路径
                String relativePath = virtualFilePath.substring(projectBasePath.length());

                // 移除文件名和扩展名，只保留目录路径
                String fileName = pyFile.getName();
                if (relativePath.endsWith("/" + fileName)) {
                    relativePath = relativePath.substring(0, relativePath.length() - fileName.length() - 1);
                }

                // 移除开头的斜杠
                if (relativePath.startsWith("/")) {
                    relativePath = relativePath.substring(1);
                }

                // 将路径分隔符替换为点号
                return relativePath.replace("/", ".");
            }

            return "";
        } catch (Exception e) {
            LOG.debug("Error calculating package path from file path: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 计算字符串开头的点号数量
     *
     * @param str 要检查的字符串
     * @return 开头的点号数量
     */
    private static int countLeadingDots(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (char c : str.toCharArray()) {
            if (c == '.') {
                count++;
            } else {
                break;
            }
        }
        return count;
    }

    /**
     * 分析import语句
     */
    public static void analyzeImportStatement(PyImportStatement importStatement, AnalysisResult result) {
        String importText = importStatement.getText();
        int lineNumber = PythonPsiUtils.getLineNumber(importStatement);
        String filePath = PythonAnalyzerUtils.getFilePath(importStatement.getContainingFile());

        // 对于普通import语句，提取导入的模块名
        List<String> resolvedClasses = new ArrayList<>();
        PyImportElement[] importElements = importStatement.getImportElements();
        for (PyImportElement element : importElements) {
            // 获取原始模块名称（不是别名）
            String originalName = getOriginalImportName(element, importText);
            if (originalName != null) {
                resolvedClasses.add(originalName);
            }
        }

        // 判断是否为外部导入 - 使用 IDEA PSI 能力和 PythonModuleResolver
        boolean isExternal = isPythonExternalImportWithPSI(importStatement);

        // 解析被导入的目标文件路径
        String targetFilePath = resolvePythonImportTargetFilePath(importStatement, resolvedClasses, isExternal);

        ImportInfo importInfo = new ImportInfo(importText, lineNumber, ImportInfo.ImportType.SINGLE, isExternal, resolvedClasses, filePath, targetFilePath);
        result.addImport(importInfo);
    }

    /**
     * 获取原始的导入模块名，处理 "import module as alias" 的情况
     */
    private static String getOriginalImportName(PyImportElement element, String importText) {
        try {
            // 首先尝试使用 getVisibleName()
            String visibleName = element.getVisibleName();

            // 检查是否有别名
            String asName = element.getAsName();
            if (asName != null) {
                // 有别名的情况，需要从原始导入文本中提取真实模块名
                // 对于 "import torch.distributed as dist"，我们需要提取 "torch.distributed"
                return extractModuleNameFromImportText(importText, asName);
            }

            // 没有别名的情况，visibleName 应该就是模块名
            if (visibleName != null) {
                return visibleName;
            }

            // 回退到 getName()
            return element.getName();
        } catch (Exception e) {
            LOG.debug("Error getting original import name for element: " + e.getMessage());
            return element.getName(); // 最后的回退
        }
    }

    /**
     * 从导入文本中提取模块名，处理别名情况
     */
    private static String extractModuleNameFromImportText(String importText, String alias) {
        if (importText == null || alias == null) {
            return null;
        }

        // 查找 "as alias" 的位置
        String asPattern = " as " + alias;
        int asIndex = importText.indexOf(asPattern);
        if (asIndex == -1) {
            return null;
        }

        // 从 "import " 开始到 " as " 结束的部分就是模块名
        String importPrefix = "import ";
        int importIndex = importText.indexOf(importPrefix);
        if (importIndex == -1) {
            return null;
        }

        // 提取模块名部分
        String moduleName = importText.substring(importIndex + importPrefix.length(), asIndex).trim();

        // 处理多个导入的情况，取第一个（通常是主要的）
        if (moduleName.contains(",")) {
            moduleName = moduleName.split(",")[0].trim();
        }

        return moduleName;
    }

    /**
     * Python 外部导入判断逻辑 - 使用 PSI 元素和 IDEA 的项目能力
     * 这是主要判断方法，充分利用 IDEA 的 PSI 解析和项目文件索引
     */
    private static boolean isPythonExternalImportWithPSI(PyImportStatementBase importStatement) {
        try {
            // 获取导入的模块元素
            PyImportElement[] importElements = importStatement.getImportElements();
            if (importElements.length == 0) {
                // 无导入元素，可能是语法错误，保守地认为是外部导入
                return true;
            }

            // 检查第一个导入元素（通常最具代表性）
            PyImportElement firstElement = importElements[0];

            // 处理相对导入的特殊情况
            String elementName = firstElement.getName();
            if (elementName != null && elementName.startsWith(".")) {
                // 相对导入（如 .module, ..parent）肯定是项目内部的
                return false;
            }

            // 尝试解析导入元素的真实 PSI 对象
            PsiElement resolvedElement = firstElement.resolve();
            if (resolvedElement == null) {
                // 无法解析的导入，回退到字符串分析
                return isExternalImportStringFallback(importStatement);
            }
            // 使用 PythonModuleResolver 进行精确的模块类型判断
            PythonModuleResolver.ModuleInfo moduleInfo = PythonModuleResolver.resolveModuleInfo(resolvedElement);

            // 根据模块类型判断是否为外部导入
            boolean isExternal = switch (moduleInfo.type()) {
                // 项目内部模块 - 内部导入
                case PROJECT -> false;
                // Python 标准库 - 外部导入
                case STANDARD_LIBRARY -> true;
                // 第三方库 - 外部导入
                case THIRD_PARTY -> true;
                // 未知类型保守地认为是外部导入
                case UNKNOWN -> true;
            };

            LOG.debug("PSI resolved import: {} -> type: {}, external: {}",
                    elementName, moduleInfo.type(), isExternal);
            return isExternal;

        } catch (Exception e) {
            LOG.debug("Error determining external import with PSI for {}: {}",
                    importStatement.getText(), e.getMessage());
            // 出错时回退到字符串分析
            return isExternalImportStringFallback(importStatement);
        }
    }

    /**
     * 回退的字符串分析方法 - 当 PSI 解析失败时使用
     */
    private static boolean isExternalImportStringFallback(PyImportStatementBase importStatement) {
        String importText = importStatement.getText();
        if (importText == null || importText.trim().isEmpty()) {
            return true;
        }

        // 提取模块名
        String moduleName = extractPythonModuleName(importText);

        // 相对导入（以 . 开头）认为是内部导入
        if (moduleName.startsWith(".")) {
            return false;
        }

        // 使用现有的标准库模式匹配（快速路径）
        String topLevelModule = moduleName.split("\\.")[0];
        if (PythonModulePatternMatcher.isPythonStandardLibraryModule(topLevelModule)) {
            return true;
        }

        // 无法确定时保守地认为是外部导入
        LOG.debug("String fallback for import: {} -> external (conservative)", moduleName);
        return true;
    }

    /**
     * 从导入语句中提取模块名
     */
    private static String extractPythonModuleName(String importText) {
        String cleaned = importText.trim();

        if (cleaned.startsWith("from ")) {
            // from module import item
            int importIndex = cleaned.indexOf(" import ");
            if (importIndex > 0) {
                return cleaned.substring(5, importIndex).trim();
            }
        } else if (cleaned.startsWith("import ")) {
            // import module
            return cleaned.substring(7).trim().split("\\s+")[0];
        }

        return cleaned;
    }

    /**
     * 解析Python from-import语句的目标文件路径
     */
    private static String resolvePythonTargetFilePath(PyFromImportStatement fromImport, String sourceModule, boolean isExternal) {
        try {
            // 如果是外部导入，不解析具体文件路径
            if (isExternal) {
                return null;
            }

            // 尝试通过PSI解析模块路径
            PsiElement sourceElement = fromImport.resolveImportSource();
            if (sourceElement instanceof PyFile) {
                PyFile pyFile = (PyFile) sourceElement;
                return getRelativePyFilePath(pyFile);
            }

            // 回退方案：如果无法通过PSI解析，尝试路径推断
            if (sourceModule != null && !sourceModule.isEmpty()) {
                return inferPythonModulePath(fromImport, sourceModule);
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error resolving Python target file path: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析Python import语句的目标文件路径
     */
    private static String resolvePythonImportTargetFilePath(PyImportStatement importStatement, List<String> resolvedClasses, boolean isExternal) {
        try {
            // 如果是外部导入，不解析具体文件路径
            if (isExternal) {
                return null;
            }

            // 尝试解析第一个导入元素
            PyImportElement[] elements = importStatement.getImportElements();
            if (elements.length > 0) {
                PsiElement resolved = elements[0].resolve();
                if (resolved instanceof PyFile) {
                    PyFile pyFile = (PyFile) resolved;
                    return getRelativePyFilePath(pyFile);
                }
            }

            // 回退方案：从导入的类名推断路径
            if (!resolvedClasses.isEmpty()) {
                String moduleName = resolvedClasses.get(0);
                return inferPythonModulePath(importStatement, moduleName);
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error resolving Python import target file path: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取Python文件的相对路径
     */
    private static String getRelativePyFilePath(PyFile pyFile) {
        try {
            com.intellij.openapi.project.Project project = pyFile.getProject();
            com.intellij.openapi.vfs.VirtualFile virtualFile = pyFile.getVirtualFile();

            if (virtualFile != null && project != null) {
                com.intellij.openapi.vfs.VirtualFile projectDir = project.getBaseDir();
                if (projectDir != null) {
                    return com.intellij.openapi.vfs.VfsUtilCore.getRelativePath(virtualFile, projectDir, '/');
                }
            }

            return pyFile.getName();
        } catch (Exception e) {
            LOG.debug("Error getting relative Python file path: {}", e.getMessage());
            return pyFile.getName();
        }
    }

    /**
     * 根据模块名推断Python文件路径
     */
    private static String inferPythonModulePath(PsiElement context, String moduleName) {
        try {
            if (moduleName == null || moduleName.isEmpty()) {
                return null;
            }

            // 处理相对导入
            if (moduleName.startsWith(".")) {
                PsiFile currentFile = context.getContainingFile();
                if (currentFile instanceof PyFile) {
                    String currentPath = getRelativePyFilePath((PyFile) currentFile);
                    return resolveRelativePythonPath(currentPath, moduleName);
                }
            } else {
                // 将模块名转换为文件路径
                String filePath = moduleName.replace('.', '/') + ".py";

                // 尝试在项目中查找该路径
                com.intellij.openapi.project.Project project = context.getProject();
                if (project != null) {
                    com.intellij.openapi.vfs.VirtualFile projectDir = project.getBaseDir();
                    if (projectDir != null) {
                        com.intellij.openapi.vfs.VirtualFile targetFile = projectDir.findFileByRelativePath(filePath);
                        if (targetFile != null) {
                            return filePath;
                        }
                    }
                }
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error inferring Python module path: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析Python相对导入路径
     */
    private static String resolveRelativePythonPath(String currentPath, String relativePath) {
        try {
            if (currentPath == null || relativePath == null) {
                return null;
            }

            // 计算相对路径的层级（点的数量）
            int dotCount = 0;
            for (char c : relativePath.toCharArray()) {
                if (c == '.') {
                    dotCount++;
                } else {
                    break;
                }
            }

            // 获取当前文件的目录路径
            String currentDir = currentPath.substring(0, currentPath.lastIndexOf('/'));

            // 向上回退相应的层数
            String[] pathParts = currentDir.split("/");
            int targetIndex = Math.max(0, pathParts.length - (dotCount - 1));

            StringBuilder resolvedPath = new StringBuilder();
            for (int i = 0; i < targetIndex; i++) {
                if (i > 0) resolvedPath.append('/');
                resolvedPath.append(pathParts[i]);
            }

            // 添加相对路径的模块部分
            String modulePart = relativePath.substring(dotCount);
            if (!modulePart.isEmpty()) {
                if (resolvedPath.length() > 0) {
                    resolvedPath.append('/');
                }
                resolvedPath.append(modulePart.replace('.', '/'));
            }

            resolvedPath.append(".py");
            return resolvedPath.toString();

        } catch (Exception e) {
            LOG.debug("Error resolving relative Python path: {}", e.getMessage());
            return null;
        }
    }
}