package com.sankuai.deepcode.astplugin.python.export.exporter;

import com.intellij.openapi.project.Project;

import java.io.IOException;
import java.util.List;

/**
 * Python 数据导出器接口
 * 所有具体的导出器都需要实现此接口
 *
 * <AUTHOR>
 */
public interface PythonDataExporter {

    /**
     * 导出数据到指定目录
     *
     * @param project             项目实例
     * @param analysisFileResults 包含文件路径的分析结果列表
     * @param outputDirPath       输出目录路径
     * @param timestamp           时间戳
     * @throws IOException 导出异常
     */
    void export(Project project,
                List<AnalysisFileResult> analysisFileResults,
                String outputDirPath,
                String timestamp) throws IOException;

    /**
     * 获取导出器名称
     *
     * @return 导出器名称
     */
    String getExporterName();

    /**
     * 获取导出文件扩展名
     *
     * @return 文件扩展名
     */
    String getFileExtension();

    default String escapeCsv(String value) {
        if (value == null) {
            return "";
        }
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }
}