package com.sankuai.deepcode.astplugin.python;

import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.sankuai.deepcode.astplugin.analyzer.AnalyzerService;

/**
 * Python分析器服务
 *
 * <AUTHOR>
 */
@Service
public final class PythonAnalyzerService {

    private static final Logger LOG = Logger.getInstance(PythonAnalyzerService.class);

    public PythonAnalyzerService() {
        try {
            LOG.info("Initializing Python Analyzer Service");

            // 注册Python分析器到共享服务
            PythonASTAnalyzer pythonAnalyzer = new PythonASTAnalyzer();
            AnalyzerService.getInstance().registerAnalyzer(pythonAnalyzer);

            LOG.info("Python Analyzer registered successfully: " + pythonAnalyzer.getClass().getSimpleName());
        } catch (Exception e) {
            LOG.error("Failed to register Python analyzer", e);
        }
    }

    /**
     * 手动触发分析器注册，用于调试
     */
    public void ensureAnalyzerRegistered() {
        try {
            PythonASTAnalyzer pythonAnalyzer = new PythonASTAnalyzer();
            AnalyzerService analyzerService = AnalyzerService.getInstance();

            LOG.info("Available analyzers: " + analyzerService.getAvailableAnalyzers().size());
            for (var analyzer : analyzerService.getAvailableAnalyzers()) {
                LOG.info("- " + analyzer.getClass().getSimpleName() + " supports: " + analyzer.getSupportedLanguage());
            }

            analyzerService.registerAnalyzer(pythonAnalyzer);
            LOG.info("Python analyzer re-registered. Total analyzers: " + analyzerService.getAvailableAnalyzers().size());

        } catch (Exception e) {
            LOG.error("Failed to ensure analyzer registration", e);
        }
    }
}