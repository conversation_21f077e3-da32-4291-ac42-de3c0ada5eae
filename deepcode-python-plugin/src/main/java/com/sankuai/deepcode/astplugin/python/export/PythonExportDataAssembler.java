package com.sankuai.deepcode.astplugin.python.export;

import com.intellij.openapi.project.Project;
import com.sankuai.deepcode.astplugin.export.ExportOption;
import com.sankuai.deepcode.astplugin.python.export.exporter.AnalysisFileResult;
import com.sankuai.deepcode.astplugin.python.export.exporter.PythonDataExporter;
import com.sankuai.deepcode.astplugin.python.export.exporter.PythonExporterFactory;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Python 导出数据组装器
 * 负责将分析结果分发到不同的导出器
 *
 * <AUTHOR>
 */
public class PythonExportDataAssembler {

    /**
     * 根据导出选项组装数据并写入文件
     */
    public static void assembleAndExport(Project project,
                                       List<AnalysisFileResult> analysisFileResults,
                                       ExportOption exportOption,
                                       String outputDirPath) throws IOException {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));

        // 使用导出器工厂获取对应的导出器
        PythonDataExporter exporter = PythonExporterFactory.getExporter(exportOption);
        exporter.export(project, analysisFileResults, outputDirPath, timestamp);
    }
}