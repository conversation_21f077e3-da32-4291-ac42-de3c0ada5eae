package com.sankuai.deepcode.astplugin.python.util;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.jetbrains.python.psi.PyFile;
import com.jetbrains.python.psi.PyFunction;

/**
 * Python分析器工具类
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
public class PythonAnalyzerUtils {

    /**
     * 获取项目相对文件路径
     */
    public static String getFilePath(PsiFile psiFile) {
        try {
            VirtualFile virtualFile = psiFile.getVirtualFile();
            Project project = psiFile.getProject();

            if (virtualFile != null) {
                // 获取项目根目录
                String projectBasePath = project.getBasePath();
                if (projectBasePath != null) {
                    String absolutePath = virtualFile.getPath();
                    // 计算相对于项目根目录的路径
                    if (absolutePath.startsWith(projectBasePath)) {
                        String relativePath = absolutePath.substring(projectBasePath.length());
                        // 移除开头的路径分隔符
                        if (relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                            relativePath = relativePath.substring(1);
                        }
                        // 统一使用正斜杠作为路径分隔符
                        relativePath = relativePath.replace("\\", "/");
                        return relativePath;
                    }
                }
                // 如果无法获取项目根目录，返回绝对路径
                return virtualFile.getPath();
            }
        } catch (Exception e) {
            // 静默失败，返回文件名
        }
        return psiFile.getName();
    }

    /**
     * 线程安全的行号获取方法（专门针对Python做了优化）
     */
    public static int getSafeLineNumber(PsiElement element) {
        try {
            PsiFile containingFile = element.getContainingFile();
            if (containingFile == null) {
                return 1;
            }

            // 使用文档管理器获取行号（线程安全）
            com.intellij.openapi.editor.Document document =
                    PsiDocumentManager.getInstance(element.getProject())
                            .getDocument(containingFile);

            if (document != null) {
                // 对于Python函数，尝试获取函数名标识符的位置
                if (element instanceof PyFunction pyFunction) {
                    // 尝试获取函数名标识符，这应该指向def关键字后的函数名
                    PsiElement nameIdentifier = pyFunction.getNameIdentifier();
                    if (nameIdentifier != null) {
                        int nameOffset = nameIdentifier.getTextOffset();
                        return document.getLineNumber(nameOffset) + 1;
                    }

                    // 如果获取不到函数名标识符，使用备用方法
                    return getPythonFunctionDefLineNumber(pyFunction, document);
                }

                // 获取元素在文件中的偏移量
                int offset = element.getTextOffset();
                if (offset < 0) {
                    return 1;
                }

                // 确保偏移量在有效范围内
                int safeOffset = Math.min(offset, document.getTextLength() - 1);
                safeOffset = Math.max(0, safeOffset);
                // 转换为1基索引
                return document.getLineNumber(safeOffset) + 1;
            }

            // 备用方法：手动计算行号
            String text = containingFile.getText();
            if (text != null) {
                int offset = element.getTextOffset();
                if (offset >= 0 && offset < text.length()) {
                    int lineNumber = 1;
                    for (int i = 0; i < offset; i++) {
                        if (text.charAt(i) == '\n') {
                            lineNumber++;
                        }
                    }
                    return lineNumber;
                }
            }

        } catch (Exception e) {
            // 静默失败，记录日志用于调试
            // LOG.warn("Error getting line number for element: " + e.getMessage(), e);
        }
        // 返回默认行号
        return 1;
    }

    /**
     * 获取Python函数def关键字的行号
     * 直接使用PSI提供的标准方法
     */
    private static int getPythonFunctionDefLineNumber(PyFunction function, com.intellij.openapi.editor.Document document) {
        try {
            // 使用PyFunction.getNameIdentifier()获取函数名的PSI元素
            // 这个方法应该返回函数名本身，而不包括装饰器
            PsiElement nameElement = function.getNameIdentifier();
            if (nameElement != null) {
                int nameOffset = nameElement.getTextOffset();
                return document.getLineNumber(nameOffset) + 1;
            }

            // 如果getNameIdentifier()失败，尝试使用函数体的起始位置
            // PyFunction.getStatementList()应该返回函数体，我们可以从这里向前找def
            PsiElement statementList = function.getStatementList();
            if (statementList != null) {
                int bodyOffset = statementList.getTextOffset();
                // 从函数体向前查找，找到def关键字
                String fileText = document.getText();

                // 向前查找def关键字的位置
                for (int i = bodyOffset - 1; i >= 0; i--) {
                    char c = fileText.charAt(i);
                    if (c == ':') {
                        // 找到冒号，继续向前找def
                        continue;
                    }
                    if (i >= 3 && "def".equals(fileText.substring(i - 3, i))) {
                        // 验证是独立的def关键字
                        if (i == 3 || !Character.isLetterOrDigit(fileText.charAt(i - 4))) {
                            return document.getLineNumber(i - 3) + 1;
                        }
                    }
                    // 检查async def的情况
                    if (i >= 9 && "async def".equals(fileText.substring(i - 9, i - 4))) {
                        if (i == 9 || !Character.isLetterOrDigit(fileText.charAt(i - 10))) {
                            return document.getLineNumber(i - 3) + 1;
                        }
                    }
                }
            }

            // 最终回退：使用函数的文本范围起始位置
            // 但跳过装饰器部分
            String functionText = function.getText();
            if (functionText != null) {
                int defIndex = functionText.indexOf("def ");
                if (defIndex == -1) {
                    // 处理async def
                    int asyncIndex = functionText.indexOf("async def ");
                    if (asyncIndex != -1) {
                        defIndex = asyncIndex + 6; // 指向def
                    }
                }

                if (defIndex != -1) {
                    int defAbsoluteOffset = function.getTextOffset() + defIndex;
                    return document.getLineNumber(defAbsoluteOffset) + 1;
                }
            }

            // 真正的最终回退
            return document.getLineNumber(function.getTextOffset()) + 1;

        } catch (Exception e) {
            // 回退到标准方法
            try {
                return document.getLineNumber(function.getTextOffset()) + 1;
            } catch (Exception fallbackException) {
                return 1;
            }
        }
    }

    /**
     * 查找def关键字的实际位置（处理async def情况）
     */
    private static int findActualDefPosition(String functionText) {
        try {
            if (functionText == null || functionText.isEmpty()) {
                return -1;
            }

            // 首先查找 "async def" 模式
            int asyncDefIndex = functionText.indexOf("async def");
            if (asyncDefIndex != -1) {
                // 验证前面是否为空白字符或行首
                if (asyncDefIndex == 0 || isWhitespaceOrNewline(functionText.charAt(asyncDefIndex - 1))) {
                    // 返回def关键字的位置
                    // "async " 的长度是6
                    return asyncDefIndex + 6;
                }
            }

            // 查找普通的 "def" 关键字
            int defIndex = functionText.indexOf("def");
            while (defIndex != -1) {
                // 验证这是一个完整的关键字
                boolean isValidDef = true;

                // 检查def前面的字符
                if (defIndex > 0) {
                    char prevChar = functionText.charAt(defIndex - 1);
                    if (!isWhitespaceOrNewline(prevChar)) {
                        isValidDef = false;
                    }
                }

                // 检查def后面的字符
                if (isValidDef && defIndex + 3 < functionText.length()) {
                    char nextChar = functionText.charAt(defIndex + 3);
                    if (Character.isLetterOrDigit(nextChar) || nextChar == '_') {
                        isValidDef = false;
                    }
                }

                if (isValidDef) {
                    return defIndex;
                }

                // 继续查找下一个
                defIndex = functionText.indexOf("def", defIndex + 1);
            }

            return -1;
        } catch (Exception e) {
            return -1;
        }
    }

    /**
     * 检查字符是否为空白字符或换行符
     */
    private static boolean isWhitespaceOrNewline(char c) {
        return Character.isWhitespace(c) || c == '\n' || c == '\r' || c == '@';
    }


    /**
     * 检测Python项目模块信息
     */
    public static String detectProjectModule(PsiFile psiFile) {
        try {
            VirtualFile virtualFile = psiFile.getVirtualFile();
            if (virtualFile != null) {
                VirtualFile current = virtualFile.getParent();
                while (current != null) {
                    // Python setup.py
                    VirtualFile setupFile = current.findChild("setup.py");
                    if (setupFile != null) {
                        return current.getName();
                    }

                    // Python pyproject.toml
                    VirtualFile pyprojectFile = current.findChild("pyproject.toml");
                    if (pyprojectFile != null) {
                        return current.getName();
                    }

                    // requirements.txt (简单项目)
                    VirtualFile reqFile = current.findChild("requirements.txt");
                    if (reqFile != null) {
                        return current.getName();
                    }

                    current = current.getParent();
                }
            }
        } catch (Exception e) {
            // 静默失败
        }
        return null;
    }

    /**
     * 获取Python模块名（基于文件名）
     */
    public static String getModuleName(PsiFile file) {
        try {
            if (file instanceof PyFile) {
                String fileName = file.getName();
                if (fileName.endsWith(".py")) {
                    return fileName.substring(0, fileName.length() - 3);
                }
            }
            return file.getName();
        } catch (Exception e) {
            // 静默失败
        }
        return "Unknown";
    }

    /**
     * 简化的文件有效性检查
     */
    public static boolean isValidPythonFile(PsiFile psiFile) {
        return psiFile != null && (
                psiFile instanceof PyFile ||
                        psiFile.getName().endsWith(".py") ||
                        "Python".equals(psiFile.getLanguage().getID())
        );
    }

    /**
     * 获取Python包路径（基于文件结构）
     */
    public static String getPythonPackagePath(PsiFile psiFile) {
        try {
            VirtualFile virtualFile = psiFile.getVirtualFile();
            if (virtualFile != null) {
                VirtualFile parent = virtualFile.getParent();
                StringBuilder packagePath = new StringBuilder();

                while (parent != null) {
                    // 检查是否存在__init__.py文件
                    VirtualFile initFile = parent.findChild("__init__.py");
                    if (initFile == null) {
                        break;
                    }

                    if (!packagePath.isEmpty()) {
                        packagePath.insert(0, ".");
                    }
                    packagePath.insert(0, parent.getName());
                    parent = parent.getParent();
                }

                return packagePath.toString();
            }
        } catch (Exception e) {
            // 静默失败
        }
        return "";
    }
}