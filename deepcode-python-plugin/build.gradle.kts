plugins {
    id("java")
    id("org.jetbrains.intellij.platform") version "2.6.0"
}

group = "com.sankuai.deepcode"
version = "1.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
    intellijPlatform {
        defaultRepositories()
    }
}

dependencies {
    implementation(project(":shared-core"))
    implementation(project(":shared-ui"))
    
    intellijPlatform {
        pycharmCommunity("2024.1.4")
        bundledPlugins("PythonCore")
        
        pluginVerifier()
        zipSigner()
        // instrumentationTools() 已弃用，不再需要
    }
}

intellijPlatform {
    buildSearchableOptions = false
    instrumentCode = false
    
    pluginConfiguration {
        id = "com.sankuai.deepcode.astplugin.python"
        name = "DeepCode AST Analyzer for Python"
        vendor {
            name = "Shanghai Sankuai Technology Co., Ltd."
            email = "<EMAIL>"
        }
        description = """
            AST分析器 - Python语言支持

            专门为PyCharm设计的Python代码分析插件。
            支持Python代码的AST解析、结构分析和调用关系分析。

            功能特性：
            - Python 类、函数、方法结构解析
            - 模块和包分析
            - 内嵌函数和装饰器支持
            - 函数调用关系分析
            - import/from语句分析
            - 代码统计信息
            - 可视化AST结果展示
            
            支持的文件类型：
            - .py (Python)
        """.trimIndent()
        
        ideaVersion {
            sinceBuild = "241"
            untilBuild = "252.*"
        }
    }
    
    pluginVerification {
        ides {
            recommended()
        }
    }
}

tasks {
    runIde {
        // autoReloadPlugins 在新版本中配置方式不同
        maxHeapSize = "2g"

        jvmArgs = listOf(
            "-Xms512m",
            "-Xmx2g",
            "-XX:ReservedCodeCacheSize=512m",
            "-XX:+UseConcMarkSweepGC",
            "-XX:SoftRefLRUPolicyMSPerMB=50",
            "-ea",
            "-XX:CICompilerCount=2",
            "-Dsun.io.useCanonPrefixCache=false",
            "-Djdk.http.auth.tunneling.disabledSchemes=\"\"",
            "-XX:+HeapDumpOnOutOfMemoryError",
            "-XX:-OmitStackTraceInFastThrow",
            "-Dide.show.tips.on.startup.default.value=false",
            "-Didea.ProcessCanceledException=disabled",
            // 启用详细日志输出 - 简化配置
            "-Didea.log.debug.categories=#com.sankuai.deepcode.astplugin",
            "-Dide.log.level=DEBUG",
            "-Didea.log.level=DEBUG"
        )
    }

    compileJava {
        options.compilerArgs.addAll(listOf("-Xlint:deprecation", "-Xlint:unchecked"))
    }
}