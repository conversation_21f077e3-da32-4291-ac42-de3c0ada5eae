# DeepCode Python Plugin

Python 语言专用的 AST 分析插件，支持精确的 Python 代码结构分析和调用关系提取，在有 Python 插件支持的 IDE 中提供完整的 PSI API 功能。

## 🎯 插件特性

### 核心功能
- 🐍 **完整的 Python AST 解析**：支持类、函数、方法的结构分析
- 🔍 **智能函数分析**：解析函数签名、参数、装饰器、类型注解
- 📊 **变量分析**：识别模块级变量、实例变量、类变量
- 📞 **调用关系分析**：追踪函数/方法调用，包括内置方法调用
- 📦 **包结构支持**：识别 Python 包和模块结构
- 🎯 **精确行号定位**：准确计算代码元素的位置信息

### Python 语言特性支持
- ✅ **类继承关系**：基类、多重继承、mixin
- ✅ **装饰器支持**：函数装饰器、类装饰器、属性装饰器
- ✅ **类型注解**：函数参数、返回值、变量类型注解
- ✅ **导入分析**：from...import、import、相对导入
- ✅ **内置方法调用**：str、list、dict 等内置类型方法
- ✅ **特殊方法**：`__init__`、`__str__`、`super()` 调用

## 🏗️ 适用场景

### 目标 IDE
- **PyCharm Community Edition** - 主要目标平台
- **PyCharm Professional Edition** - 完全兼容
- **IntelliJ IDEA Ultimate** (带 Python 插件)

### 支持的 Python 版本
- Python 2.7 (有限支持)
- Python 3.6+
- Python 3.8
- Python 3.9
- Python 3.10+

## 📦 安装和配置

### 前置条件
确保 IDE 中已安装并启用 Python 插件：
- PyCharm 自带 Python 支持
- IntelliJ IDEA 需要安装 Python 插件

### 安装方式一：构建安装
```bash
# 构建 Python 插件
./gradlew :deepcode-python-plugin:build

# 生成的插件位于
# deepcode-python-plugin/build/distributions/deepcode-python-plugin-*.zip
```

### 安装方式二：开发模式
```bash
# 启动带插件的 PyCharm 开发环境
./gradlew :deepcode-python-plugin:runIde

# 在沙盒环境中测试插件
./gradlew :deepcode-python-plugin:runIdeSandboxed
```

### 手动安装
1. 打开 PyCharm 或 IntelliJ IDEA (带 Python 插件)
2. `File` → `Settings` → `Plugins`  
3. 齿轮图标 → `Install Plugin from Disk...`
4. 选择构建生成的 zip 文件
5. 重启 IDE

## 🚀 使用方法

### 基本使用

1. **打开 Python 文件** 
   - 在 IDE 中打开任意 `.py` 文件
   - 确保文件属于一个有效的 Python 项目
   - 建议配置 Python 解释器

2. **启动分析**
   ```
   方式一：右键菜单 → "Quick AST Analyzer"
   方式二：快捷键 Ctrl+Alt+Shift+T
   方式三：Tools → AST Analysis → Quick Analysis
   ```

3. **查看结果**
   - 弹窗显示分析摘要
   - 工具窗口显示详细信息
   - 支持导出为 JSON 格式

### 工具窗口使用

通过 `View` → `Tool Windows` → `AST Analysis` 打开工具窗口，包含：

- **节点树**：显示类、函数、变量的层级结构
- **调用关系**：显示函数/方法间的调用关系
- **统计信息**：显示代码结构统计数据

### 导出功能

支持将分析结果导出为：
- **JSON 格式**：完整的结构化数据
- **CSV 格式**：适合数据分析
- **HTML 报告**：可视化分析报告

## 🔍 分析示例

### Python 类分析

对于以下 Python 代码：

```python
# user_service.py
from typing import List, Optional
from dataclasses import dataclass

@dataclass  
class User:
    id: int
    name: str
    email: str

class UserService:
    def __init__(self, repository):
        self.repository = repository
        self.cache = {}
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        if user_id in self.cache:
            return self.cache[user_id]
        
        user_data = self.repository.find_by_id(user_id)
        if user_data:
            user = User(**user_data)
            self.cache[user_id] = user
            return user
        return None
    
    def get_all_users(self) -> List[User]:
        return [User(**data) for data in self.repository.find_all()]
```

### 分析结果

**识别的节点**：
```
1. CLASS: user_service.User
   - 位置: user_service.py:4
   - 装饰器: @dataclass
   - 基类: None

2. FIELD: user_service.User.id  
   - 位置: user_service.py:5
   - 类型注解: int

3. FIELD: user_service.User.name
   - 位置: user_service.py:6
   - 类型注解: str

4. CLASS: user_service.UserService
   - 位置: user_service.py:9
   - 基类: None

5. METHOD: user_service.UserService.__init__
   - 位置: user_service.py:10
   - 参数: self, repository
   - 返回类型: None (构造函数)

6. METHOD: user_service.UserService.get_user_by_id
   - 位置: user_service.py:14
   - 参数: self, user_id: int
   - 返回类型: Optional[User]

7. METHOD: user_service.UserService.get_all_users
   - 位置: user_service.py:25
   - 参数: self
   - 返回类型: List[User]
```

**调用关系**：
```
1. UserService.get_user_by_id() → self.repository.find_by_id() [外部调用]
   - 位置: user_service.py:18

2. UserService.get_user_by_id() → User.__init__() [内部调用]
   - 位置: user_service.py:20

3. UserService.get_all_users() → self.repository.find_all() [外部调用]
   - 位置: user_service.py:26

4. UserService.get_all_users() → User.__init__() [内部调用]
   - 位置: user_service.py:26 (列表推导式中)
```

## 🔧 特殊功能

### 包结构识别

自动识别 Python 包结构：

```python
# 自动检测包层级
# 通过 __init__.py 文件识别包
# 支持命名空间包 (PEP 420)
```

### 内外部调用区分

**内部调用判定条件**：
- 被调用函数在同一模块中定义
- 被调用类在同一项目中定义
- 自定义类的方法调用

**外部调用判定条件**：
- Python 内置函数：`len()`, `str()`, `list()` 等
- 内置类型方法：`str.upper()`, `list.append()` 等  
- 第三方库：`numpy.*`, `pandas.*`, `requests.*`
- 标准库：`os.*`, `sys.*`, `json.*`

### 装饰器处理

支持多种装饰器分析：

```python
# 函数装饰器
@property
@staticmethod
@classmethod
def decorated_method(self):
    pass

# 类装饰器
@dataclass
@singleton
class DecoratedClass:
    pass
```

### 类型注解解析

解析现代 Python 类型注解：

```python
from typing import Union, Optional, List, Dict, Callable

def process_data(
    data: List[Dict[str, Union[str, int]]],
    processor: Callable[[Dict], str],
    default: Optional[str] = None
) -> List[str]:
    return [processor(item) for item in data]
```

## 🛠️ 开发指南

### 环境搭建

```bash
# 克隆项目  
git clone <repository>
cd deepcode-ast-plugin

# 确保 Python 环境
python --version  # Python 3.8+ 推荐

# 构建项目
./gradlew :deepcode-python-plugin:build

# 运行开发环境
./gradlew :deepcode-python-plugin:runIde
```

### 代码结构

```
deepcode-python-plugin/src/main/java/com/sankuai/deepcode/astplugin/python/
├── PythonASTAnalyzer.java          # 主分析器实现
├── PythonNodeVisitor.java          # Python PSI 节点访问器
├── PythonCallAnalyzer.java         # 调用关系分析器
├── PythonImportAnalyzer.java       # 导入语句分析器  
├── PythonTypeAnalyzer.java         # 类型注解分析器
└── util/
    ├── PythonPsiUtils.java         # Python PSI 工具方法
    ├── PythonSignatureUtils.java   # 函数签名工具
    └── PythonPackageDetector.java  # Python 包检测
```

### 核心分析流程

```java
public class PythonASTAnalyzer implements ASTAnalyzer {
    
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        return ReadAction.compute(() -> {
            AnalysisResult result = new AnalysisResult();
            
            // 1. 分析文件结构  
            analyzeFileStructure(psiFile, result);
            
            // 2. 分析调用关系
            analyzeCallRelations(psiFile, result);
            
            // 3. 分析导入关系
            analyzeImports(psiFile, result);
            
            // 4. 分析类型注解
            analyzeTypeAnnotations(psiFile, result);
            
            // 5. 计算统计信息
            calculateStatistics(result);
            
            return result;
        });
    }
}
```

### Python PSI 元素处理

```java
public class PythonNodeVisitor extends PyElementVisitor {
    
    @Override
    public void visitPyClass(PyClass pyClass) {
        // 处理类定义
        analyzeClass(pyClass);
        super.visitPyClass(pyClass);
    }
    
    @Override  
    public void visitPyFunction(PyFunction pyFunction) {
        // 处理函数定义
        analyzeFunction(pyFunction);
        super.visitPyFunction(pyFunction);
    }
    
    @Override
    public void visitPyCallExpression(PyCallExpression pyCall) {
        // 处理函数调用
        analyzeCall(pyCall);
        super.visitPyCallExpression(pyCall);
    }
}
```

### 扩展分析器

添加新的 Python 语言特性支持：

```java
public class EnhancedPythonAnalyzer extends PythonASTAnalyzer {
    
    @Override
    protected void analyzeSpecialCases(PsiElement element, AnalysisResult result) {
        super.analyzeSpecialCases(element, result);
        
        // 添加异步函数分析
        if (element instanceof PyFunction) {
            PyFunction func = (PyFunction) element;
            if (func.isAsync()) {
                analyzeAsyncFunction(func, result);
            }
        }
        
        // 添加上下文管理器分析
        if (element instanceof PyWithStatement) {
            analyzeContextManager((PyWithStatement) element, result);
        }
    }
}
```

### 调试技巧

#### 启用调试模式

```bash
# 方式一：环境变量
export AST_ANALYZER_DEBUG=true
./gradlew :deepcode-python-plugin:runIde

# 方式二：JVM 参数  
./gradlew :deepcode-python-plugin:runIde -Dast.analyzer.debug=true
```

#### 查看调试输出

```java
private void debugLog(String message, Object... args) {
    if (isDebugEnabled()) {
        logger.info("[PYTHON-DEBUG] " + String.format(message, args));
    }
}

// 使用示例
debugLog("Analyzing function: %s at line %d", functionName, lineNumber);
debugLog("Found call: %s -> %s", caller, callee);
```

### 性能优化

#### PSI 访问优化

```java
// ✅ 正确：批量访问 Python PSI
public void analyzeFunctions(PyClass pyClass, AnalysisResult result) {
    ReadAction.run(() -> {
        PyFunction[] methods = pyClass.getMethods();
        for (PyFunction method : methods) {
            analyzeMethod(method, result);
        }
    });
}

// ✅ 处理 Python 特有的动态特性
private boolean isCallable(PyExpression expression) {
    return ReadAction.compute(() -> {
        PyType type = TypeEvalContext.codeAnalysis(expression.getProject(), 
                                                  expression.getContainingFile())
                                   .getType(expression);
        return type instanceof PyCallableType;
    });
}
```

#### 缓存使用

```java
private final Map<String, AnalysisResult> pythonFileCache = new ConcurrentHashMap<>();

public AnalysisResult analyze(PsiFile psiFile) {
    String fileKey = generateCacheKey(psiFile);
    return pythonFileCache.computeIfAbsent(fileKey, key -> {
        return performPythonAnalysis(psiFile);
    });
}
```

## 🐍 Python 特有处理

### 动态特性处理

```java
// 处理动态属性访问
private void analyzeDynamicAccess(PyReferenceExpression ref, AnalysisResult result) {
    // getattr(), setattr(), delattr() 调用
    // 属性字典访问 obj.__dict__['attr']
    // 动态方法调用 getattr(obj, method_name)()
}

// 处理元类和描述符
private void analyzeMetaclassFeatures(PyClass pyClass, AnalysisResult result) {
    // __new__, __init__ 方法分析
    // 描述符协议 __get__, __set__
    // 元类定义分析
}
```

### 异步代码分析

```java
// 分析异步函数和协程
public void analyzeAsyncCode(PyFunction asyncFunc, AnalysisResult result) {
    if (asyncFunc.isAsync()) {
        // await 表达式分析
        // async with 语句
        // async for 循环
    }
}
```

### 生成器和迭代器

```java
// 分析生成器函数
private void analyzeGenerator(PyFunction generator, AnalysisResult result) {
    // yield 表达式
    // yield from 语句  
    // 生成器表达式
}
```

## ⚠️ 使用注意事项

### 项目配置

1. **确保 Python 解释器配置正确**
   - 配置项目的 Python 解释器
   - 确保虚拟环境已激活（如适用）
   - 检查 PYTHONPATH 设置

2. **处理依赖库**
   - 安装项目依赖：`pip install -r requirements.txt`
   - 确保第三方库可正常导入
   - 处理相对导入路径问题

### 性能考虑

1. **大文件处理**
   - Python 文件通常较 Java 文件小
   - 复杂的元类和装饰器可能影响性能
   - 考虑分析深度设置

2. **内存使用**
   - Python PSI 树较为复杂
   - 类型推断可能消耗额外内存

### 限制说明

1. **动态特性**
   - 运行时动态创建的类和方法无法分析
   - 复杂的元编程模式可能无法完全识别
   - eval() 和 exec() 执行的代码无法分析

2. **类型推断**
   - 没有类型注解时推断可能不准确
   - 鸭子类型特性影响精确性
   - 动态类型转换难以追踪

## 🔗 相关文档

- [项目总览](../README.md)
- [共享核心模块](../shared-core/README.md)  
- [共享UI模块](../shared-ui/README.md)
- [Java插件](../deepcode-java-plugin/README.md)
- [开发文档](../DEVELOPMENT_GUIDE.md)
- [使用手册](../USAGE_GUIDE.md)

## 🐛 问题排查

### 常见问题

1. **插件未加载**
   - 检查 PyCharm/IDEA 版本兼容性（>=2024.1.4）
   - 确认 Python 插件已启用
   - 查看 IDE 错误日志

2. **分析结果为空**
   - 启用调试模式查看详细过程
   - 检查文件是否为有效 Python 源文件
   - 确认 Python 解释器配置正确

3. **调用关系不准确**
   - 检查 Python 路径配置
   - 确认依赖库安装正确
   - 查看调试日志中的类型推断过程

4. **PSI 相关错误**
   - 确认 Python 插件版本兼容性
   - 检查文件编码是否正确 (UTF-8)
   - 验证 Python 语法是否正确

### 日志查看

IDE 日志位置：
- **Windows**: `%APPDATA%\JetBrains\<IDE>\<version>\log\idea.log`
- **macOS**: `~/Library/Logs/JetBrains/<IDE>/`
- **Linux**: `~/.cache/JetBrains/<IDE>/log/`

搜索关键词：
- `PythonASTAnalyzer`
- `PYTHON-DEBUG`  
- `deepcode.astplugin.python`

通过 DeepCode Python Plugin，您可以深入分析 Python 代码的结构和调用关系，充分利用 Python 的现代语言特性，提高代码理解和重构效率。