# Python 插件使用指南

本文档详细介绍 DeepCode Python Plugin 的使用方法、功能特性和最佳实践。

## 🎯 快速开始

### 前置检查

在使用插件前，请确认：

1. **Python 插件已启用**：
   - PyCharm 自带 Python 支持
   - IntelliJ IDEA 需要安装 Python 插件

2. **Python 解释器已配置**：
   - `File` → `Settings` → `Project` → `Python Interpreter`
   - 选择合适的 Python 版本（推荐 3.8+）

3. **项目结构正确**：
   - 包含 `.py` 文件
   - 可选：`requirements.txt`、`setup.py`、`pyproject.toml`

### 第一次使用

1. **打开 Python 文件**：选择一个中等大小的 `.py` 文件（100-300行）
2. **运行分析**：右键选择 "Quick AST Analyzer" 或按 `Ctrl+Alt+Shift+T`
3. **查看结果**：弹窗显示分析结果摘要

## 🎨 用户界面

### 快速分析对话框

分析完成后会显示如下信息：

```
┌─────── Python AST Analysis Results ─────────┐
│                                             │
│ File: user_service.py                       │
│ ✓ Analysis completed in 89ms                │
│                                             │
│ 📊 Summary:                                 │
│ • Classes: 2                                │
│ • Functions: 5                              │
│ • Variables: 3                              │
│ • Call Relations: 8                         │
│                                             │
│ 🔗 Call Relations:                          │
│ • Internal calls: 3                         │
│ • External calls: 5                         │
│                                             │
│ [View Details] [Export JSON] [Close]        │
└─────────────────────────────────────────────┘
```

### 工具窗口

通过 `View` → `Tool Windows` → `AST Analysis` 打开详细工具窗口：

#### 节点树面板
```
Python 节点
├── 📦 user_service
│   ├── 🏛️ User (dataclass)
│   │   ├── 📦 id: int
│   │   ├── 📦 name: str
│   │   └── 📦 email: str
│   ├── 🏛️ UserService
│   │   ├── ⚙️ __init__(self, repository)
│   │   ├── ⚙️ get_user_by_id(self, user_id: int) -> Optional[User]
│   │   └── ⚙️ get_all_users(self) -> List[User]
│   └── 📦 MODULE_CACHE: Dict[str, Any]
```

#### 调用关系面板
| 调用方 | 被调用方 | 类型 | 位置 |
|--------|----------|------|------|
| UserService.get_user_by_id | self.repository.find_by_id | 外部 | 行 18 |
| UserService.get_user_by_id | User.__init__ | 内部 | 行 20 |
| UserService.get_all_users | self.repository.find_all | 外部 | 行 26 |
| UserService.get_all_users | User.__init__ | 内部 | 行 26 |

#### 统计面板
- **节点统计**：类 2 个、函数 5 个、变量 3 个
- **调用统计**：总计 8 个调用关系，内部调用 3 个，外部调用 5 个
- **类型注解**：覆盖率 80%（4/5 函数有类型注解）

## 🔧 详细功能

### 节点分析

#### 类（CLASS）
- **识别内容**：类定义、继承关系、装饰器
- **提取信息**：基类、元类、类装饰器
- **示例**：
```python
@dataclass
class User(BaseModel):
    """用户实体类"""
    id: int
    name: str
    
    # 识别为：
    # 类型: CLASS
    # 名称: User
    # 基类: BaseModel
    # 装饰器: @dataclass
    # 类型注解: id: int, name: str
```

#### 函数/方法（METHOD）
- **识别内容**：函数定义、方法定义、静态方法、类方法
- **提取信息**：参数类型、返回类型、装饰器
- **示例**：
```python
@property
def full_name(self) -> str:
    """获取完整姓名"""
    return f"{self.first_name} {self.last_name}"
    
# 识别为：
# 类型: METHOD
# 名称: full_name
# 返回类型: str
# 装饰器: @property
# 参数: self
```

#### 变量（VARIABLE）
- **识别内容**：模块级变量、类变量、类型注解
- **提取信息**：变量类型、初始值
- **示例**：
```python
# 模块级变量
DEFAULT_TIMEOUT: int = 30
cache: Dict[str, Any] = {}

# 识别为两个VARIABLE节点：
# 1. DEFAULT_TIMEOUT: int = 30
# 2. cache: Dict[str, Any] = {}
```

### 调用关系分析

#### 内部调用判定
满足以下条件的调用被认为是内部调用：
- 被调用函数在同一模块中定义
- 被调用类在同一项目中定义
- 自定义类的方法调用

**判定示例**：
```python
# user_service.py
class UserService:
    def get_user(self, user_id: int) -> User:
        user_data = self._fetch_data(user_id)  # → 内部调用
        return User(user_data)                  # → 内部调用(如果User在项目中)
    
    def _fetch_data(self, user_id: int) -> dict:
        # 私有方法
        pass
```

#### 外部调用判定
包括以下类型的调用：

1. **Python 内置函数**：
```python
len(my_list)           # 内置函数
str(123)               # 类型转换
print("hello")         # 内置函数
```

2. **内置类型方法**：
```python
my_list.append(item)       # list 方法
my_dict.get("key")         # dict 方法
my_string.upper()          # str 方法
```

3. **第三方库**：
```python
requests.get(url)              # requests 库
pandas.DataFrame(data)         # pandas 库
numpy.array([1, 2, 3])        # numpy 库
```

4. **标准库**：
```python
json.loads(data)               # json 标准库
os.path.join(a, b)            # os 标准库
datetime.now()                 # datetime 标准库
```

### Python 特有功能

#### 装饰器分析

插件能识别各种装饰器：

```python
# 函数装饰器
@staticmethod
@classmethod
@property
def decorated_method():
    pass

# 类装饰器
@dataclass
@singleton
@attr.s
class DecoratedClass:
    pass

# 参数化装饰器
@cache(maxsize=128)
@retry(attempts=3)
def complex_function():
    pass
```

#### 类型注解解析

解析现代 Python 类型注解：

```python
from typing import List, Dict, Optional, Union, Callable

def process_data(
    items: List[Dict[str, Union[str, int]]],
    processor: Callable[[Dict], str],
    default: Optional[str] = None
) -> List[str]:
    """
    识别为：
    - 参数类型: List[Dict[str, Union[str, int]]]
    - 参数类型: Callable[[Dict], str]  
    - 参数类型: Optional[str]
    - 返回类型: List[str]
    """
    return [processor(item) for item in items]
```

#### 魔法方法识别

识别 Python 特殊方法：

```python
class CustomContainer:
    def __init__(self, items):
        self.items = items
    
    def __len__(self):          # 长度方法
        return len(self.items)
    
    def __getitem__(self, key): # 索引访问
        return self.items[key]
    
    def __str__(self):          # 字符串表示
        return str(self.items)
```

## 📊 导出和报告

### JSON 导出格式

```json
{
  "filePath": "user_service.py",
  "language": "PYTHON",
  "analysisTime": "2024-06-18T10:30:45.123Z",
  "nodes": {
    "user_service.User": {
      "id": "user_service.User",
      "type": "CLASS",
      "name": "User",
      "packageName": "user_service",
      "lineNumber": 5,
      "signature": "@dataclass class User",
      "language": "PYTHON",
      "decorators": ["dataclass"],
      "baseClasses": []
    },
    "user_service.UserService.get_user_by_id": {
      "id": "user_service.UserService.get_user_by_id",
      "type": "METHOD",
      "name": "get_user_by_id",
      "packageName": "user_service",
      "lineNumber": 14,
      "signature": "get_user_by_id(self, user_id: int) -> Optional[User]",
      "language": "PYTHON",
      "parameters": ["self", "user_id: int"],
      "returnType": "Optional[User]"
    }
  },
  "callRelations": [
    {
      "callerId": "user_service.UserService.get_user_by_id",
      "calleeId": "self.repository.find_by_id",
      "isInternal": false,
      "instances": [
        {
          "lineNumber": 18,
          "context": "self.repository.find_by_id(user_id)"
        }
      ]
    }
  ],
  "imports": [
    {
      "module": "typing",
      "items": ["List", "Optional"],
      "isFromImport": true,
      "line": 2
    },
    {
      "module": "dataclasses",
      "items": ["dataclass"],
      "isFromImport": true,  
      "line": 3
    }
  ]
}
```

### CSV 导出格式

**节点信息表 (nodes.csv)**：
```csv
ID,Type,Name,Package,File,Line,Signature,Decorators,ReturnType
user_service.User,CLASS,User,user_service,user_service.py,5,"@dataclass class User",dataclass,
user_service.UserService.get_user_by_id,METHOD,get_user_by_id,user_service,user_service.py,14,"get_user_by_id(self, user_id: int) -> Optional[User]",,Optional[User]
```

**调用关系表 (calls.csv)**：
```csv
Caller,Callee,Internal,Line,Context
user_service.UserService.get_user_by_id,self.repository.find_by_id,false,18,self.repository.find_by_id(user_id)
```

## 🎯 使用场景

### 代码理解

**场景**：理解一个新的 Python 项目结构

**操作步骤**：
1. 从项目的入口文件开始（如 `main.py`、`app.py`）
2. 运行 AST 分析
3. 查看导入关系，了解模块依赖
4. 分析类和函数的调用关系

**收益**：
- 快速掌握项目架构
- 理解模块间依赖关系
- 识别核心业务逻辑

### API 文档生成

**场景**：为 Python 模块生成 API 文档

**操作步骤**：
1. 分析所有公共模块文件
2. 导出 JSON 格式结果
3. 提取类、函数签名和类型注解
4. 结合代码注释生成文档

**收益**：
- 自动化文档生成
- 类型信息完整
- API 接口清晰

### 类型检查准备

**场景**：在项目中引入 mypy 类型检查

**操作步骤**：
1. 分析现有代码的类型注解覆盖率
2. 识别缺少类型注解的函数和方法
3. 查看外部库调用，确定需要的 stub 文件
4. 逐步添加类型注解

**收益**：
- 提高代码质量
- 减少类型相关错误
- 改善IDE支持

### 重构影响分析

**场景**：重构类或函数用，需要评估影响范围

**操作步骤**：
1. 分析包含目标类/函数的文件
2. 查看调用关系，找出所有调用者
3. 导出调用关系数据进行分析
4. 制定重构计划

**收益**：
- 降低重构风险
- 确保修改完整性
- 提高重构效率

## ⚙️ 高级配置

### 调试模式

启用详细的调试输出：

```bash
# 方式一：环境变量
export AST_ANALYZER_DEBUG=true
./gradlew :deepcode-python-plugin:runIde

# 方式二：IDE 启动参数
-Dast.analyzer.debug=true
```

调试输出示例：
```
[PYTHON-DEBUG] Starting analysis for: user_service.py
[PYTHON-DEBUG] Found class: User at line 5 with decorator @dataclass
[PYTHON-DEBUG] Found method: get_user_by_id at line 14 with return type Optional[User]
[PYTHON-DEBUG] Analyzing call: self.repository.find_by_id(user_id) at line 18
[PYTHON-DEBUG] Resolved call target: repository.find_by_id - external
[PYTHON-DEBUG] Analysis completed: 8 nodes, 4 call relations
```

### Python 解释器配置

确保正确配置 Python 解释器：

```python
# 检查当前解释器版本
import sys
print(f"Python版本: {sys.version}")
print(f"解释器路径: {sys.executable}")

# 检查重要库是否可用
try:
    import typing
    print("✓ typing 模块可用")
except ImportError:
    print("✗ typing 模块不可用")
```

### 虚拟环境支持

插件支持 Python 虚拟环境：

```bash
# 创建虚拟环境
python -m venv myproject_env

# 激活虚拟环境 
source myproject_env/bin/activate  # Linux/Mac
# 或
myproject_env\Scripts\activate     # Windows

# 安装项目依赖
pip install -r requirements.txt

# 在 IDE 中配置虚拟环境解释器
# File → Settings → Project → Python Interpreter
# 选择 myproject_env/bin/python
```

## ❗ 注意事项

### 项目要求

1. **Python 解释器配置**：
   - 必须配置有效的 Python 解释器
   - 推荐使用 Python 3.8 或更高版本
   - 虚拟环境需要正确激活

2. **依赖库安装**：
   - 项目依赖应该已安装
   - 第三方库导入可正常解析
   - 相对导入路径正确

3. **文件编码**：
   - 确保文件使用 UTF-8 编码
   - 避免非 ASCII 字符引起的解析问题

### 性能考虑

1. **大文件处理**：
   - Python 文件通常比 Java 文件小
   - 复杂的类层次可能影响分析速度
   - 大量装饰器会增加处理时间

2. **内存使用**：
   - Python PSI 树相对复杂
   - 类型推断可能消耗额外内存
   - 建议为 IDE 分配足够内存

### 限制说明

1. **动态特性**：
   - 无法分析 `eval()` 和 `exec()` 执行的代码
   - 动态创建的类和方法无法识别
   - 复杂的元类特性可能无法完全支持

2. **类型推断**：
   - 没有类型注解时推断可能不准确
   - 复杂的泛型可能解析不完整
   - 运行时类型变化无法追踪

3. **第三方库**：
   - 某些库的特殊语法可能无法识别
   - C 扩展模块的方法可能显示为外部调用
   - 动态导入的模块可能无法解析

## 🔧 故障排除

### 常见问题

**问题一：分析结果为空**

可能原因：
- Python 解释器未正确配置
- 文件语法错误
- Python 插件未启用

解决方法：
1. 检查 Python 解释器配置
2. 验证 Python 文件语法正确
3. 确认 Python 插件已启用
4. 启用调试模式查看详细日志

**问题二：类型注解无法识别**

可能原因：
- Python 版本过低（< 3.5）
- typing 模块导入失败
- 使用了不支持的类型注解语法

解决方法：
1. 使用 Python 3.6+ 版本
2. 确保 typing 模块可用：`import typing`
3. 检查类型注解语法是否正确
4. 查看 IDE 的 Python 语言级别设置

**问题三：调用关系不准确**

可能原因：
- 动态方法调用
- 复杂的继承关系
- 第三方库方法无法解析

解决方法：
1. 添加明确的类型注解
2. 确保所有依赖已正确安装
3. 检查导入路径是否正确
4. 查看调试日志中的解析过程

**问题四：装饰器分析错误**

可能原因：
- 复杂的装饰器链
- 参数化装饰器
- 自定义装饰器

解决方法：
1. 简化装饰器使用
2. 确保装饰器模块已导入
3. 检查装饰器语法是否正确

### 日志查看

查看插件运行日志：

**日志位置**：
- Windows：`%APPDATA%\JetBrains\PyCharmCE2024.1\log\idea.log`
- macOS：`~/Library/Logs/JetBrains/PyCharmCE2024.1/`
- Linux：`~/.cache/JetBrains/PyCharmCE2024.1/log/`

**关键词搜索**：
- `PythonASTAnalyzer`
- `PYTHON-DEBUG`
- `deepcode.astplugin.python`

### 性能优化建议

1. **IDE 内存配置**：
```
# pycharm64.exe.vmoptions 或 pycharm.vmoptions
-Xmx2048m
-XX:ReservedCodeCacheSize=512m
```

2. **项目优化**：
   - 排除不必要的目录（如 `venv/`, `.git/`）
   - 使用项目级别的 Python 解释器
   - 定期清理缓存：`File` → `Invalidate Caches and Restart`

通过遵循这个使用指南，您可以充分发挥 Python 插件的功能，提高 Python 代码的理解和开发效率。