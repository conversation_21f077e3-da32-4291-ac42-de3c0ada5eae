# CommonJS require() 导入 targetFilePath 解析修复

## 问题描述

在之前的实现中，CommonJS `require()` 导入的三方库 `targetFilePath` 解析存在问题：

**问题现象**：
- 对于外部三方库的 `require()` 导入（如 `require('react')`、`require('lodash')`），`targetFilePath` 字段返回 `null`
- 只有内部项目文件的 `require()` 导入才能正确解析 `targetFilePath`

**根本原因**：
在 `resolveTypeScriptRequireTargetFilePath()` 方法中，存在以下问题代码：
```java
// 如果是外部导入，返回 null
if (isExternal) {
    return null;
}
```

这导致所有外部三方库的 `targetFilePath` 都被直接设置为 `null`，无法获取实际的文件路径信息。

## 修复方案

### 修复思路

参考 ES6 导入的成功实现，为 CommonJS require 导入添加完整的路径解析策略：

1. **PSI 引用解析**：尝试通过 PSI 获取实际引用的文件
2. **增强文件解析**：使用 `ImportUtils.resolveResourceWithPSI()` 进行深度解析
3. **文件系统回退**：对于外部模块，通过文件系统查找 node_modules 中的实际文件
4. **路径推断**：最后回退到基于模块名的路径推断

### 修复实现

#### 修改文件
`deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/util/TypeScriptImportAnalyzer.java`

#### 核心修改
将 `resolveTypeScriptRequireTargetFilePath()` 方法从简单的内外部判断改为多层解析策略：

```java
private static String resolveTypeScriptRequireTargetFilePath(JSCallExpression callExpression, String moduleSpecifier, boolean isExternal) {
    try {
        // 方法1: 尝试通过PSI解析模块引用获得实际目标文件
        PsiElement resolved = resolveRequireModule(callExpression, moduleSpecifier);
        if (resolved instanceof PsiFile) {
            // 处理PSI解析成功的情况
        }

        // 方法2: 如果 PSI 解析失败，尝试使用增强的文件解析
        ImportUtils.ResourceResolutionResult resolutionResult =
            ImportUtils.resolveResourceWithPSI(callExpression, moduleSpecifier);
        
        // 方法3: 如果所有 PSI 方法都失败，对于外部模块尝试文件系统查找
        if (isExternal) {
            String fileSystemPath = resolveExternalModuleByFileSystem(callExpression, moduleSpecifier);
            // 处理文件系统解析结果
        }

        // 方法4: 最后回退到路径推断
        return inferFilePathFromModuleSpecifier(moduleSpecifier);
    }
}
```

### 解析策略详解

#### 1. PSI 引用解析
- 使用 `resolveRequireModule()` 尝试直接解析 require 调用的目标
- 如果成功解析到 `PsiFile`，获取其绝对路径并转换为项目相对路径

#### 2. 增强文件解析
- 使用 `ImportUtils.resolveResourceWithPSI()` 进行深度解析
- 支持别名路径、相对路径等复杂情况
- 自动判断内部/外部文件并返回相应路径

#### 3. 文件系统回退
- 对于外部模块，通过 `resolveExternalModuleByFileSystem()` 在 node_modules 中查找
- 支持 package.json 的 main 字段解析
- 支持常见的入口文件模式（index.js, index.ts 等）

#### 4. 路径推断
- 最后的回退方案，基于模块名推断可能的文件路径
- 处理简单的模块名到路径的映射

## 修复效果

### 修复前
```json
{
  "statement": "const React = require('react')",
  "isExternal": true,
  "targetFilePath": null  // ❌ 无法获取路径信息
}
```

### 修复后
```json
{
  "statement": "const React = require('react')",
  "isExternal": true,
  "targetFilePath": "/path/to/project/node_modules/react/index.js"  // ✅ 正确解析
}
```

## 支持的 require 类型

修复后的实现支持以下所有类型的 CommonJS require 导入：

### 1. 外部三方库
```javascript
const React = require('react');
const lodash = require('lodash');
const moment = require('moment');
```

### 2. 相对路径
```javascript
const utils = require('./utils');
const config = require('../config');
const helpers = require('../../helpers');
```

### 3. 别名路径
```javascript
const pageUtils = require('@page/actForm/utils');
const components = require('@components/Button');
```

### 4. Node.js 内置模块
```javascript
const fs = require('fs');
const path = require('path');
const http = require('http');
```

### 5. 复杂解构
```javascript
const { Component, useState } = require('react');
const { merge, cloneDeep } = require('lodash');
```

## 技术优势

### 1. 一致性
- 与 ES6 导入解析保持一致的多层策略
- 统一的路径解析逻辑和错误处理

### 2. 准确性
- 基于 PSI 的实际文件解析，确保路径正确
- 支持复杂的项目结构和配置

### 3. 可靠性
- 多层回退机制，确保在各种情况下都能提供结果
- 完善的错误处理和日志记录

### 4. 性能
- 优先使用高效的 PSI 解析
- 只在必要时才进行文件系统操作

## 验证方法

### 1. 测试文件
创建了 `test-commonjs-require.js` 包含各种 require 场景的测试用例。

### 2. 构建验证
```bash
./gradlew build
# BUILD SUCCESSFUL - 所有测试通过
```

### 3. 功能验证
- ✅ 外部三方库 require 的 targetFilePath 正确解析
- ✅ 相对路径 require 继续正常工作
- ✅ 别名路径 require 正确处理
- ✅ 所有现有功能保持完整

## 总结

此次修复成功解决了 CommonJS require 导入的 targetFilePath 解析问题，特别是外部三方库的路径解析。修复后的实现：

- ✅ **完整支持**：所有类型的 require 导入都能正确解析 targetFilePath
- ✅ **高准确性**：基于 PSI 和文件系统的多层解析策略
- ✅ **强兼容性**：与现有 ES6 导入解析保持一致
- ✅ **高可靠性**：多层回退机制确保稳定性

现在 CommonJS require 导入和 ES6 import 导入都能提供完整准确的 targetFilePath 信息，为代码分析和依赖关系追踪提供了强有力的支持。
