#!/bin/bash

# 测试相对路径解析功能的脚本

echo "=== 测试相对路径解析功能 ==="
echo ""

echo "1. 测试文件结构："
echo "   源文件: static/page/org/pages/StationMark/components/index.tsx"
echo "   目标文件: static/page/org/Stations/index.hooks.ts"
echo "   相对路径: '../../../Stations/index.hooks'"
echo ""

echo "2. 验证文件是否存在："
if [ -f "static/page/org/pages/StationMark/components/index.tsx" ]; then
    echo "   ✓ 源文件存在"
else
    echo "   ✗ 源文件不存在"
fi

if [ -f "static/page/org/Stations/index.hooks.ts" ]; then
    echo "   ✓ 目标文件存在"
else
    echo "   ✗ 目标文件不存在"
fi

echo ""
echo "3. 手动验证相对路径解析："
echo "   从 static/page/org/pages/StationMark/components/"
echo "   解析 '../../../Stations/index.hooks'"
echo "   应该得到: static/page/org/Stations/index.hooks"

# 使用 realpath 验证路径解析
cd static/page/org/pages/StationMark/components/
target_path=$(realpath ../../../Stations/index.hooks.ts 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "   ✓ realpath 解析成功: $target_path"
else
    echo "   ✗ realpath 解析失败"
fi

cd - > /dev/null

echo ""
echo "4. 检查导入语句："
echo "   在源文件中查找导入语句..."
if grep -q "from '../../../Stations/index.hooks'" static/page/org/pages/StationMark/components/index.tsx; then
    echo "   ✓ 找到目标导入语句"
    grep "from '../../../Stations/index.hooks'" static/page/org/pages/StationMark/components/index.tsx
else
    echo "   ✗ 未找到目标导入语句"
fi

echo ""
echo "5. 测试完成！"
echo "   现在可以在 IntelliJ IDEA 中打开项目并测试 PSI 解析功能"
echo "   修复后的代码应该能够正确解析多级相对路径"
