# CommonJS require() TypeScriptModuleImpl 处理修复

## 问题诊断

通过详细的调试日志，我们发现了 `require('crypto')` 返回 `null` 的真正原因：

### 日志分析结果

```
[CommonJS Debug] 子方法1: 尝试 PSI 引用解析...
[CommonJS Debug] ✅ PSI 引用解析成功: TypeScriptModuleImpl
[CommonJS Debug] ❌ PSI 解析失败，resolved = TypeScriptModuleImpl
```

**关键发现**：
1. **PSI 引用解析成功**：成功解析到了 `TypeScriptModuleImpl` 对象
2. **类型检查失败**：代码只检查 `resolved instanceof PsiFile`，但 `TypeScriptModuleImpl` 不是 `PsiFile`
3. **错误判断**：因此被误判为解析失败

## 根本原因

原始代码的问题：

```java
// 问题代码：只处理 PsiFile 类型
if (resolved instanceof PsiFile) {
    PsiFile resolvedFile = (PsiFile) resolved;
    String absolutePath = resolvedFile.getVirtualFile().getPath();
    // 处理文件路径...
} else {
    // 误判为失败！
    LOG.info("❌ PSI 解析失败，resolved = " + resolved.getClass().getSimpleName());
}
```

**问题**：Node.js 内置模块（如 `crypto`）的 PSI 解析结果是 `TypeScriptModuleImpl`，不是 `PsiFile`，因此被错误地判断为解析失败。

## 修复方案

### 1. 扩展 PSI 元素类型支持

将原来只支持 `PsiFile` 的逻辑扩展为支持所有 `PsiElement` 类型：

```java
// 修复后：支持所有 PsiElement 类型
PsiElement resolved = resolveRequireModule(callExpression, moduleSpecifier);
if (resolved != null) {
    LOG.info("✅ PSI 解析成功! 解析到: " + resolved.getClass().getSimpleName());
    
    // 尝试获取文件路径
    String absolutePath = getFilePathFromPsiElement(resolved);
    if (absolutePath != null) {
        // 处理文件路径...
        return absolutePath;
    }
}
```

### 2. 新增通用文件路径获取方法

创建了 `getFilePathFromPsiElement()` 方法来处理不同类型的 PSI 元素：

```java
private static String getFilePathFromPsiElement(PsiElement element) {
    // 方法1: 如果是 PsiFile，直接获取路径
    if (element instanceof PsiFile) {
        PsiFile file = (PsiFile) element;
        if (file.getVirtualFile() != null) {
            return file.getVirtualFile().getPath();
        }
    }

    // 方法2: 如果元素有包含文件，获取包含文件的路径
    PsiFile containingFile = element.getContainingFile();
    if (containingFile != null && containingFile.getVirtualFile() != null) {
        return containingFile.getVirtualFile().getPath();
    }

    // 方法3: 尝试通过 getNavigationElement 获取
    PsiElement navigationElement = element.getNavigationElement();
    if (navigationElement != null && navigationElement != element) {
        return getFilePathFromPsiElement(navigationElement);
    }

    // 方法4: TypeScript 模块特殊处理
    if (element.getClass().getSimpleName().contains("TypeScript") && 
        element.getClass().getSimpleName().contains("Module")) {
        // 特殊处理 TypeScript 模块...
    }

    return null;
}
```

### 3. 增强调试日志

为新的处理逻辑添加了详细的调试日志：

```java
LOG.info("[CommonJS Debug] === getFilePathFromPsiElement() for: " + element.getClass().getSimpleName() + " ===");

if (element instanceof PsiFile) {
    LOG.info("[CommonJS Debug] ✅ PsiFile 路径: " + path);
} else {
    LOG.info("[CommonJS Debug] ✅ 包含文件路径: " + path);
}
```

## 修复效果

### 修复前
```
[CommonJS Debug] ✅ PSI 引用解析成功: TypeScriptModuleImpl
[CommonJS Debug] ❌ PSI 解析失败，resolved = TypeScriptModuleImpl
[CommonJS Debug] ❌ 所有解析方法都失败，返回 null
```

### 修复后（预期）
```
[CommonJS Debug] ✅ PSI 引用解析成功: TypeScriptModuleImpl
[CommonJS Debug] ✅ PSI 解析成功! 解析到: TypeScriptModuleImpl
[CommonJS Debug] === getFilePathFromPsiElement() for: TypeScriptModuleImpl ===
[CommonJS Debug] ✅ 包含文件路径: /path/to/node_modules/@types/node/crypto.d.ts
[CommonJS Debug] 外部模块，使用绝对路径: /path/to/node_modules/@types/node/crypto.d.ts
```

## 技术要点

### 1. PSI 元素类型多样性
- **PsiFile**：普通文件
- **TypeScriptModuleImpl**：TypeScript 模块（如 Node.js 内置模块）
- **其他 PSI 元素**：各种语言结构

### 2. 文件路径获取策略
- **直接路径**：`element.getVirtualFile().getPath()`
- **包含文件路径**：`element.getContainingFile().getVirtualFile().getPath()`
- **导航元素路径**：`element.getNavigationElement()`
- **递归解析**：对复杂元素进行递归处理

### 3. 防御性编程
- 多层 null 检查
- 异常处理
- 详细日志记录

## 支持的场景

修复后的实现支持：

### Node.js 内置模块
```javascript
const crypto = require('crypto');
// PSI 解析: TypeScriptModuleImpl
// targetFilePath: /path/to/node_modules/@types/node/crypto.d.ts
```

### 外部三方库
```javascript
const React = require('react');
// PSI 解析: PsiFile 或其他类型
// targetFilePath: /path/to/node_modules/react/index.js
```

### 内部项目文件
```javascript
const utils = require('./utils');
// PSI 解析: PsiFile
// targetFilePath: src/utils.ts
```

## 验证方法

1. **编译验证**：`./gradlew compileJava` - ✅ 成功
2. **功能测试**：运行插件分析包含 `require('crypto')` 的文件
3. **日志观察**：查看详细的解析过程日志
4. **结果验证**：确认 `targetFilePath` 不再是 `null`

## 总结

此次修复解决了 CommonJS require 解析中的关键问题：

- ✅ **扩展类型支持**：不再局限于 `PsiFile`，支持所有 `PsiElement` 类型
- ✅ **通用路径获取**：新的 `getFilePathFromPsiElement()` 方法处理各种 PSI 元素
- ✅ **特殊类型处理**：专门处理 `TypeScriptModuleImpl` 等 TypeScript 特有类型
- ✅ **详细日志记录**：便于问题诊断和验证

现在 `require('crypto')` 应该能够正确解析到 Node.js 类型定义文件的路径，而不是返回 `null`！🎯
