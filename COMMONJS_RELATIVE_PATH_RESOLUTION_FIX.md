# CommonJS require() 相对路径解析修复

## 问题描述

用户反馈几个相对路径的 CommonJS require 导入存在问题：

| targetFilePath | importStatement |
|-------------|----------|
| `../../../../static/lib/vue/2.5.11/vue.common.js` | `require('../../../../static/lib/vue/2.5.11/vue.common.js')` |
| `../modules/filedownload/jquery.fileDownload.ts` | `require('../modules/filedownload/jquery.fileDownload')` |
| `./vendor/jquery.ui.widget.ts` | `require('./vendor/jquery.ui.widget')` |

**问题分析**：
1. **扩展名缺失**：`require('../modules/filedownload/jquery.fileDownload')` 应该解析到 `.ts` 文件
2. **路径解析不准确**：相对路径没有正确解析到实际存在的文件
3. **回退逻辑不够智能**：当 PSI 解析失败时，简单的路径推断不够准确

## 根本原因

### 1. 相对路径解析逻辑不足

原始的相对路径处理只是简单的字符串操作：

```java
// 问题代码：简单的路径推断
String inferredPath = inferFilePathFromModuleSpecifier(moduleSpecifier);
```

### 2. 缺少文件系统验证

没有检查推断的路径是否对应实际存在的文件。

### 3. 扩展名处理不完善

对于没有扩展名的 require，没有智能地查找对应的 `.ts`、`.js` 等文件。

## 修复方案

### 1. 增强智能路径推断

将简单的路径推断升级为智能路径推断：

```java
// 修复后：使用智能路径推断
String smartInferredPath = smartInferFilePath(callExpression, moduleSpecifier);
if (smartInferredPath != null) {
    // 转换为项目相对路径
    String relativePath = convertToProjectRelativePath(smartInferredPath, callExpression);
    return relativePath != null ? relativePath : smartInferredPath;
}
```

### 2. 新增相对路径解析方法

#### `resolveRelativePathToAbsolute()`
```java
private static String resolveRelativePathToAbsolute(String currentFileAbsolutePath, String relativeModuleSpecifier) {
    // 获取当前文件的目录
    String currentDir = currentFileAbsolutePath.substring(0, currentFileAbsolutePath.lastIndexOf('/'));
    
    // 解析相对路径
    String[] currentParts = currentDir.split("/");
    String[] relativeParts = relativeModuleSpecifier.split("/");
    
    // 处理 .. 和 . 路径
    List<String> resultParts = new ArrayList<>(Arrays.asList(currentParts));
    for (String part : relativeParts) {
        if (part.equals("..")) {
            if (!resultParts.isEmpty()) {
                resultParts.remove(resultParts.size() - 1);
            }
        } else if (!part.equals(".") && !part.isEmpty()) {
            resultParts.add(part);
        }
    }
    
    return String.join("/", resultParts);
}
```

#### `findActualFileWithExtensions()`
```java
private static String findActualFileWithExtensions(String basePath) {
    String[] extensions = {".ts", ".tsx", ".js", ".jsx", ".vue", ".d.ts"};
    
    // 1. 检查是否已经有扩展名
    for (String ext : extensions) {
        if (basePath.endsWith(ext)) {
            File file = new File(basePath);
            if (file.exists()) {
                return basePath;
            }
        }
    }
    
    // 2. 尝试添加扩展名
    for (String ext : extensions) {
        String pathWithExt = basePath + ext;
        File file = new File(pathWithExt);
        if (file.exists()) {
            return pathWithExt;
        }
    }
    
    // 3. 检查是否是目录，查找 index 文件
    File dir = new File(basePath);
    if (dir.exists() && dir.isDirectory()) {
        for (String ext : extensions) {
            String indexPath = basePath + "/index" + ext;
            File indexFile = new File(indexPath);
            if (indexFile.exists()) {
                return indexPath;
            }
        }
    }
    
    return null;
}
```

### 3. 增强的智能推断流程

```java
private static String smartInferFilePath(PsiElement context, String moduleSpecifier) {
    // 1. 获取当前文件的绝对路径
    String currentFileAbsolutePath = context.getContainingFile().getVirtualFile().getPath();
    
    if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
        // 2. 计算目标文件的绝对路径
        String targetAbsolutePath = resolveRelativePathToAbsolute(currentFileAbsolutePath, moduleSpecifier);
        
        // 3. 查找实际存在的文件（考虑扩展名）
        String actualFilePath = findActualFileWithExtensions(targetAbsolutePath);
        
        if (actualFilePath != null) {
            return actualFilePath;
        } else {
            // 4. 如果找不到实际文件，使用智能扩展名推断
            return addAppropriateExtension(targetAbsolutePath);
        }
    }
    
    return inferFilePathFromModuleSpecifier(moduleSpecifier);
}
```

## 修复效果

### 修复前
```json
{
  "statement": "require('../modules/filedownload/jquery.fileDownload')",
  "targetFilePath": "../modules/filedownload/jquery.fileDownload.ts"  // 路径可能不准确
}
```

### 修复后（预期）
```json
{
  "statement": "require('../modules/filedownload/jquery.fileDownload')",
  "targetFilePath": "modules/filedownload/jquery.fileDownload.ts"  // 正确的项目相对路径
}
```

## 技术要点

### 1. 文件系统验证
- 使用 `java.io.File` 检查文件是否实际存在
- 支持多种扩展名的自动检测
- 支持 index 文件的自动查找

### 2. 相对路径计算
- 正确处理 `../` 和 `./` 路径
- 基于当前文件的绝对路径进行计算
- 支持多级目录的相对路径

### 3. 扩展名智能处理
- 优先级：`.ts` > `.tsx` > `.js` > `.jsx` > `.vue` > `.d.ts`
- 支持已有扩展名的文件
- 支持无扩展名文件的自动补全

### 4. 详细日志记录
```
[CommonJS Debug] === smartInferFilePath() for: ../modules/filedownload/jquery.fileDownload ===
[CommonJS Debug] 当前文件绝对路径: /project/src/components/MyComponent.js
[CommonJS Debug] 检测到相对路径，开始解析...
[CommonJS Debug] ✅ 解析到目标绝对路径: /project/src/modules/filedownload/jquery.fileDownload
[CommonJS Debug] ✅ 找到实际文件（添加扩展名）: /project/src/modules/filedownload/jquery.fileDownload.ts
[CommonJS Debug] ✅ 转换为项目相对路径: src/modules/filedownload/jquery.fileDownload.ts
```

## 支持的场景

修复后的实现支持：

### 1. 基本相对路径
```javascript
const utils = require('./utils');           // → utils.ts
const config = require('../config');        // → config.ts
```

### 2. 多级相对路径
```javascript
const rootUtils = require('../../utils');   // → utils.ts
const deepLib = require('../../../lib');    // → lib/index.ts
```

### 3. 复杂路径结构
```javascript
const vueLib = require('../../../../static/lib/vue/2.5.11/vue.common.js');
// → static/lib/vue/2.5.11/vue.common.js
```

### 4. 无扩展名文件
```javascript
const fileDownload = require('../modules/filedownload/jquery.fileDownload');
// → modules/filedownload/jquery.fileDownload.ts
```

### 5. 目录导入
```javascript
const moduleIndex = require('./modules');   // → modules/index.ts
```

## 验证方法

1. **编译验证**：`./gradlew compileJava` - ✅ 成功
2. **功能测试**：运行插件分析包含相对路径 require 的文件
3. **路径检查**：确认 `targetFilePath` 指向正确的文件
4. **扩展名验证**：确认无扩展名的 require 能正确解析到对应文件
5. **日志观察**：查看详细的相对路径解析过程

## 总结

此次修复大幅提升了 CommonJS require 相对路径的解析能力：

- ✅ **文件系统验证**：检查文件是否实际存在
- ✅ **智能扩展名处理**：自动查找对应的 `.ts`、`.js` 等文件
- ✅ **准确路径计算**：基于当前文件位置正确计算相对路径
- ✅ **项目相对路径**：统一转换为项目相对路径格式
- ✅ **详细日志记录**：便于问题诊断和验证

现在相对路径的 CommonJS require 应该能够正确解析到实际存在的文件，并返回准确的项目相对路径！🎯
