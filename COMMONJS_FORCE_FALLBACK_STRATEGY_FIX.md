# CommonJS require() 强制兜底策略修复

## 问题描述

用户反馈即使 IDE 无法跳转的相对路径，也需要强制解析：

```
TS2307: Cannot find module
../page/proPractices/components/ErrorPage/Empty/EmptyPage
```

**问题分析**：
- IDE 确实无法跳转这个相对路径
- PSI 解析也失败了
- 但是需要兜底策略强制解析，不能返回 `null`

## 根本原因

### 1. 兜底策略覆盖不全

原始的兜底策略只对 `!isExternal` 的模块生效：

```java
// 问题代码：相对路径可能被错误标记为外部模块
if (!isExternal) {
    // 只有内部模块才执行兜底策略
    String smartInferredPath = smartInferFilePath(callExpression, moduleSpecifier);
} else {
    // 外部模块直接返回 null，没有兜底！
    return null;
}
```

### 2. 缺少强制解析机制

当 PSI 解析失败时，没有强制的路径计算机制。

## 修复方案

### 1. 增强兜底策略触发条件

```java
// 修复后：相对路径必须执行兜底策略
boolean isRelativePath = moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../");

if (!isExternal || isRelativePath) {
    // 对相对路径强制执行兜底解析
    if (isRelativePath) {
        LOG.info("检测到相对路径，强制执行兜底解析");
    }
    
    String smartInferredPath = forceSmartInferFilePath(callExpression, moduleSpecifier);
    // 处理结果...
}
```

### 2. 新增强制解析方法

#### `forceSmartInferFilePath()`
```java
private static String forceSmartInferFilePath(PsiElement context, String moduleSpecifier) {
    // 1. 获取当前文件的绝对路径
    String currentFileAbsolutePath = context.getContainingFile().getVirtualFile().getPath();
    
    if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
        // 2. 强制计算目标文件的绝对路径
        String targetAbsolutePath = forceResolveRelativePathToAbsolute(currentFileAbsolutePath, moduleSpecifier);
        
        // 3. 尝试找到实际存在的文件
        String actualFilePath = findActualFileWithExtensions(targetAbsolutePath);
        
        if (actualFilePath != null) {
            return actualFilePath;
        } else {
            // 4. 即使找不到实际文件，也要返回推断的路径（兜底策略）
            return forceAddAppropriateExtension(targetAbsolutePath);
        }
    }
    
    return forceInferFilePathFromModuleSpecifier(moduleSpecifier);
}
```

#### `forceResolveRelativePathToAbsolute()`
```java
private static String forceResolveRelativePathToAbsolute(String currentFileAbsolutePath, String relativeModuleSpecifier) {
    // 获取当前文件的目录
    String currentDir = currentFileAbsolutePath.substring(0, currentFileAbsolutePath.lastIndexOf('/'));
    
    // 解析相对路径
    String[] currentParts = currentDir.split("/");
    String[] relativeParts = relativeModuleSpecifier.split("/");
    
    List<String> resultParts = new ArrayList<>(Arrays.asList(currentParts));
    
    for (String part : relativeParts) {
        if (part.equals("..")) {
            if (!resultParts.isEmpty()) {
                resultParts.remove(resultParts.size() - 1);
            }
        } else if (!part.equals(".") && !part.isEmpty()) {
            resultParts.add(part);
        }
    }
    
    return String.join("/", resultParts);
}
```

#### `forceAddAppropriateExtension()`
```java
private static String forceAddAppropriateExtension(String filePath) {
    // 如果已经有扩展名，直接返回
    if (filePath.matches(".*\\.(ts|tsx|js|jsx|vue|d\\.ts)$")) {
        return filePath;
    }
    
    // 根据路径特征推断扩展名
    String extension = ".ts"; // 默认扩展名
    
    if (filePath.contains("component") || filePath.contains("Component")) {
        extension = filePath.contains("vue") ? ".vue" : ".tsx";
    } else if (filePath.contains("page") || filePath.contains("Page")) {
        extension = ".tsx";
    } else if (filePath.contains("static") || filePath.contains("lib") || filePath.contains("vendor")) {
        extension = ".js";
    }
    
    return filePath + extension;
}
```

### 3. 增强 PSI 解析尝试

#### `tryRelativePathPsiResolution()`
```java
private static PsiElement tryRelativePathPsiResolution(JSCallExpression callExpression, String moduleSpecifier) {
    // 1. 计算目标文件的绝对路径
    String targetAbsolutePath = forceResolveRelativePathToAbsolute(currentFileAbsolutePath, moduleSpecifier);
    
    // 2. 通过 VirtualFileManager 查找文件
    VirtualFileManager fileManager = VirtualFileManager.getInstance();
    String[] extensions = {".ts", ".tsx", ".js", ".jsx", ".vue", ".d.ts"};
    
    for (String ext : extensions) {
        String pathWithExt = targetAbsolutePath + ext;
        VirtualFile virtualFile = fileManager.findFileByUrl("file://" + pathWithExt);
        if (virtualFile != null) {
            PsiFile psiFile = PsiManager.getInstance(project).findFile(virtualFile);
            if (psiFile != null) {
                return psiFile;
            }
        }
    }
    
    // 3. 查找 index 文件
    for (String ext : extensions) {
        String indexPath = targetAbsolutePath + "/index" + ext;
        // 类似处理...
    }
    
    return null;
}
```

## 修复效果

### 修复前
```
TS2307: Cannot find module ../page/proPractices/components/ErrorPage/Empty/EmptyPage

{
  "statement": "require('../page/proPractices/components/ErrorPage/Empty/EmptyPage')",
  "targetFilePath": null  // PSI 解析失败，没有兜底策略
}
```

### 修复后（预期）
```
{
  "statement": "require('../page/proPractices/components/ErrorPage/Empty/EmptyPage')",
  "targetFilePath": "page/proPractices/components/ErrorPage/Empty/EmptyPage.tsx"  // 强制兜底解析
}
```

## 技术要点

### 1. 强制执行原则
- **相对路径必须解析**：无论 PSI 是否成功，相对路径都要执行兜底策略
- **永不返回 null**：即使找不到实际文件，也要返回推断的路径
- **智能扩展名推断**：根据路径特征推断最可能的扩展名

### 2. 多层兜底机制
```
1. PSI 字符串字面量引用解析
2. 相对路径 PSI 解析
3. 模块解析服务
4. 强制智能路径推断 ← 兜底策略
   ├── 文件系统验证
   ├── 扩展名推断
   └── 强制路径计算 ← 最终兜底
```

### 3. 路径特征识别
- **组件路径**：包含 `component`、`Component` → `.tsx` 或 `.vue`
- **页面路径**：包含 `page`、`Page` → `.tsx`
- **静态资源**：包含 `static`、`lib`、`vendor` → `.js`
- **默认情况**：→ `.ts`

### 4. 详细日志记录
```
[CommonJS Debug] 检测到相对路径，强制执行兜底解析
[CommonJS Debug] === forceSmartInferFilePath() for: ../page/proPractices/components/ErrorPage/Empty/EmptyPage ===
[CommonJS Debug] 当前文件绝对路径: /project/src/components/MyComponent.js
[CommonJS Debug] 强制解析相对路径...
[CommonJS Debug] ✅ 强制解析到目标绝对路径: /project/src/page/proPractices/components/ErrorPage/Empty/EmptyPage
[CommonJS Debug] ⚠️ 未找到实际文件，使用兜底路径: /project/src/page/proPractices/components/ErrorPage/Empty/EmptyPage.tsx
[CommonJS Debug] ✅ 转换为项目相对路径: src/page/proPractices/components/ErrorPage/Empty/EmptyPage.tsx
```

## 支持的场景

修复后的强制兜底策略支持：

### 1. PSI 解析失败的相对路径
```javascript
const emptyPage = require('../page/proPractices/components/ErrorPage/Empty/EmptyPage');
// → page/proPractices/components/ErrorPage/Empty/EmptyPage.tsx
```

### 2. 复杂的多级相对路径
```javascript
const deepComponent = require('../../../../static/lib/vue/2.5.11/vue.common.js');
// → static/lib/vue/2.5.11/vue.common.js
```

### 3. 无扩展名的相对路径
```javascript
const fileDownload = require('../modules/filedownload/jquery.fileDownload');
// → modules/filedownload/jquery.fileDownload.ts
```

### 4. 不存在的文件路径
```javascript
const nonExistent = require('./non-existent-file');
// → non-existent-file.ts （强制推断）
```

## 验证方法

1. **编译验证**：`./gradlew compileJava` - ✅ 成功
2. **功能测试**：运行插件分析 PSI 解析失败的相对路径 require
3. **兜底验证**：确认即使 PSI 失败也能返回合理的 `targetFilePath`
4. **路径准确性**：验证强制推断的路径是否合理
5. **日志观察**：查看详细的强制兜底解析过程

## 总结

此次修复实现了 CommonJS require 的强制兜底策略：

- ✅ **相对路径必解析**：无论 PSI 是否成功，相对路径都会被解析
- ✅ **永不返回 null**：即使找不到实际文件，也返回推断路径
- ✅ **智能扩展名推断**：根据路径特征推断最可能的文件类型
- ✅ **多层兜底机制**：从 PSI 解析到强制计算的完整兜底链
- ✅ **详细日志记录**：便于问题诊断和验证

现在即使 IDE 无法跳转的相对路径，也能通过强制兜底策略得到合理的解析结果！🎯
