# CommonJS require() 详细调试日志分析

## 问题现状

用户反馈 `require('crypto')` 的 `targetFilePath` 现在返回 `null`，需要详细分析 CommonJS require 解析过程中每个步骤的执行情况。

## 添加的详细日志

为了深入分析 CommonJS require 解析失败的原因，我在关键方法中添加了详细的调试日志：

### 1. 主解析方法日志

在 `resolveTypeScriptRequireTargetFilePath()` 方法中添加了 4 个解析方法的详细日志：

#### 方法1：PSI 解析模块引用
```java
LOG.info("[CommonJS Debug] 方法1: 开始 PSI 解析模块引用...");
System.out.println("[CommonJS Debug] 方法1: 开始 PSI 解析模块引用...");

if (resolved instanceof PsiFile) {
    LOG.info("[CommonJS Debug] ✅ PSI 解析成功! 目标文件: " + absolutePath);
    System.out.println("[CommonJS Debug] ✅ PSI 解析成功! 目标文件: " + absolutePath);
} else {
    LOG.info("[CommonJS Debug] ❌ PSI 解析失败，resolved = " + (resolved != null ? resolved.getClass().getSimpleName() : "null"));
    System.out.println("[CommonJS Debug] ❌ PSI 解析失败，resolved = " + (resolved != null ? resolved.getClass().getSimpleName() : "null"));
}
```

#### 方法2：增强文件解析
```java
LOG.info("[CommonJS Debug] 方法2: 开始增强文件解析...");
System.out.println("[CommonJS Debug] 方法2: 开始增强文件解析...");

if (resolutionResult.getAbsolutePath() != null) {
    LOG.info("[CommonJS Debug] ✅ 增强解析成功: " + resolutionResult.getAbsolutePath());
    System.out.println("[CommonJS Debug] ✅ 增强解析成功: " + resolutionResult.getAbsolutePath());
} else {
    LOG.info("[CommonJS Debug] ❌ 增强解析失败，未找到文件路径");
    System.out.println("[CommonJS Debug] ❌ 增强解析失败，未找到文件路径");
}
```

#### 方法3：文件系统查找
```java
if (isExternal) {
    LOG.info("[CommonJS Debug] 方法3: 开始文件系统查找（仅限外部模块）...");
    System.out.println("[CommonJS Debug] 方法3: 开始文件系统查找（仅限外部模块）...");
    
    if (fileSystemPath != null) {
        LOG.info("[CommonJS Debug] ✅ 文件系统查找成功: " + fileSystemPath);
        System.out.println("[CommonJS Debug] ✅ 文件系统查找成功: " + fileSystemPath);
    } else {
        LOG.info("[CommonJS Debug] ❌ 文件系统查找失败");
        System.out.println("[CommonJS Debug] ❌ 文件系统查找失败");
    }
} else {
    LOG.info("[CommonJS Debug] 跳过方法3（仅适用于外部模块）");
    System.out.println("[CommonJS Debug] 跳过方法3（仅适用于外部模块）");
}
```

#### 方法4：路径推断
```java
if (!isExternal) {
    LOG.info("[CommonJS Debug] 方法4: 开始路径推断（仅限内部模块）...");
    System.out.println("[CommonJS Debug] 方法4: 开始路径推断（仅限内部模块）...");
} else {
    LOG.info("[CommonJS Debug] 跳过方法4（外部模块不使用路径推断）");
    System.out.println("[CommonJS Debug] 跳过方法4（外部模块不使用路径推断）");
}
```

### 2. 子解析方法日志

在 `resolveRequireModule()` 方法中添加了 3 个子方法的详细日志：

#### 子方法1：PSI 引用解析
```java
LOG.info("[CommonJS Debug] 子方法1: 尝试 PSI 引用解析...");
System.out.println("[CommonJS Debug] 子方法1: 尝试 PSI 引用解析...");

if (resolved != null) {
    LOG.info("[CommonJS Debug] ✅ PSI 引用解析成功: " + resolved.getClass().getSimpleName());
    System.out.println("[CommonJS Debug] ✅ PSI 引用解析成功: " + resolved.getClass().getSimpleName());
} else {
    LOG.info("[CommonJS Debug] ❌ PSI 引用解析失败");
    System.out.println("[CommonJS Debug] ❌ PSI 引用解析失败");
}
```

#### 子方法2：require 调用表达式解析
```java
LOG.info("[CommonJS Debug] 子方法2: 尝试 require 调用表达式解析...");
System.out.println("[CommonJS Debug] 子方法2: 尝试 require 调用表达式解析...");
```

#### 子方法3：文件查找
```java
LOG.info("[CommonJS Debug] 子方法3: 尝试文件查找...");
System.out.println("[CommonJS Debug] 子方法3: 尝试文件查找...");
```

### 3. PSI 引用解析详细日志

在 `tryPsiReferenceResolution()` 方法中已有详细日志：

```java
LOG.info("=== tryPsiReferenceResolution for: " + moduleSpecifier + " ===");
LOG.info("Found " + references.length + " references for string literal: " + literal.getText());

for (PsiReference reference : references) {
    LOG.info("Processing reference: " + reference.getClass().getSimpleName());
    PsiElement resolved = reference.resolve();
    if (resolved != null) {
        LOG.info("✅ String literal reference resolved to: " + resolved.getClass().getSimpleName());
        if (resolved.getContainingFile() != null && resolved.getContainingFile().getVirtualFile() != null) {
            String resolvedPath = resolved.getContainingFile().getVirtualFile().getPath();
            LOG.info("✅ Resolved file path: " + resolvedPath);
        }
        return resolved;
    } else {
        LOG.info("❌ Reference resolved to null");
    }
}
```

## 日志输出格式

所有调试日志都使用统一的格式：

- **前缀**：`[CommonJS Debug]` 便于过滤和识别
- **状态标识**：
  - `✅` 表示成功
  - `❌` 表示失败
  - `🔍` 表示正在处理
- **双重输出**：同时使用 `LOG.info()` 和 `System.out.println()` 确保日志可见

## 预期日志输出示例

对于 `require('crypto')`，预期看到类似以下的日志输出：

```
[CommonJS Debug] === resolveTypeScriptRequireTargetFilePath() for: crypto (isExternal: true) ===
[CommonJS Debug] 方法1: 开始 PSI 解析模块引用...
[CommonJS Debug] === resolveRequireModule() for: crypto ===
[CommonJS Debug] 子方法1: 尝试 PSI 引用解析...
=== tryPsiReferenceResolution for: crypto ===
Found 1 references for string literal: 'crypto'
Processing reference: TypeScriptModuleReference
✅ String literal reference resolved to: TypeScriptFile
✅ Resolved file path: /path/to/node_modules/@types/node/crypto.d.ts
[CommonJS Debug] ✅ PSI 引用解析成功: TypeScriptFile
[CommonJS Debug] ✅ PSI 解析成功! 目标文件: /path/to/node_modules/@types/node/crypto.d.ts
[CommonJS Debug] 外部模块，使用绝对路径: /path/to/node_modules/@types/node/crypto.d.ts
```

或者如果解析失败：

```
[CommonJS Debug] === resolveTypeScriptRequireTargetFilePath() for: crypto (isExternal: true) ===
[CommonJS Debug] 方法1: 开始 PSI 解析模块引用...
[CommonJS Debug] === resolveRequireModule() for: crypto ===
[CommonJS Debug] 子方法1: 尝试 PSI 引用解析...
=== tryPsiReferenceResolution for: crypto ===
Found 0 references for string literal: 'crypto'
❌ No references resolved successfully
[CommonJS Debug] ❌ PSI 引用解析失败
[CommonJS Debug] 子方法2: 尝试 require 调用表达式解析...
[CommonJS Debug] ❌ require 调用表达式解析失败
[CommonJS Debug] 子方法3: 尝试文件查找...
[CommonJS Debug] ❌ 文件查找失败
[CommonJS Debug] ❌ resolveRequireModule 所有方法都失败: crypto
[CommonJS Debug] ❌ PSI 解析失败，resolved = null
[CommonJS Debug] 方法2: 开始增强文件解析...
[CommonJS Debug] ❌ 增强解析失败，未找到文件路径
[CommonJS Debug] 方法3: 开始文件系统查找（仅限外部模块）...
[CommonJS Debug] ❌ 文件系统查找失败
[CommonJS Debug] 跳过方法4（外部模块不使用路径推断）
[CommonJS Debug] 外部模块 'crypto' 无法解析，返回 null 以避免错误路径
[CommonJS Debug] ❌ 所有解析方法都失败，返回 null
```

## 使用方法

1. **编译项目**：`./gradlew compileJava`
2. **运行分析**：在包含 `require('crypto')` 的 JavaScript 文件上运行插件
3. **查看日志**：在控制台或日志文件中搜索 `[CommonJS Debug]`
4. **分析结果**：根据日志输出确定哪个解析步骤失败了

## 下一步

通过这些详细日志，我们可以：

1. **确定失败点**：看到具体是哪个解析方法失败了
2. **分析原因**：了解 PSI 引用解析为什么没有找到目标文件
3. **优化策略**：根据失败原因调整解析逻辑
4. **验证修复**：确认修复后的解析过程是否正确

现在请运行插件并提供日志输出，我们就能准确诊断 `require('crypto')` 解析失败的具体原因了！
