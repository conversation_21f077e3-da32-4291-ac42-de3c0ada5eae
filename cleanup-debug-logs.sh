#!/bin/bash

# 清理 TypeScript 插件中所有的 [DeepCode] 调试日志

echo "开始清理 [DeepCode] 调试日志..."

# 定义要处理的文件列表
files=(
    "deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/util/TypeScriptImportAnalyzer.java"
    "deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/util/ImportClassifier.java"
    "deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/util/ImportUtils.java"
    "deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/util/TypeScriptCallAnalyzer.java"
)

# 备份原文件
echo "备份原文件..."
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        cp "$file" "$file.backup"
        echo "已备份: $file"
    fi
done

# 删除包含 [DeepCode] 的 System.out.println 行
echo "删除调试日志..."
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "处理文件: $file"
        
        # 删除包含 [DeepCode] 的 System.out.println 行
        sed -i.tmp '/System\.out\.println.*\[DeepCode\]/d' "$file"
        
        # 删除临时文件
        rm -f "$file.tmp"
        
        echo "已处理: $file"
    else
        echo "文件不存在: $file"
    fi
done

echo "清理完成！"
echo ""
echo "检查清理结果..."

# 检查是否还有遗漏的调试日志
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        count=$(grep -c "\[DeepCode\]" "$file" 2>/dev/null || echo "0")
        if [ "$count" -gt 0 ]; then
            echo "警告: $file 中仍有 $count 行包含 [DeepCode]"
            grep -n "\[DeepCode\]" "$file"
        else
            echo "✓ $file 已清理完成"
        fi
    fi
done

echo ""
echo "如果需要恢复，可以使用备份文件："
for file in "${files[@]}"; do
    if [ -f "$file.backup" ]; then
        echo "  mv $file.backup $file"
    fi
done
