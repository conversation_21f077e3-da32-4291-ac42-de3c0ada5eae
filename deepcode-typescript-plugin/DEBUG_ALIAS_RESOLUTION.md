# 别名解析调试指南

## 问题现状

`@page/actForm/utils` 仍然被错误识别为外部模块：

```json
{
  "statement": "import { replaceToPlainStr } from '@page/actForm/utils';",
  "isExternal": true,  // ❌ 应该是 false
  "targetFilePath": null  // ❌ 应该是 "static/page/actForm/utils/index.ts"
}
```

## 添加的调试日志

我已经在关键方法中添加了详细的调试日志，这些日志会输出到控制台和日志文件中：

### 1. ImportClassifier.isExternalModuleWithPSI()

**日志前缀**: `[DeepCode]`

**关键日志点**:
- 导入语句信息
- 当前文件路径
- PSI 引用元素数量
- 每个引用元素的详细信息
- 文件路径解析结果
- 项目内外判断结果

**示例日志**:
```
[DeepCode] === PSI Module Analysis for: @page/actForm/utils ===
[DeepCode] Import statement: ...
[DeepCode] Current file: /path/to/current/file.tsx
[DeepCode] Step 1: Trying PSI reference resolution...
[DeepCode] PSI found 1 referenced elements for module: @page/actForm/utils
[DeepCode] Found 1 referenced elements, analyzing each...
[DeepCode] Element 1/1: SomeElementType
[DeepCode] Element details: ...
[DeepCode] Referenced element file path: /path/to/resolved/file.ts
[DeepCode] Checking if file is in project...
[DeepCode] CONCLUSION: TypeScript Module @page/actForm/utils is INTERNAL/EXTERNAL
```

### 2. isFileInProject()

**关键日志点**:
- 文件路径检查
- 项目根目录获取
- 路径比较结果
- node_modules 检查

**示例日志**:
```
[DeepCode] === isFileInProject() called ===
[DeepCode] Checking file path: /path/to/file.ts
[DeepCode] Getting project root...
[DeepCode] Project root path: /path/to/project
[DeepCode] Checking if file is under project root...
[DeepCode] File is under project root
[DeepCode] Checking if file is in node_modules...
[DeepCode] File is in project: internal
```

### 3. tryCommonAliasPatterns()

**关键日志点**:
- 别名模式识别
- 项目根目录
- 标准别名处理 (@/)
- 自定义别名处理

**示例日志**:
```
[DeepCode] === tryCommonAliasPatterns() for: @page/actForm/utils ===
[DeepCode] Project root: /path/to/project
[DeepCode] Processing custom alias: @page/actForm/utils
[DeepCode] Custom alias resolved: /path/to/resolved/file.ts
```

### 4. tryCustomAliasResolution()

**关键日志点**:
- 别名解析过程
- 搜索目录列表
- 别名前缀和路径解析
- 文件查找结果

**示例日志**:
```
[DeepCode] === tryCustomAliasResolution() for: @page/actForm/utils ===
[DeepCode] Will search in directories: src, static, app, lib, source
[DeepCode] Parsed alias - prefix: 'page', remaining: 'actForm/utils'
[DeepCode] Searching in directory: static
[DeepCode] Found source directory: /path/to/project/static
[DeepCode] Found alias directory: /path/to/project/static/page
[DeepCode] Resolved custom alias @page/actForm/utils in static: /path/to/project/static/page/actForm/utils/index.ts
```

## 调试步骤

### 1. 运行测试并收集日志

运行您的测试用例，查看控制台输出中所有以 `[DeepCode]` 开头的日志。

### 2. 关键检查点

**检查点 1: PSI 引用解析**
- 查看是否找到了引用元素
- 如果找到了，检查元素的文件路径是否正确

**检查点 2: 文件路径解析**
- 确认项目根目录是否正确
- 检查文件路径是否在项目根目录下
- 验证是否正确排除了 node_modules

**检查点 3: 别名解析**
- 确认是否进入了自定义别名处理分支
- 检查别名前缀解析是否正确 (`page`)
- 验证是否在 `static` 目录中找到了对应文件

### 3. 可能的问题点

**问题 1: PSI 引用解析失败**
- 如果 `PSI found 0 referenced elements`，说明 IntelliJ 无法解析这个导入
- 可能需要检查项目配置或文件索引

**问题 2: 项目根目录错误**
- 如果项目根目录不正确，会影响内外部判断
- 检查 `getProjectRoot()` 的返回值

**问题 3: 别名解析失败**
- 如果没有进入自定义别名分支，检查条件判断
- 如果进入了但没找到文件，检查目录结构

**问题 4: 文件扩展名问题**
- 检查 `findFileWithExtensions()` 是否正确处理了各种扩展名

## 预期的正确日志流程

对于 `@page/actForm/utils`，预期的日志应该是：

```
[DeepCode] === PSI Module Analysis for: @page/actForm/utils ===
[DeepCode] Step 1: Trying PSI reference resolution...
[DeepCode] PSI found 1 referenced elements for module: @page/actForm/utils
[DeepCode] Referenced element file path: /path/to/project/static/page/actForm/utils/index.ts
[DeepCode] === isFileInProject() called ===
[DeepCode] Checking file path: /path/to/project/static/page/actForm/utils/index.ts
[DeepCode] Project root path: /path/to/project
[DeepCode] File is under project root
[DeepCode] File is in project: internal
[DeepCode] CONCLUSION: TypeScript Module @page/actForm/utils is INTERNAL (PSI element analysis)
```

## 下一步

1. **运行测试**: 执行您的测试用例
2. **收集日志**: 复制所有 `[DeepCode]` 日志
3. **分析结果**: 对比实际日志和预期日志
4. **定位问题**: 找出在哪个步骤出现了偏差

请运行测试并提供完整的日志输出，这样我就能准确定位问题所在并进行针对性修复。
