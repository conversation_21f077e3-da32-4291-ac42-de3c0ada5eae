# 通配符导入和 Export 语句支持

## 问题描述

从用户截图可以看到，以下类型的导入和导出语句没有被正确识别：

1. **命名导入**: `import { ISchema } from '@sfe/xform-core';`
2. **通配符导出**: `export * from './ui';`

## 当前支持状态

### ✅ 已支持的导入类型

#### 1. 命名导入 (FROM_IMPORT)
```typescript
import { ISchema } from '@sfe/xform-core';
import { Checkbox, Tooltip } from 'antd';
import { QuestionCircleFilled } from '@ant-design/icons';
```

**检测逻辑**:
```java
// 检查是否有命名导入 (import { ... })
if (importText.contains("import {") && importText.contains("}")) {
    LOG.debug("Detected named imports via text analysis");
    return ImportInfo.ImportType.FROM_IMPORT;
}
```

#### 2. 通配符导入 (WILDCARD)
```typescript
import * as React from 'react';
import * from 'module';
```

**检测逻辑**:
```java
// 检查是否是命名空间导入 (import * as name)
if (importText.contains("import * as ") || importText.contains("import *")) {
    LOG.debug("Detected wildcard import via text analysis");
    return ImportInfo.ImportType.WILDCARD;
}
```

#### 3. 默认导入 (SINGLE)
```typescript
import React from 'react';
import './styles.css';
```

### 🚧 新增支持的 Export 语句

#### 1. 通配符导出 (WILDCARD)
```typescript
export * from './ui';
export * from '../components';
```

#### 2. 命名导出 (FROM_IMPORT)
```typescript
export { Button, Input } from 'antd';
export { default as Component } from './Component';
```

**实现位置**: `analyzeMiscellaneousImports()` → `analyzeExportStatements()`

## 技术实现

### 导入类型检测

在 `determineImportTypeFromTypeScriptImport()` 方法中：

```java
private static ImportInfo.ImportType determineImportTypeFromTypeScriptImport(TypeScriptImportStatement importStatement) {
    try {
        // 检查是否是类型导入
        if (importStatement.isTypeImport()) {
            return ImportInfo.ImportType.FROM_IMPORT;
        }

        String importText = importStatement.getText();

        // 1. 通配符导入检测
        if (importText.contains("import * as ") || importText.contains("import *")) {
            return ImportInfo.ImportType.WILDCARD;
        }

        // 2. 命名导入检测
        if (importText.contains("import {") && importText.contains("}")) {
            return ImportInfo.ImportType.FROM_IMPORT;
        }

        // 3. 默认导入
        return ImportInfo.ImportType.SINGLE;

    } catch (Exception e) {
        return ImportInfo.ImportType.SINGLE;
    }
}
```

### Export 语句检测

在 `analyzeExportStatements()` 方法中：

```java
private static void analyzeExportStatements(JSFile jsFile, AnalysisResult result, String filePath) {
    try {
        VirtualFile virtualFile = jsFile.getVirtualFile();
        String fileContent = new String(virtualFile.contentsToByteArray());
        String[] lines = fileContent.split("\n");

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            int lineNumber = i + 1;

            // 检测 export * from 语句
            if (line.startsWith("export * from ") || line.contains("export * from ")) {
                analyzeExportFromStatement(line, lineNumber, result, filePath, ImportInfo.ImportType.WILDCARD);
            }

            // 检测 export { ... } from 语句
            if ((line.startsWith("export {") || line.contains("export {")) && line.contains(" from ")) {
                analyzeExportFromStatement(line, lineNumber, result, filePath, ImportInfo.ImportType.FROM_IMPORT);
            }
        }
    } catch (Exception e) {
        LOG.debug("Failed to analyze export statements", e);
    }
}
```

### 模块路径提取

```java
private static String extractModuleSpecifierFromExportStatement(String exportStatement) {
    try {
        int fromIndex = exportStatement.indexOf(" from ");
        if (fromIndex == -1) return null;

        String afterFrom = exportStatement.substring(fromIndex + 6).trim();
        
        // 提取引号内的内容
        if (afterFrom.startsWith("'")) {
            int endQuote = afterFrom.indexOf("'", 1);
            if (endQuote != -1) {
                return afterFrom.substring(1, endQuote);
            }
        } else if (afterFrom.startsWith("\"")) {
            int endQuote = afterFrom.indexOf("\"", 1);
            if (endQuote != -1) {
                return afterFrom.substring(1, endQuote);
            }
        }

        return null;
    } catch (Exception e) {
        return null;
    }
}
```

## 预期结果

修复后，以下语句都应该被正确识别和分析：

### 1. 命名导入
```json
{
  "statement": "import { ISchema } from '@sfe/xform-core';",
  "type": "FROM_IMPORT",
  "isExternal": true,
  "targetFilePath": "node_modules/@sfe/xform-core/index.d.ts",
  "resolvedClasses": ["@sfe/xform-core"]
}
```

### 2. 通配符导出
```json
{
  "statement": "export * from './ui';",
  "type": "WILDCARD", 
  "isExternal": false,
  "targetFilePath": "static/components/ui/index.ts",
  "resolvedClasses": ["./ui"]
}
```

### 3. 命名导出
```json
{
  "statement": "export { Button } from 'antd';",
  "type": "FROM_IMPORT",
  "isExternal": true, 
  "targetFilePath": "node_modules/antd/index.d.ts",
  "resolvedClasses": ["antd"]
}
```

## 调试信息

新的日志输出会显示 export 语句的检测过程：

```
[DeepCode] --- Analyzing Export Statements ---
[DeepCode] Found export statement: export * from './ui'; -> ./ui
[DeepCode] Successfully analyzed export statement: ./ui (type: WILDCARD)
```

## 集成位置

Export 语句分析被集成到主要的分析流程中：

```
analyzeImports()
├── analyzeImportsUsingPSIInterfaces()
│   ├── analyzeTypeScriptImportsViaPSI()
│   ├── analyzeES6ImportsViaPSI()
│   └── analyzeRequireCallsViaPSI()
└── analyzeSupplementaryImports()
    ├── analyzeDynamicImports()
    ├── analyzeAMDImports()
    └── analyzeMiscellaneousImports()
        └── analyzeExportStatements() ← 新增
```

## 相对路径转换

所有检测到的 export 语句也会应用相对路径转换逻辑，确保 `targetFilePath` 返回项目相对路径而不是绝对路径。

这个修复确保了对 TypeScript/JavaScript 项目中常见的导入导出模式的完整支持。
