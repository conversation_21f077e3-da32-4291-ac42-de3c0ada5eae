# ES6 导入目标路径修复

## 问题描述

对于 ES6 导入语句如 `import { Checkbox, Tooltip } from 'antd';`，虽然导入了多个资源，但它们都来自同一个文件。之前的实现没有正确处理这种情况，导致 `targetFilePath` 为 `null`。

### 问题示例

```json
{
  "statement": "import { Checkbox, Tooltip } from 'antd';",
  "lineNumber": 4,
  "type": "FROM_IMPORT",
  "isExternal": true,
  "filePath": "static/components/activity/CheckboxGroupPro/index.tsx",
  "targetFilePath": null,  // ❌ 应该是 node_modules/antd/index.d.ts
  "resolvedClasses": [
    "antd.Checkbox",
    "antd.Tooltip"
  ]
}
```

## 根本原因

1. **ES6ImportDeclaration 处理不完整**: 之前的代码没有正确获取 ES6 导入的引用元素
2. **缺少 FromClause 解析**: 没有通过 FromClause 来解析模块的实际文件位置
3. **反射方法缺失**: 没有使用反射来调用可能存在的 `findReferencedElements` 方法

## 解决方案

### 核心思想

**为 ES6ImportDeclaration 添加多层解析策略，确保能够获取到实际的目标文件。**

### 技术实现

#### 1. 增强 ES6ImportDeclaration 处理

```java
} else if (importElement instanceof ES6ImportDeclaration) {
    ES6ImportDeclaration es6Import = (ES6ImportDeclaration) importElement;
    
    // 方法1: 尝试直接获取引用元素（通过反射）
    try {
        Method method = es6Import.getClass().getMethod("findReferencedElements");
        Object result = method.invoke(es6Import);
        if (result instanceof Collection) {
            referencedElements = (Collection<? extends PsiElement>) result;
        }
    } catch (Exception reflectionException) {
        // 反射失败，尝试其他方法
    }
    
    // 方法2: 如果反射失败，通过 FromClause 获取模块引用
    if (referencedElements == null || referencedElements.isEmpty()) {
        if (es6Import.getFromClause() != null) {
            PsiElement fromClause = es6Import.getFromClause();
            referencedElements = resolveES6FromClause(fromClause, moduleSpecifier);
        }
    }
}
```

#### 2. 新增 resolveES6FromClause() 方法

```java
private static Collection<? extends PsiElement> resolveES6FromClause(PsiElement fromClause, String moduleSpecifier) {
    // 方法1: 尝试通过 FromClause 的 resolve 方法
    try {
        Method resolveMethod = fromClause.getClass().getMethod("resolve");
        Object resolved = resolveMethod.invoke(fromClause);
        if (resolved instanceof PsiElement) {
            return Collections.singletonList((PsiElement) resolved);
        }
    } catch (Exception resolveException) {
        // resolve 方法失败
    }
    
    // 方法2: 尝试通过 FromClause 的 getReference 方法
    try {
        Method getReferenceMethod = fromClause.getClass().getMethod("getReference");
        Object reference = getReferenceMethod.invoke(fromClause);
        if (reference != null) {
            // 尝试解析引用
            Method refResolveMethod = reference.getClass().getMethod("resolve");
            Object refResolved = refResolveMethod.invoke(reference);
            if (refResolved instanceof PsiElement) {
                return Collections.singletonList((PsiElement) refResolved);
            }
        }
    } catch (Exception referenceException) {
        // getReference 方法失败
    }
    
    return Collections.emptyList();
}
```

#### 3. 详细的调试日志

```java
String es6Msg = "ES6 import detected, trying to get referenced elements...";
System.out.println("[DeepCode] " + es6Msg);

String foundMsg = "ES6 import found " + referencedElements.size() + " referenced elements via reflection";
System.out.println("[DeepCode] " + foundMsg);

String fromClauseMsg = "Trying to resolve via FromClause...";
System.out.println("[DeepCode] " + fromClauseMsg);

String resolvedMsg = "FromClause resolved to: " + resolvedElement.getClass().getSimpleName();
System.out.println("[DeepCode] " + resolvedMsg);
```

## 解析策略

### 多层回退机制

1. **第一层**: 通过反射调用 `findReferencedElements()` 方法
2. **第二层**: 通过 FromClause 的 `resolve()` 方法
3. **第三层**: 通过 FromClause 的 `getReference().resolve()` 方法
4. **第四层**: 回退到增强的文件解析（`ImportUtils.resolveResourceWithPSI`）

### 适用场景

- **标准 npm 包**: `import { Component } from 'react'`
- **子路径导入**: `import { Button } from 'antd/lib/button'`
- **命名导入**: `import { Checkbox, Tooltip } from 'antd'`
- **默认导入**: `import React from 'react'`
- **混合导入**: `import React, { Component } from 'react'`

## 预期效果

修复后，所有 ES6 导入都应该能正确解析 `targetFilePath`：

### 1. 标准 npm 包
```json
{
  "statement": "import { Checkbox, Tooltip } from 'antd';",
  "isExternal": true,
  "targetFilePath": "/path/to/project/node_modules/antd/index.d.ts"  // ✅ 正确
}
```

### 2. 子路径导入
```json
{
  "statement": "import Checkbox from 'antd/lib/checkbox';",
  "isExternal": true,
  "targetFilePath": "/path/to/project/node_modules/antd/lib/checkbox/index.d.ts"  // ✅ 正确
}
```

### 3. React 导入
```json
{
  "statement": "import React from 'react';",
  "isExternal": true,
  "targetFilePath": "/path/to/project/node_modules/react/index.d.ts"  // ✅ 正确
}
```

## 调试信息

新的日志会显示详细的 ES6 解析过程：

```
[DeepCode] === resolveTypeScriptTargetFilePath() for: antd (isExternal: true) ===
[DeepCode] ES6 import detected, trying to get referenced elements...
[DeepCode] ES6 import found 1 referenced elements via reflection
[DeepCode] Analyzing referenced element: TypeScriptFile
[DeepCode] Found target file: /path/to/project/node_modules/antd/index.d.ts
[DeepCode] Using absolute path for external file: /path/to/project/node_modules/antd/index.d.ts
```

或者如果反射失败：

```
[DeepCode] ES6 findReferencedElements method not available: NoSuchMethodException
[DeepCode] Trying to resolve via FromClause...
[DeepCode] FromClause type: ES6FromClause
[DeepCode] === resolveES6FromClause() for: antd ===
[DeepCode] FromClause resolved to: TypeScriptFile
[DeepCode] Found target file: /path/to/project/node_modules/antd/index.d.ts
```

## 技术优势

### 1. 兼容性
- **多版本支持**: 通过反射适应不同版本的 IntelliJ API
- **优雅降级**: 多层回退确保在各种情况下都能工作

### 2. 准确性
- **实际文件验证**: PSI 确保解析到的文件真实存在
- **与 IDE 一致**: 解析结果与 IntelliJ 的跳转行为一致

### 3. 可靠性
- **异常处理**: 完善的错误处理和日志记录
- **回退机制**: 多种解析方法确保高成功率

## 测试建议

1. **运行测试**: 执行包含各种 ES6 导入的测试用例
2. **检查日志**: 查看 ES6 导入的解析过程
3. **验证结果**: 确认 `targetFilePath` 是否正确填充
4. **跳转测试**: 验证解析的路径是否与 IDE 跳转一致

这个修复应该能解决所有 ES6 导入的 `targetFilePath` 问题，特别是像 `import { Checkbox, Tooltip } from 'antd';` 这样的多资源导入。
