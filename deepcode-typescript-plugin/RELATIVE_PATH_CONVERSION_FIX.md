# 相对路径转换修复

## 问题描述

虽然文件系统回退解决方案能够成功解析外部模块的文件路径，但返回的是绝对路径，而不是项目相对路径。

### 问题示例

**修复前**：
```json
{
  "statement": "import React from 'react';",
  "targetFilePath": "/Users/<USER>/Documents/projects/bm/banma_fe_page/node_modules/react/index.d.ts"  // ❌ 绝对路径
}
```

**期望结果**：
```json
{
  "statement": "import React from 'react';",
  "targetFilePath": "node_modules/react/index.d.ts"  // ✅ 项目相对路径
}
```

## 解决方案

### 核心修复

在文件系统解析成功后，立即将绝对路径转换为项目相对路径：

```java
String fileSystemPath = resolveExternalModuleByFileSystem(importElement, moduleSpecifier);
if (fileSystemPath != null) {
    // 转换为项目相对路径
    String relativePath = convertToProjectRelativePath(fileSystemPath, importElement);
    if (relativePath != null) {
        return relativePath;  // 返回相对路径
    } else {
        return fileSystemPath;  // 转换失败时回退到绝对路径
    }
}
```

### convertToProjectRelativePath() 方法

这个方法已经存在，它的作用是：

```java
private static String convertToProjectRelativePath(String absolutePath, PsiElement context) {
    // 获取项目根目录
    Project project = context.getProject();
    String projectBasePath = project.getBasePath();
    
    // 如果文件在项目目录下，转换为相对路径
    if (absolutePath.startsWith(projectBasePath)) {
        String relativePath = absolutePath.substring(projectBasePath.length());
        if (relativePath.startsWith("/")) {
            relativePath = relativePath.substring(1);  // 移除开头的 /
        }
        return relativePath;
    }
    
    return null;  // 不在项目目录下，无法转换
}
```

## 转换逻辑

### 1. 路径前缀移除

**输入**：
```
绝对路径: /Users/<USER>/Documents/projects/bm/banma_fe_page/node_modules/react/index.d.ts
项目根目录: /Users/<USER>/Documents/projects/bm/banma_fe_page
```

**处理过程**：
1. 检查绝对路径是否以项目根目录开头
2. 移除项目根目录前缀：`/node_modules/react/index.d.ts`
3. 移除开头的 `/`：`node_modules/react/index.d.ts`

**输出**：
```
相对路径: node_modules/react/index.d.ts
```

### 2. 支持的路径类型

#### 外部模块（node_modules）
- **绝对路径**: `/path/to/project/node_modules/react/index.d.ts`
- **相对路径**: `node_modules/react/index.d.ts`

#### 内部模块（项目文件）
- **绝对路径**: `/path/to/project/static/page/actForm/utils/index.ts`
- **相对路径**: `static/page/actForm/utils/index.ts`

#### 相对导入（CSS 等）
- **绝对路径**: `/path/to/project/static/components/activity/CheckboxGroupPro/styles.css`
- **相对路径**: `static/components/activity/CheckboxGroupPro/styles.css`

## 预期效果

修复后，所有类型的导入都应该返回项目相对路径：

### 1. 外部模块
```json
{
  "statement": "import React, { forwardRef } from 'react';",
  "targetFilePath": "node_modules/react/index.d.ts"  // ✅ 相对路径
},
{
  "statement": "import { Checkbox, Tooltip } from 'antd';",
  "targetFilePath": "node_modules/antd/index.d.ts"  // ✅ 相对路径
},
{
  "statement": "import { QuestionCircleFilled } from '@ant-design/icons';",
  "targetFilePath": "node_modules/@ant-design/icons/index.d.ts"  // ✅ 相对路径
}
```

### 2. 内部模块
```json
{
  "statement": "import { replaceToPlainStr } from '@page/actForm/utils';",
  "targetFilePath": "static/page/actForm/utils/index.ts"  // ✅ 相对路径
}
```

### 3. 相对导入
```json
{
  "statement": "import './styles.css';",
  "targetFilePath": "static/components/activity/CheckboxGroupPro/styles.css"  // ✅ 相对路径
}
```

## 调试信息

新的日志会显示路径转换过程：

```
[DeepCode] File system resolution found: /Users/<USER>/Documents/projects/bm/banma_fe_page/node_modules/react/index.d.ts
[DeepCode] Converted to project relative path: node_modules/react/index.d.ts
```

如果转换失败：
```
[DeepCode] File system resolution found: /some/external/path/file.ts
[DeepCode] Could not convert to relative path, using absolute path
```

## 技术优势

### 1. 一致性
- **统一格式**: 所有 `targetFilePath` 都使用项目相对路径
- **可预测**: 路径格式一致，便于后续处理

### 2. 可移植性
- **环境无关**: 相对路径不依赖具体的文件系统位置
- **团队协作**: 不同开发者的绝对路径不同，但相对路径一致

### 3. 简洁性
- **路径简短**: 移除了冗长的绝对路径前缀
- **易读性**: 更容易理解文件在项目中的位置

### 4. 兼容性
- **回退机制**: 如果转换失败，仍然返回绝对路径
- **错误处理**: 完善的异常处理确保不会崩溃

## 应用场景

### 1. 代码分析工具
- 分析导入依赖关系
- 生成依赖图
- 检测循环依赖

### 2. 构建工具
- 模块打包
- 依赖优化
- 代码分割

### 3. IDE 功能
- 文件跳转
- 重构支持
- 智能提示

### 4. 文档生成
- API 文档
- 依赖文档
- 架构图

这个相对路径转换修复确保了所有 `targetFilePath` 都使用一致的项目相对路径格式，提高了结果的可用性和一致性。
