#!/bin/bash

echo "=== TypeScript Import Analysis Test ==="

# 编译检查
echo "1. 编译检查..."
./gradlew :deepcode-typescript-plugin:compileJava
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi
echo "✅ 编译成功"

# 编译测试文件
echo "2. 编译测试类..."
./gradlew :deepcode-typescript-plugin:compileTestJava
if [ $? -ne 0 ]; then
    echo "❌ 测试编译失败"
    exit 1
fi
echo "✅ 测试编译成功"

echo ""
echo "=== 导入分析测试完成 ==="
echo "请在 IDE 中打开以下文件来测试导入关系分析："
echo "1. deepcode-typescript-plugin/src/test/typescript/ImportTestExample.ts"
echo "2. deepcode-typescript-plugin/src/test/typescript/ComprehensiveImportExample.ts"
echo "3. deepcode-typescript-plugin/src/test/typescript/ProjectAliasExample.ts"
echo "4. deepcode-typescript-plugin/src/test/typescript/PageModuleExample.ts"
echo ""
echo "观察日志输出（Tools -> DeepCode -> Analysis Panel）"
echo "检查是否显示："
echo "- TypeScript import statements found"
echo "- Import analysis completed"
echo "- Total imports found"
echo ""
echo "如果导入关系没有显示，请检查:"
echo "1. 日志输出中的导入分析信息"
echo "2. AnalysisResult中的imports数量"
echo "3. UI面板的导入关系显示"

