# 基于 PSI 的目标路径解析修复

## 问题分析

根据日志分析，别名解析已经修复，但还有两个关键问题：

1. **node_modules 库的 targetFilePath 没有解析出来**
   - 例如：`antd/lib/checkbox` 应该解析到 `node_modules/antd/lib/checkbox/index.d.ts`

2. **相对路径的 targetFilePath 没有正确解析**
   - 例如：`./styles.css` 应该解析到 `static/components/activity/CheckboxGroupPro/styles.css`

## 根本原因

之前的 `resolveTypeScriptTargetFilePath` 方法没有充分利用 PSI 的能力，而是依赖复杂的字符串匹配逻辑。

**核心问题**：所有能在 IDE 中跳转的导入都应该使用统一的 PSI 解析逻辑。

## 解决方案

### 核心思想

**完全基于 PSI 的 `findReferencedElements()` 方法来获取实际的目标文件路径。**

### 技术实现

#### 1. 重写 resolveTypeScriptTargetFilePath()

```java
private static String resolveTypeScriptTargetFilePath(PsiElement importElement, String moduleSpecifier, boolean isExternal) {
    // 方法1: 使用 PSI 的 findReferencedElements() 获取实际目标文件
    Collection<? extends PsiElement> referencedElements = null;
    
    if (importElement instanceof TypeScriptImportStatement) {
        TypeScriptImportStatement tsImport = (TypeScriptImportStatement) importElement;
        referencedElements = tsImport.findReferencedElements();
    } else if (importElement instanceof ES6ImportDeclaration) {
        // ES6ImportDeclaration 使用替代方法
        // 因为可能没有 findReferencedElements 方法
    }

    // 分析引用的元素，找到文件
    if (referencedElements != null && !referencedElements.isEmpty()) {
        for (PsiElement element : referencedElements) {
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null && containingFile.getVirtualFile() != null) {
                VirtualFile virtualFile = containingFile.getVirtualFile();
                String absolutePath = virtualFile.getPath();
                
                // 尝试获取项目相对路径
                String relativePath = getProjectRelativeFilePath(containingFile);
                if (relativePath != null) {
                    return relativePath;  // 内部文件
                } else {
                    return absolutePath;  // 外部文件
                }
            }
        }
    }

    // 方法2: 如果 findReferencedElements() 失败，使用增强的文件解析
    ImportUtils.ResourceResolutionResult resolutionResult = 
        ImportUtils.resolveResourceWithPSI(importElement, moduleSpecifier);
    
    if (resolutionResult.getAbsolutePath() != null) {
        if (resolutionResult.isInternal()) {
            return convertToProjectRelativePath(resolutionResult.getAbsolutePath(), importElement);
        } else {
            return resolutionResult.getAbsolutePath();
        }
    }

    return null;
}
```

#### 2. 新增 convertToProjectRelativePath()

```java
private static String convertToProjectRelativePath(String absolutePath, PsiElement context) {
    // 获取项目根目录
    Project project = context.getProject();
    String projectBasePath = project.getBasePath();
    
    // 如果文件在项目目录下，转换为相对路径
    if (absolutePath.startsWith(projectBasePath)) {
        String relativePath = absolutePath.substring(projectBasePath.length());
        if (relativePath.startsWith("/")) {
            relativePath = relativePath.substring(1);
        }
        return relativePath;
    }
    
    return null;
}
```

#### 3. 添加详细的调试日志

```java
String methodMsg = "=== resolveTypeScriptTargetFilePath() for: " + moduleSpecifier + " (isExternal: " + isExternal + ") ===";
System.out.println("[DeepCode] " + methodMsg);

String tsMsg = "TypeScript import found " + referencedElements.size() + " referenced elements";
System.out.println("[DeepCode] " + tsMsg);

String foundFileMsg = "Found target file: " + absolutePath;
System.out.println("[DeepCode] " + foundFileMsg);

String relativeMsg = "Converted to relative path: " + relativePath;
System.out.println("[DeepCode] " + relativeMsg);
```

## 预期效果

修复后，所有类型的导入都应该能正确解析 `targetFilePath`：

### 1. 内部别名
```json
{
  "statement": "import { replaceToPlainStr } from '@page/actForm/utils';",
  "isExternal": false,
  "targetFilePath": "static/page/actForm/utils/index.ts"  // ✅ 正确
}
```

### 2. 相对路径
```json
{
  "statement": "import './styles.css';",
  "isExternal": false,
  "targetFilePath": "static/components/activity/CheckboxGroupPro/styles.css"  // ✅ 正确
}
```

### 3. 外部 npm 包
```json
{
  "statement": "import Checkbox from 'antd/lib/checkbox';",
  "isExternal": true,
  "targetFilePath": "/path/to/project/node_modules/antd/lib/checkbox/index.d.ts"  // ✅ 正确
}
```

### 4. 标准 npm 包
```json
{
  "statement": "import React from 'react';",
  "isExternal": true,
  "targetFilePath": "/path/to/project/node_modules/react/index.d.ts"  // ✅ 正确
}
```

## 优势

### 1. 统一性
- **一致的解析逻辑**：所有导入类型使用相同的 PSI 解析方法
- **与 IDE 一致**：解析结果与 IntelliJ 的跳转行为完全一致

### 2. 准确性
- **实际文件验证**：PSI 确保文件真实存在且可访问
- **正确的路径格式**：自动处理相对路径和绝对路径的转换

### 3. 可靠性
- **多层回退**：PSI 解析 → 增强文件解析 → 失败处理
- **详细日志**：完整的调试信息便于问题排查

### 4. 扩展性
- **自动适应**：支持任何 IntelliJ 能识别的导入模式
- **配置无关**：无需手动维护路径映射

## 调试信息

新的日志会显示详细的解析过程：

```
[DeepCode] === resolveTypeScriptTargetFilePath() for: antd/lib/checkbox (isExternal: true) ===
[DeepCode] TypeScript import found 1 referenced elements
[DeepCode] Analyzing referenced element: SomeElementType
[DeepCode] Found target file: /path/to/project/node_modules/antd/lib/checkbox/index.d.ts
[DeepCode] Using absolute path for external file: /path/to/project/node_modules/antd/lib/checkbox/index.d.ts
```

```
[DeepCode] === resolveTypeScriptTargetFilePath() for: ./styles.css (isExternal: false) ===
[DeepCode] TypeScript import found 1 referenced elements
[DeepCode] Found target file: /path/to/project/static/components/activity/CheckboxGroupPro/styles.css
[DeepCode] Converted to relative path: static/components/activity/CheckboxGroupPro/styles.css
```

## 测试建议

1. **运行测试**：执行您的测试用例
2. **检查日志**：查看 `[DeepCode]` 开头的目标路径解析日志
3. **验证结果**：确认 `targetFilePath` 字段是否正确填充
4. **跳转测试**：验证解析的路径是否与 IDE 跳转一致

这种完全基于 PSI 的方法应该能解决所有 `targetFilePath` 解析问题，无论是内部文件、外部包还是相对路径。
