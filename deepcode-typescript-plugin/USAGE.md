# TypeScript 插件使用指南

本文档详细介绍 DeepCode TypeScript Plugin 的使用方法、功能特性和最佳实践。

## 🎯 快速开始

### 前置检查

在使用插件前，请确认：

1. **JavaScript 插件已启用**：
   - WebStorm 自带 TypeScript 支持
   - IntelliJ IDEA 需要安装 JavaScript 和 TypeScript 插件

2. **Node.js 环境已配置**：
   - 安装 Node.js 16+ 版本
   - 可选：配置 TypeScript 编译器 `npm install -g typescript`

3. **项目结构正确**：
   - 包含 `.ts`、`.tsx`、`.js`、`.jsx` 文件
   - 推荐：`tsconfig.json` 或 `jsconfig.json` 配置文件
   - 可选：`package.json`、`node_modules/`

### 第一次使用

1. **打开 TypeScript 文件**：选择一个中等大小的 `.ts` 文件（100-300行）
2. **运行分析**：右键选择 "Quick AST Analyzer" 或按 `Ctrl+Alt+Shift+T`
3. **查看结果**：弹窗显示分析结果摘要

## 🎨 用户界面

### 快速分析对话框

分析完成后会显示如下信息：

```
┌─────── TypeScript AST Analysis Results ─────────┐
│                                                 │
│ File: user-service.ts                           │
│ ✓ Analysis completed in 156ms                  │
│                                                 │
│ 📊 Summary:                                     │
│ • Interfaces: 2                                 │
│ • Classes: 1                                    │
│ • Functions: 4                                  │
│ • Type Aliases: 2                               │
│ • Call Relations: 12                            │
│                                                 │
│ 🔗 Call Relations:                              │
│ • Internal calls: 3                             │
│ • External calls: 9                             │
│                                                 │
│ [View Details] [Export JSON] [Close]            │
└─────────────────────────────────────────────────┘
```

### 工具窗口

通过 `View` → `Tool Windows` → `AST Analysis` 打开详细工具窗口：

#### 节点树面板
```
TypeScript 节点
├── 📦 user-service
│   ├── 🔧 IUser (interface)
│   │   ├── 📦 id: number
│   │   ├── 📦 name: string
│   │   ├── 📦 email: string
│   │   └── 📦 createdAt: Date
│   ├── 🔧 IUserRepository (interface)
│   │   ├── ⚙️ findById(id: number): Promise<IUser | null>
│   │   ├── ⚙️ findAll(): Promise<IUser[]>
│   │   └── ⚙️ create(user: Omit<IUser, 'id'>): Promise<IUser>
│   ├── 🏛️ UserService (class)
│   │   ├── ⚙️ constructor(userRepository: IUserRepository)
│   │   ├── ⚙️ getUserById(userId: number): Promise<IUser | null>
│   │   ├── ⚙️ getAllUsers(): Promise<IUser[]>
│   │   └── ⚙️ createUser(userData: Omit<IUser, 'id' | 'createdAt'>): Promise<IUser>
│   ├── 🔰 UserCreateRequest (type alias)
│   └── 🔰 UserUpdateRequest (type alias)
```

#### 调用关系面板
| 调用方 | 被调用方 | 类型 | 位置 |
|--------|----------|------|------|
| UserService.getUserById | this.userRepository.findById | 外部 | 行 29 |
| UserService.getUserById | Error.constructor | 外部 | 行 26 |
| UserService.getAllUsers | this.userRepository.findAll | 外部 | 行 33 |
| UserService.createUser | this.userRepository.create | 外部 | 行 41 |
| UserService.createUser | Date.constructor | 外部 | 行 39 |

#### 统计面板
- **节点统计**：接口 2 个、类 1 个、函数 4 个、类型别名 2 个
- **调用统计**：总计 12 个调用关系，内部调用 3 个，外部调用 9 个
- **类型覆盖**：100% 的函数有类型注解，90% 的变量有类型声明

## 🔧 详细功能

### 节点分析

#### 接口（INTERFACE）
- **识别内容**：接口定义、属性声明、方法签名、泛型参数
- **提取信息**：属性类型、方法参数、返回类型、继承关系
- **示例**：
```typescript
export interface IUserRepository<T extends BaseEntity> {
  findById(id: string): Promise<T | null>;
  findAll(filter?: Partial<T>): Promise<T[]>;
}

// 识别为：
// 类型: INTERFACE
// 名称: IUserRepository
// 泛型: T extends BaseEntity
// 方法: findById, findAll
// 导出: export
```

#### 类（CLASS）
- **识别内容**：类定义、构造函数、方法、属性、装饰器
- **提取信息**：访问修饰符、静态成员、抽象标记、继承关系
- **示例**：
```typescript
@Injectable()
export abstract class BaseService {
  protected readonly logger = new Logger();

  abstract process(): Promise<void>;

  protected log(message: string): void {
    this.logger.info(message);
  }
}

// 识别为：
// 类型: CLASS
// 名称: BaseService
// 装饰器: @Injectable
// 抽象: true
// 导出: export
```

#### 函数/方法（FUNCTION/METHOD）
- **识别内容**：函数定义、箭头函数、方法定义、异步函数
- **提取信息**：参数类型、返回类型、泛型约束、装饰器
- **示例**：
```typescript
export async function processUsers<T extends IUser>(
  users: T[],
  processor: (user: T) => Promise<void>
): Promise<void> {
  for (const user of users) {
    await processor(user);
  }
}

// 识别为：
// 类型: FUNCTION
// 名称: processUsers
// 泛型: T extends IUser
// 异步: true
// 导出: export
```

#### 类型别名（TYPE_ALIAS）
- **识别内容**：type 声明、联合类型、交叉类型、条件类型
- **提取信息**：类型定义、泛型参数、约束条件
- **示例**：
```typescript
export type ApiResponse<T> =
  | { success: true; data: T }
  | { success: false; error: string };

export type UserKeys = keyof IUser;

// 识别为两个TYPE_ALIAS节点：
// 1. ApiResponse<T> - 联合类型
// 2. UserKeys - keyof 类型查询
```

#### 枚举（ENUM）
- **识别内容**：enum 声明、成员定义、数值枚举、字符串枚举
- **提取信息**：枚举成员、初始值、兼容性
- **示例**：
```typescript
export enum UserStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

// 识别为：
// 类型: ENUM
// 名称: UserStatus
// 成员: PENDING, ACTIVE, INACTIVE
// 字符串枚举: true
```

### 调用关系分析

#### 内部调用判定
满足以下条件的调用被认为是内部调用：
- 被调用函数在同一模块中定义
- 被调用类在同一项目中定义
- 项目内部的模块调用（通过相对路径导入）

**判定示例**：
```typescript
// user-service.ts
import { ValidationUtils } from './utils/validation';  // 项目内部

export class UserService {
  validateUser(user: IUser): boolean {
    return ValidationUtils.isValidEmail(user.email);  // → 内部调用
  }

  private formatUserName(name: string): string {      // → 内部调用
    return this.capitalize(name);
  }

  private capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }
}
```

#### 外部调用判定
包括以下类型的调用：

1. **JavaScript/Node.js 内置对象**：
```typescript
console.log("message");           // console API
JSON.parse(data);                 // JSON 内置对象
Array.from(iterable);             // Array 静态方法
Promise.resolve(value);           // Promise 构造
```

2. **浏览器 API**：
```typescript
document.querySelector('.class'); // DOM API
window.localStorage.getItem('key'); // Storage API
fetch('/api/users');              // Fetch API
navigator.geolocation.getCurrentPosition(); // Navigator API
```

3. **Node.js API**：
```typescript
import * as fs from 'fs';
import * as path from 'path';

fs.readFileSync(path.join(__dirname, 'file.txt'));  // fs + path API
process.env.NODE_ENV;             // process 全局对象
```

4. **第三方库**：
```typescript
import axios from 'axios';        // HTTP 客户端
import * as lodash from 'lodash'; // 工具库
import { Component } from '@angular/core';  // 框架

axios.get('/api/data');           // 外部调用
lodash.debounce(fn, 300);        // 外部调用
```

### TypeScript 特有功能

#### 类型注解解析

解析各种 TypeScript 类型注解：

```typescript
// 基础类型
let count: number = 0;
let message: string = "hello";
let isActive: boolean = true;

// 联合类型
type Status = 'loading' | 'success' | 'error';
let currentStatus: Status = 'loading';

// 泛型类型
interface Repository<T> {
  items: T[];
  add(item: T): void;
  find<K extends keyof T>(key: K): T[K] | undefined;
}

// 函数类型
type EventHandler<T> = (event: T) => void;
type AsyncProcessor<T, R> = (input: T) => Promise<R>;

// 条件类型
type NonNullable<T> = T extends null | undefined ? never : T;
type ReturnTypeOf<T> = T extends (...args: any[]) => infer R ? R : never;
```

#### 装饰器分析

识别各种装饰器模式：

```typescript
// 类装饰器
@Component({
  selector: 'app-user',
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.css']
})
export class UserComponent {
  // 属性装饰器
  @Input() user: IUser;
  @Output() userSelected = new EventEmitter<IUser>();

  // 方法装饰器
  @HostListener('click', ['$event'])
  @Log('UserComponent.onClick')
  onClick(event: MouseEvent): void {
    this.userSelected.emit(this.user);
  }

  // 参数装饰器
  processUser(@Inject('USER_CONFIG') config: UserConfig): void {
    // 处理逻辑
  }
}

// 自定义装饰器
function Log(message: string) {
  return function(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    console.log(`${message}: ${propertyName} called`);
  };
}
```

#### 模块系统分析

处理各种模块导入导出：

```typescript
// ES6 导入导出
export { UserService } from './user-service';
export * from './types';
import { Injectable } from '@nestjs/common';
import type { Repository } from 'typeorm';

// 默认导入导出
export default class DefaultUserService {}
import DefaultUserService from './default-user-service';

// 命名空间导入
import * as UserUtils from './user-utils';
import type * as UserTypes from './user-types';

// 动态导入
const module = await import('./dynamic-module');

// 命名空间声明
declare namespace Global {
  interface User {
    id: string;
    name: string;
  }
}

// 模块声明
declare module 'external-library' {
  interface Config {
    apiKey: string;
  }
}
```

## 📊 导出和报告

### JSON 导出格式

```json
{
  "filePath": "user-service.ts",
  "language": "TYPESCRIPT",
  "analysisTime": "2024-06-18T10:30:45.123Z",
  "nodes": {
    "user-service.IUser": {
      "id": "user-service.IUser",
      "type": "INTERFACE",
      "name": "IUser",
      "packageName": "user-service",
      "lineNumber": 4,
      "signature": "export interface IUser",
      "language": "TYPESCRIPT",
      "exported": true,
      "properties": [
        { "name": "id", "type": "number" },
        { "name": "name", "type": "string" },
        { "name": "email", "type": "string" },
        { "name": "createdAt", "type": "Date" }
      ]
    },
    "user-service.UserService": {
      "id": "user-service.UserService",
      "type": "CLASS",
      "name": "UserService",
      "packageName": "user-service",
      "lineNumber": 18,
      "signature": "@Injectable() export class UserService",
      "language": "TYPESCRIPT",
      "decorators": ["Injectable"],
      "exported": true,
      "methods": [
        {
          "name": "constructor",
          "parameters": ["userRepository: IUserRepository"],
          "accessModifier": "public"
        },
        {
          "name": "getUserById",
          "parameters": ["userId: number"],
          "returnType": "Promise<IUser | null>",
          "async": true
        }
      ]
    }
  },
  "callRelations": [
    {
      "callerId": "user-service.UserService.getUserById",
      "calleeId": "this.userRepository.findById",
      "isInternal": false,
      "instances": [
        {
          "lineNumber": 29,
          "context": "await this.userRepository.findById(userId)"
        }
      ]
    }
  ],
  "imports": [
    {
      "module": "@nestjs/common",
      "items": ["Injectable"],
      "importType": "named",
      "line": 2
    },
    {
      "module": "typeorm",
      "items": ["Repository"],
      "importType": "named",
      "line": 3
    }
  ],
  "exports": [
    {
      "name": "IUser",
      "type": "interface",
      "line": 4
    },
    {
      "name": "UserService",
      "type": "class",
      "line": 18
    }
  ]
}
```

### CSV 导出格式

**节点信息表 (nodes.csv)**：
```csv
ID,Type,Name,Package,File,Line,Signature,Exported,Decorators,ReturnType
user-service.IUser,INTERFACE,IUser,user-service,user-service.ts,4,"export interface IUser",true,,
user-service.UserService,CLASS,UserService,user-service,user-service.ts,18,"@Injectable() export class UserService",true,Injectable,
user-service.UserService.getUserById,METHOD,getUserById,user-service,user-service.ts,24,"async getUserById(userId: number): Promise<IUser | null>",false,,Promise<IUser | null>
```

**调用关系表 (calls.csv)**：
```csv
Caller,Callee,Internal,Line,Context,Async
user-service.UserService.getUserById,this.userRepository.findById,false,29,"await this.userRepository.findById(userId)",true
user-service.UserService.getUserById,Error.constructor,false,26,"throw new Error('Invalid user ID')",false
```

**类型信息表 (types.csv)**：
```csv
NodeID,TypeCategory,TypeDefinition,GenericParams,Constraints
user-service.IUser,interface,"{ id: number; name: string; email: string; createdAt: Date }",,
user-service.UserCreateRequest,type_alias,"Pick<IUser, 'name' | 'email'>",,
user-service.Repository,interface,"Repository<T extends BaseEntity>",T,"T extends BaseEntity"
```

## 🎯 使用场景

### 代码理解

**场景**：理解一个新的 TypeScript 项目结构

**操作步骤**：
1. 从项目的入口文件开始（如 `main.ts`、`index.ts`、`app.ts`）
2. 运行 AST 分析
3. 查看导入关系，了解模块依赖
4. 分析类和接口的类型关系

**收益**：
- 快速掌握项目架构
- 理解类型系统设计
- 识别核心业务实体

### API 文档生成

**场景**：为 TypeScript 项目生成 API 文档

**操作步骤**：
1. 分析所有公共模块文件
2. 导出 JSON 格式结果
3. 提取接口、类型别名和类型注解
4. 结合 TSDoc 注释生成文档

**收益**：
- 自动化文档生成
- 类型信息完整且准确
- API 接口规范清晰

### 类型检查准备

**场景**：提升项目的类型安全性

**操作步骤**：
1. 分析现有代码的类型注解覆盖率
2. 识别使用 any 类型的位置
3. 查看外部库调用，确认类型声明
4. 逐步加强类型约束

**收益**：
- 提高代码质量
- 减少运行时错误
- 改善开发体验

### 重构影响分析

**场景**：重构接口或类型定义，需要评估影响范围

**操作步骤**：
1. 分析包含目标接口/类型的文件
2. 查看调用关系和类型引用
3. 导出依赖关系数据进行分析
4. 制定渐进式重构计划

**收益**：
- 降低重构风险
- 确保类型兼容性
- 提高重构效率

### 微前端架构分析

**场景**：分析微前端项目的模块边界

**操作步骤**：
1. 分析各个微应用的导出接口
2. 查看跨应用的类型共享
3. 识别公共类型和工具函数
4. 优化模块划分策略

**收益**：
- 明确模块职责边界
- 优化代码复用策略
- 减少重复类型定义

## ⚙️ 高级配置

### 调试模式

启用详细的调试输出：

```bash
# 方式一：环境变量
export AST_ANALYZER_DEBUG=true
./gradlew :deepcode-typescript-plugin:runIde

# 方式二：IDE 启动参数
-Dast.analyzer.debug=true
```

调试输出示例：
```
[TYPESCRIPT-DEBUG] Starting analysis for: user-service.ts
[TYPESCRIPT-DEBUG] Found interface: IUser at line 4 with 4 properties
[TYPESCRIPT-DEBUG] Found class: UserService at line 18 with decorator @Injectable
[TYPESCRIPT-DEBUG] Analyzing method: getUserById at line 24 with return type Promise<IUser | null>
[TYPESCRIPT-DEBUG] Analyzing call: this.userRepository.findById(userId) at line 29
[TYPESCRIPT-DEBUG] Resolved call target: userRepository.findById - external
[TYPESCRIPT-DEBUG] Analysis completed: 8 nodes, 5 call relations
```

### TypeScript 编译器配置

确保正确配置 TypeScript 编译器选项：

**tsconfig.json**:
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "node",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "declaration": true,
    "declarationMap": true
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts"
  ]
}
```

### WebStorm/IDEA TypeScript 设置

优化 IDE 的 TypeScript 支持：

```
File → Settings → Languages & Frameworks → TypeScript
- TypeScript version: Use TypeScript from project (推荐)
- Compile scope: All places
- Check errors: ✓
- Show suggestions for: ✓ Language service
- Use TypeScript Language Service for: ✓ Syntax highlighting, error highlighting
```

### npm/yarn 依赖管理

确保类型声明包完整：

```bash
# 安装项目依赖
npm install

# 安装类型声明包
npm install --save-dev @types/node @types/lodash

# 检查缺失的类型声明
npx typesync

# 验证类型解析
npx tsc --noEmit
```

## ❗ 注意事项

### 项目要求

1. **TypeScript/JavaScript 环境**：
   - Node.js 16+ 环境（推荐）
   - 正确配置 tsconfig.json 或 jsconfig.json
   - 安装必要的类型声明包 (@types/*)

2. **IDE 插件支持**：
   - WebStorm 内置 TypeScript 支持
   - IntelliJ IDEA 需要启用 JavaScript 和 TypeScript 插件
   - 确保插件版本与 IDE 版本兼容

3. **文件编码和语法**：
   - 确保文件使用 UTF-8 编码
   - TypeScript/JavaScript 语法正确
   - 避免语法错误影响 PSI 解析

### 性能考虑

1. **大项目处理**：
   - TypeScript 项目通常包含大量类型定义
   - 复杂的泛型系统可能影响分析速度
   - 建议逐个文件或模块分析

2. **内存使用**：
   - TypeScript PSI 树包含丰富的类型信息
   - 类型推断过程可能消耗额外内存
   - 建议为 IDE 分配足够内存（8GB+）

3. **依赖库影响**：
   - 大量第三方库会增加类型解析时间
   - node_modules 目录建议排除在分析范围外

### 限制说明

1. **动态特性**：
   - 运行时动态生成的类型无法分析
   - eval() 和动态 import() 的部分情况无法完全解析
   - 高阶类型操作可能识别不完整

2. **类型推断精度**：
   - 复杂的条件类型可能推断不准确
   - any 类型会降低分析精度
   - 缺少类型声明的第三方库影响分析结果

3. **JavaScript 兼容性**：
   - 普通 JavaScript 文件缺少类型信息
   - JSDoc 类型注解支持有限
   - 动态特性较多的 JS 代码分析精度较低

## 🔧 故障排除

### 常见问题

**问题一：分析结果为空或不完整**

可能原因：
- TypeScript 配置文件缺失或配置错误
- JavaScript 插件未正确启用
- 文件语法错误导致 PSI 解析失败

解决方法：
1. 检查 tsconfig.json 配置：
```bash
npx tsc --showConfig
```

2. 验证 TypeScript 编译：
```bash
npx tsc --noEmit
```

3. 确认插件状态：
`File` → `Settings` → `Plugins` → 搜索 "JavaScript"

4. 启用调试模式查看详细错误

**问题二：类型信息无法识别**

可能原因：
- 缺少类型声明文件
- 模块路径解析错误
- 泛型约束过于复杂

解决方法：
1. 安装缺失的类型声明：
```bash
npm install --save-dev @types/[library-name]
```

2. 检查模块解析：
```typescript
// 在代码中测试导入
import { SomeType } from './some-module'; // 是否正常解析
```

3. 简化复杂类型定义进行测试

**问题三：调用关系分析不准确**

可能原因：
- 动态方法调用
- 复杂的继承链
- 第三方库方法无法解析

解决方法：
1. 添加明确的类型注解：
```typescript
// 明确接口类型
const service: IUserService = getService();
service.method(); // 更容易被识别
```

2. 检查依赖库安装。
```bash
npm list | grep [library-name]
```

3. 查看调试日志中的解析过程

**问题四：装饰器分析错误**

可能原因：
- 实验性装饰器功能未启用
- 装饰器库未正确安装
- 复杂的装饰器工厂

解决方法：
1. 启用实验性装饰器功能：
```json
// tsconfig.json
{
  "compilerOptions": {
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  }
}
```

2. 确保装饰器库正确导入：
```typescript
import { Injectable } from '@nestjs/common';
```

3. 简化装饰器使用进行测试

### 性能优化建议

1. **IDE 内存配置**：
```
# webstorm64.exe.vmoptions
-Xmx4096m
-XX:ReservedCodeCacheSize=512m
```

2. **项目优化**：
   - 排除不必要的目录：`node_modules/`, `dist/`, `.git/`
   - 使用项目级别的 TypeScript 编译器
   - 定期清理缓存：`File` → `Invalidate Caches and Restart`

3. **分析策略**：
   - 优先分析核心业务文件
   - 分批处理大型项目文件
   - 避免同时分析多个大文件

### 日志分析

查看插件运行日志：

**日志位置**：
- Windows：`%APPDATA%\JetBrains\WebStorm2024.1\log\idea.log`
- macOS：`~/Library/Logs/JetBrains/WebStorm2024.1/`
- Linux：`~/.cache/JetBrains/WebStorm2024.1/log/`

**关键词搜索**：
- `TypeScriptASTAnalyzer`
- `TYPESCRIPT-DEBUG`
- `deepcode.astplugin.typescript`
- `JavaScript.file.type`

**常见错误模式**：
```
ERROR - Cannot resolve symbol 'SomeType'
ERROR - Module not found: './some-module'
ERROR - Unexpected token in TypeScript file
```

通过遵循这个使用指南，您可以充分发挥 TypeScript 插件的功能，深入理解 TypeScript/JavaScript 代码结构，提高开发效率和代码质量。

