# TypeScript 导入解析修复

## 修复的问题

根据用户反馈的解析结果问题，我们修复了以下几个关键问题：

### 1. 别名识别错误

**问题**: `@page/actForm/utils` 被错误识别为外部模块
```json
{
  "statement": "import { replaceToPlainStr } from '@page/actForm/utils';",
  "isExternal": true,  // ❌ 错误：应该是 false
}
```

**修复**: 增强了别名识别逻辑
- 支持 `@/` 标准别名
- 支持自定义项目别名（如 `@page/`, `@components/`, `@utils/` 等）
- 区分 npm 包和项目别名

### 2. targetFilePath 解析失败

**问题**: `targetFilePath` 为 null，无法获取实际文件路径
```json
{
  "targetFilePath": null,  // ❌ 错误：应该是实际文件路径
}
```

**修复**: 改进了目标文件路径解析
- 使用 PSI 接口进行精确解析
- 支持内部和外部文件的路径解析
- 增强的文件查找逻辑

### 3. 相对路径解析错误

**问题**: 相对路径解析不准确
```json
{
  "statement": "import './styles.css';",
  "targetFilePath": "./styles.css.ts",  // ❌ 错误：应该是正确的相对路径
}
```

**修复**: 改进了相对路径解析
- 基于当前文件位置计算正确的相对路径
- 支持多种文件扩展名
- 自动查找 index 文件

## 技术实现

### 1. 增强的别名识别 (ImportClassifier.java)

```java
// 支持标准 @/ 别名
if (moduleSpecifier.startsWith("@/")) {
    return false; // 内部模块
}

// 支持自定义项目别名
if (moduleSpecifier.startsWith("@") && moduleSpecifier.contains("/") && !isNpmPackage(moduleSpecifier)) {
    return false; // 内部模块
}

// 区分 npm 包和项目别名
private static boolean isNpmPackage(String moduleSpecifier) {
    // 检查常见项目别名列表
    String[] commonProjectAliases = {
        "@page", "@components", "@utils", "@services", "@assets", 
        "@styles", "@views", "@layouts", "@hooks", "@store", "@api"
    };
    // ...
}
```

### 2. 改进的目标文件解析 (TypeScriptImportAnalyzer.java)

```java
private static String resolveTypeScriptTargetFilePath(PsiElement importElement, String moduleSpecifier, boolean isExternal) {
    // 1. 首先尝试 PSI 解析（无论内外部）
    PsiElement resolved = resolveModule(importElement, moduleSpecifier);
    if (resolved instanceof PsiFile) {
        return getProjectRelativeFilePath((PsiFile) resolved);
    }
    
    // 2. 对于内部模块，使用增强解析逻辑
    if (!isExternal) {
        return resolveInternalModuleWithEnhancedLogic(importElement, moduleSpecifier);
    }
    
    return null;
}
```

### 3. 增强的内部模块解析

```java
private static String resolveInternalModuleWithEnhancedLogic(PsiElement context, String moduleSpecifier) {
    // 1. 处理相对路径
    if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
        return resolveRelativeModulePath(context, moduleSpecifier, projectDir);
    }
    
    // 2. 处理各种别名模式
    if (moduleSpecifier.startsWith("@")) {
        return resolveAliasModulePath(moduleSpecifier, projectDir);
    }
    
    // 3. 处理绝对路径
    return resolveAbsoluteModulePath(moduleSpecifier, projectDir);
}
```

## 支持的别名模式

### 1. 标准别名
- `@/components/Button` → `src/components/Button.tsx`
- `@/utils/helper` → `src/utils/helper.ts`

### 2. 自定义项目别名
- `@page/actForm/utils` → `static/page/actForm/utils/index.ts`
- `@components/Button` → `src/components/Button.tsx`
- `@utils/helper` → `src/utils/helper.ts`

### 3. 相对路径
- `./styles.css` → `static/components/activity/CheckboxGroupPro/styles.css`
- `../utils/helper` → 正确的相对路径

### 4. 外部模块
- `antd/lib/checkbox` → `node_modules/antd/lib/checkbox/index.d.ts`
- `react` → `node_modules/react/index.d.ts`

## 预期修复结果

修复后，之前的问题应该得到解决：

```json
[
    {
      "statement": "import { replaceToPlainStr } from '@page/actForm/utils';",
      "lineNumber": 6,
      "type": "FROM_IMPORT",
      "isExternal": false,  // ✅ 修复：正确识别为内部模块
      "filePath": "static/components/activity/CheckboxGroupPro/index.tsx",
      "targetFilePath": "static/page/actForm/utils/index.ts",  // ✅ 修复：正确的目标文件路径
      "resolvedClasses": [
        "static/page/actForm/utils/replaceToPlainStr"
      ]
    },
    {
      "statement": "import { CheckboxGroupProps } from 'antd/lib/checkbox';",
      "lineNumber": 7,
      "type": "FROM_IMPORT",
      "isExternal": true,
      "filePath": "static/components/activity/CheckboxGroupPro/index.tsx",
      "targetFilePath": "node_modules/antd/lib/checkbox/index.d.ts",  // ✅ 修复：正确的外部文件路径
      "resolvedClasses": [
        "antd/lib/checkbox/CheckboxGroupProps"
      ]
    },    
   {
      "statement": "import './styles.css';",
      "lineNumber": 9,
      "type": "SINGLE",
      "isExternal": false,
      "filePath": "static/components/activity/CheckboxGroupPro/index.tsx",
      "targetFilePath": "static/components/activity/CheckboxGroupPro/styles.css",  // ✅ 修复：正确的相对路径
      "resolvedClasses": [
        "static/components/activity/CheckboxGroupPro/styles"
      ]
    }
]
```

## 测试建议

1. **别名测试**: 测试各种项目别名是否正确识别为内部模块
2. **路径解析测试**: 验证 `targetFilePath` 是否正确解析到实际文件
3. **相对路径测试**: 确认相对路径计算的准确性
4. **外部模块测试**: 验证 npm 包是否正确识别并解析路径

## 注意事项

1. **项目结构**: 确保项目有清晰的目录结构
2. **别名配置**: 如果使用自定义别名，确保在 `commonProjectAliases` 列表中
3. **文件扩展名**: 支持 `.ts`, `.tsx`, `.js`, `.jsx`, `.vue`, `.d.ts` 等
4. **Index 文件**: 自动查找目录下的 index 文件

这些修复应该显著提升 TypeScript 导入解析的准确性和可靠性。
