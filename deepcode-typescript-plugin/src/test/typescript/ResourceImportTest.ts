// 资源文件导入测试
// 用于测试基于 PSI 的静态资源文件导入分析

// ============ CSS 样式文件导入 ============

// 1. CSS 文件导入
import './styles.css';
import '../shared/common.css';
import '@/styles/global.css';

// 2. CSS 预处理器文件导入
import './styles.scss';
import '../styles/variables.sass';
import '@/styles/mixins.less';
import './theme.styl';

// ============ 图片文件导入 ============
// 3. 常见图片格式
import logoUrl from './assets/logo.png';

// 4. 其他图片格式

// ============ 字体文件导入 ============
// 5. 常见字体格式
import fontWoff2 from '../assets/fonts/roboto.woff2';

// ============ 音视频文件导入 ============
// 6. 音频文件
import clickSound from './sounds/click.mp3';

// 7. 视频文件
import introVideo from './videos/intro.mp4';

// ============ 文档文件导入 ============
// 8. 文档文件
import userManual from './docs/manual.pdf';

// ============ 第三方资源 ============
// 9. CDN 资源（外部）
import 'https://cdn.jsdelivr.net/npm/normalize.css@8.0.1/normalize.css';

// 10. npm 包中的资源（外部）
import 'bootstrap/dist/css/bootstrap.min.css';

// Simple component for testing
export class ResourceTestComponent {
    constructor() {
        console.log('ResourceTestComponent created');
        console.log('Logo URL:', logoUrl);
        console.log('Font loaded:', fontWoff2);
        console.log('Video path:', introVideo);
    }

    render() {
        return `
            <div>
                <img src="${logoUrl}" alt="Logo" />
                <video src="${introVideo}" controls />
            </div>
        `;
    }
}

// Test function for asset handling
export function loadAssets() {
    const assets = {
        css: './styles.css',
        image: logoUrl,
        font: fontWoff2,
        video: introVideo,
        audio: clickSound,
        document: userManual
    };

    console.log('All assets loaded:', assets);
    return assets;
}

