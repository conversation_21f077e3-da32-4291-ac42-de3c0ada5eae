// 全面的静态资源文件导入测试
// 用于验证基于 PSI 的资源文件导入分析功能

// ============ CSS 样式文件导入 ============

// 1. 相对路径 CSS 文件导入（应该被标记为内部）
import './styles.css';
import '../shared/common.css';
import '../../global/base.css';

// 2. CSS 预处理器文件导入（应该被标记为内部）
import './styles.scss';
import '../styles/variables.sass';
import './theme.less';
import '../../assets/styles/mixins.styl';

// 3. 别名路径 CSS 文件导入（应该被标记为内部）
import '@/styles/global.css';
import '@/assets/styles/theme.scss';
import '~/styles/utilities.less';

// 4. 第三方 CSS 库（应该被标记为外部）
import 'bootstrap/dist/css/bootstrap.min.css';
import 'antd/dist/antd.css';
import 'normalize.css';

// ============ 图片文件导入 ============
// 5. 相对路径图片导入（应该被标记为内部）
import logoUrl from './assets/logo.png';
import bannerImg from '../images/banner.jpg';
import iconSvg from '../../icons/icon.svg';

// 6. 别名路径图片导入（应该被标记为内部）
import headerLogo from '@/assets/images/header-logo.png';

// 7. 外部图片资源（应该被标记为外部）
import externalImage from 'https://cdn.example.com/images/logo.png';
import cdnBanner from 'https://assets.example.com/banner.jpg';

// ============ 字体文件导入 ============
// 8. 相对路径字体导入（应该被标记为内部）
import customFont from './fonts/custom.woff';
import robotoFont from '../assets/fonts/roboto.woff2';

// 9. 别名路径字体导入（应该被标记为内部）
import mainFont from '@/assets/fonts/main.woff2';

// ============ 音视频文件导入 ============
// 10. 相对路径音频导入（应该被标记为内部）
import clickSound from './sounds/click.mp3';
import backgroundMusic from '../audio/background.wav';

// 11. 相对路径视频导入（应该被标记为内部）
import introVideo from './videos/intro.mp4';
import demoClip from '../media/demo.avi';

// 12. 别名路径媒体导入（应该被标记为内部）
import welcomeVideo from '@/assets/videos/welcome.mp4';
import backgroundAudio from '~/media/background.mp3';

// ============ 文档文件导入 ============
// 13. 相对路径文档导入（应该被标记为内部）
import userManual from './docs/manual.pdf';
import readme from '../README.md';

// 14. 别名路径文档导入（应该被标记为内部）
import apiDocs from '@/docs/api.pdf';

// ============ 其他资源文件导入 ============
// 15. 二进制文件和其他格式

// ============ CDN 和外部资源 ============
// 16. CDN 资源（应该被标记为外部）
import 'https://cdn.jsdelivr.net/npm/normalize.css@8.0.1/normalize.css';

// ============ 常规模块导入（对比测试）============
// 17. 常规 JavaScript/TypeScript 模块导入

// 18. 项目内部模块导入

// ============ 测试组件 ============

export class ResourceTestComponent {
    constructor() {
        console.log('ResourceTestComponent created');
        console.log('Loaded resources:');
        console.log('- Logo URL:', logoUrl);
        console.log('- Custom font:', customFont);
        console.log('- Intro video:', introVideo);
        console.log('- Click sound:', clickSound);
        console.log('- User manual:', userManual);
    }

    render() {
        return `
            <div class="resource-test">
                <img src="${logoUrl}" alt="Logo" />
                <img src="${headerLogo}" alt="Header Logo" />
                <video src="${introVideo}" controls />
                <audio src="${clickSound}" />
                <style>
                    @font-face {
                        font-family: 'Custom';
                        src: url('${customFont}') format('woff');
                    }
                </style>
            </div>
        `;
    }

    loadAllResources() {
        const resources = {
            // CSS 资源
            css: {
                internal: ['./styles.css', '../shared/common.css', '@/styles/global.css'],
                external: ['bootstrap/dist/css/bootstrap.min.css', 'normalize.css']
            },

            // 图片资源
            images: {
                internal: [logoUrl, bannerImg, iconSvg, headerLogo],
                external: [externalImage, cdnBanner]
            },

            // 字体资源
            fonts: {
                internal: [customFont, robotoFont, mainFont],
                external: []
            },

            // 媒体资源
            media: {
                videos: [introVideo, demoClip, welcomeVideo],
                audios: [clickSound, backgroundMusic, backgroundAudio]
            },

            // 文档资源
            documents: [userManual, readme, apiDocs]
        };

        console.log('All loaded resources:', resources);
        return resources;
    }
}

// 导出测试函数
export function testResourceImports() {
    console.log('Testing resource import analysis...');

    const component = new ResourceTestComponent();
    const resources = component.loadAllResources();

    console.log('Resource import test completed');
    return { component, resources };
}

