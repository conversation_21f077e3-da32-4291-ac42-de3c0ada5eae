package com.sankuai.deepcode.astplugin.typescript;

import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.openapi.diagnostic.Logger;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.ImportInfo;
import com.sankuai.deepcode.astplugin.typescript.util.TypeScriptImportAnalyzer;

/**
 * TypeScript 导入分析器测试类
 * 用于验证基于 PSI 的导入关系解析功能
 *
 * <AUTHOR>
 */
public class TypeScriptImportAnalyzerTest {

    private static final Logger LOG = Logger.getInstance(TypeScriptImportAnalyzerTest.class);

    /**
     * 测试导入分析
     */
    public static void testImportAnalysis(JSFile jsFile) {
        try {
            LOG.info("=== TypeScript Import Analysis Test ===");
            LOG.info("Analyzing file: " + jsFile.getName());

            AnalysisResult result = new AnalysisResult(jsFile.getName(), "TypeScript");

            // 分析导入关系
            TypeScriptImportAnalyzer.analyzeImports(jsFile, result);

            // 打印结果
            printImportResults(result);

        } catch (Exception e) {
            LOG.error("Test failed", e);
        }
    }

    /**
     * 打印导入分析结果
     */
    private static void printImportResults(AnalysisResult result) {
        LOG.info("=== Import Analysis Results ===");
        LOG.info("Total imports: " + result.getImports().size());

        if (!result.getErrors().isEmpty()) {
            LOG.info("Errors: " + result.getErrors().size());
            for (String error : result.getErrors()) {
                LOG.warn("Error: " + error);
            }
        }

        LOG.info("=== Import Details ===");
        int index = 1;
        for (ImportInfo importInfo : result.getImports()) {
            LOG.info("Import " + index + ":");
            LOG.info("  Statement: " + importInfo.getStatement());
            LOG.info("  Line: " + importInfo.getLineNumber());
            LOG.info("  Type: " + importInfo.getType());
            LOG.info("  External: " + importInfo.isExternal());
            LOG.info("  Resolved: " + importInfo.getResolvedClasses());

            // 分析导入类别
            analyzeImportCategory(importInfo);

            index++;
        }

        // 统计信息
        printImportStatistics(result);
    }

    /**
     * 分析导入类别
     */
    private static void analyzeImportCategory(ImportInfo importInfo) {
        String statement = importInfo.getStatement();

        if (statement.contains("react")) {
            LOG.info("  Category: React Framework");
        } else if (statement.contains("vue")) {
            LOG.info("  Category: Vue Framework");
        } else if (statement.contains("lodash") || statement.contains("axios") || statement.contains("moment")) {
            LOG.info("  Category: Utility Library");
        } else if (statement.contains("@/") || statement.contains("~/")) {
            LOG.info("  Category: Project Alias");
        } else if (statement.startsWith("./") || statement.startsWith("../")) {
            LOG.info("  Category: Relative Import");
        } else if (statement.contains("require(")) {
            LOG.info("  Category: CommonJS");
        } else if (statement.contains("import type")) {
            LOG.info("  Category: TypeScript Type Import");
        } else if (statement.contains("import *")) {
            LOG.info("  Category: Namespace Import");
        } else if (statement.contains("import {")) {
            LOG.info("  Category: Named Import");
        } else {
            LOG.info("  Category: Default Import");
        }
    }

    /**
     * 打印导入统计信息
     */
    private static void printImportStatistics(AnalysisResult result) {
        LOG.info("=== Import Statistics ===");

        int totalImports = result.getImports().size();
        int externalImports = 0;
        int internalImports = 0;
        int singleImports = 0;
        int namedImports = 0;
        int wildcardImports = 0;

        for (ImportInfo importInfo : result.getImports()) {
            if (importInfo.isExternal()) {
                externalImports++;
            } else {
                internalImports++;
            }

            switch (importInfo.getType()) {
                case SINGLE:
                    singleImports++;
                    break;
                case FROM_IMPORT:
                    namedImports++;
                    break;
                case WILDCARD:
                    wildcardImports++;
                    break;
                default:
                    break;
            }
        }

        LOG.info("Total: " + totalImports);
        LOG.info("External: " + externalImports + " (" + (externalImports * 100 / Math.max(1, totalImports)) + "%)");
        LOG.info("Internal: " + internalImports + " (" + (internalImports * 100 / Math.max(1, totalImports)) + "%)");
        LOG.info("Single: " + singleImports);
        LOG.info("Named: " + namedImports);
        LOG.info("Wildcard: " + wildcardImports);
    }
}

