package com.sankuai.deepcode.astplugin.typescript;

import com.intellij.lang.javascript.psi.JSCallExpression;
import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;
import com.sankuai.deepcode.astplugin.typescript.util.TypeScriptCallAnalyzer;

import java.util.Collection;

/**
 * TypeScript 调用关系分析器测试类
 * 用于验证调用关系解析功能
 *
 * <AUTHOR>
 */
public class TypeScriptCallAnalyzerTest {

    private static final Logger LOG = Logger.getInstance(TypeScriptCallAnalyzerTest.class);

    /**
     * 测试调用关系分析
     */
    public static void testCallAnalysis(JSFile jsFile) {
        try {
            LOG.info("=== TypeScript Call Relation Analysis Test ===");
            LOG.info("Analyzing file: " + jsFile.getName());

            AnalysisResult result = new AnalysisResult(jsFile.getName(), "TypeScript");

            // 分析调用关系
            TypeScriptCallAnalyzer.analyzeCallRelations(jsFile, result);

            // 打印结果
            printAnalysisResults(result);

            // 详细分析每个调用
            analyzeIndividualCalls(jsFile, result);

        } catch (Exception e) {
            LOG.error("Test failed", e);
        }
    }

    /**
     * 打印分析结果
     */
    private static void printAnalysisResults(AnalysisResult result) {
        LOG.info("=== Analysis Results ===");
        LOG.info("Total nodes: " + result.getNodes().size());
        LOG.info("Total call relations: " + result.getCallRelations().size());

        if (!result.getErrors().isEmpty()) {
            LOG.info("Errors: " + result.getErrors().size());
            for (String error : result.getErrors()) {
                LOG.warn("Error: " + error);
            }
        }

        LOG.info("=== Call Relations ===");
        for (CallRelation relation : result.getCallRelations()) {
            LOG.info("Call: " + relation.getCaller().getName() + " -> " +
                    relation.getCallee().getName() +
                    " (line " + relation.getCallLineNumber() +
                    ", external: " + relation.isExternal() + ")");
            LOG.info("  Expression: " + relation.getCallExpression());

            if (relation.getAllCallInstances().size() > 1) {
                LOG.info("  Multiple instances:");
                for (CallRelation.CallInstance instance : relation.getAllCallInstances()) {
                    LOG.info("    Line " + instance.getLineNumber() + ": " + instance.getExpression());
                }
            }
        }
    }

    /**
     * 详细分析每个调用表达式
     */
    private static void analyzeIndividualCalls(JSFile jsFile, AnalysisResult result) {
        LOG.info("=== Individual Call Analysis ===");

        Collection<JSCallExpression> callExpressions = PsiTreeUtil.findChildrenOfType(jsFile, JSCallExpression.class);
        LOG.info("Found " + callExpressions.size() + " call expressions");

        int index = 1;
        for (JSCallExpression callExpression : callExpressions) {
            try {
                LOG.info("Call " + index + ":");
                LOG.info("  Text: " + callExpression.getText());
                LOG.info("  Method: " + (callExpression.getMethodExpression() != null ?
                                         callExpression.getMethodExpression().getText() : "null"));

                // 测试查找调用方节点
                var callerNode = TypeScriptCallAnalyzer.findCallerNode(callExpression, result);
                if (callerNode != null) {
                    LOG.info("  Caller: " + callerNode.getName() + " (type: " + callerNode.getType() + ")");
                } else {
                    LOG.info("  Caller: not found");
                }

                index++;

            } catch (Exception e) {
                LOG.warn("Error analyzing call " + index + ": " + e.getMessage());
                index++;
            }
        }
    }
}

