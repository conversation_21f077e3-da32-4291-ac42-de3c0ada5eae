package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.javascript.psi.*;
import com.intellij.lang.javascript.psi.ecmal4.JSClass;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.Language;

/**
 * TypeScript/JavaScript 调用表达式解析器
 * 负责解析不同类型的函数调用表达式
 *
 * <AUTHOR>
 */
public final class TypeScriptCallExpressionResolver {

    private static final Logger LOG = Logger.getInstance(TypeScriptCallExpressionResolver.class);

    private TypeScriptCallExpressionResolver() {
        // 工具类不允许实例化
    }

    /**
     * 解析结果容器
     */
    public record ResolveResult(AnalysisNode calleeNode, boolean isExternal) {
    }

    /**
     * 解析函数调用（JSFunction类型）
     */
    public static ResolveResult resolveFunctionCall(JSFunction resolvedFunction, JSCallExpression callExpression) {
        return resolveFunctionCall(resolvedFunction, callExpression, false);
    }

    /**
     * 解析函数调用（JSFunction类型）- 带调试选项
     */
    public static ResolveResult resolveFunctionCall(JSFunction resolvedFunction, JSCallExpression callExpression, boolean enableDebug) {
        try {
            if (enableDebug) {
                System.out.println("[DeepCode]   === resolveFunctionCall Debug ===");
                System.out.println("[DeepCode]   Function name: " + (resolvedFunction.getName() != null ? resolvedFunction.getName() : "anonymous"));
            }

            PsiFile resolvedFile = resolvedFunction.getContainingFile();
            PsiFile callerFile = callExpression.getContainingFile();
            boolean isSameFile = resolvedFile != null && resolvedFile.equals(callerFile);

            if (enableDebug) {
                System.out.println("[DeepCode]   Resolved file: " + (resolvedFile != null ? resolvedFile.getName() : "null"));
                System.out.println("[DeepCode]   Caller file: " + (callerFile != null ? callerFile.getName() : "null"));
                System.out.println("[DeepCode]   Is same file: " + isSameFile);
            }

            boolean isProjectInternal = isProjectInternalFunction(resolvedFunction, callExpression, enableDebug);
            if (enableDebug) {
                System.out.println("[DeepCode]   Is project internal: " + isProjectInternal);
            }

            if (isSameFile) {
                // 同文件内的函数调用
                if (enableDebug) System.out.println("[DeepCode]   ✅ Creating same-file function node (internal)");
                return createSameFileFunctionNode(resolvedFunction, callExpression);
            } else if (isProjectInternal) {
                // 项目内部但不同文件的函数调用
                if (enableDebug) System.out.println("[DeepCode]   ✅ Creating project-internal function node");
                return createProjectInternalFunctionNode(resolvedFunction);
            } else {
                // 外部函数调用（第三方库等）
                if (enableDebug) System.out.println("[DeepCode]   ❌ Creating external function node");
                return createExternalFunctionNode(resolvedFunction);
            }
        } catch (Exception e) {
            LOG.warn("Error resolving function call: " + resolvedFunction, e);
            if (enableDebug) System.out.println("[DeepCode]   ❌ Using fallback function node");
            return createFallbackFunctionNode(resolvedFunction, callExpression);
        }
    }

    /**
     * 解析类调用（JSClass类型）
     */
    public static ResolveResult resolveClassCall(JSClass resolvedClass, JSCallExpression callExpression) {
        try {
            PsiFile resolvedFile = resolvedClass.getContainingFile();
            PsiFile callerFile = callExpression.getContainingFile();
            boolean isSameFile = resolvedFile != null && resolvedFile.equals(callerFile);
            boolean isProjectInternal = isProjectInternalClass(resolvedClass, callExpression);

            if (isSameFile) {
                // 同文件内的类构造函数调用
                return createSameFileClassNode(resolvedClass, callExpression);
            } else if (isProjectInternal) {
                // 项目内部但不同文件的类构造函数调用
                return createProjectInternalClassNode(resolvedClass);
            } else {
                // 外部类构造函数调用（第三方库等）
                return createExternalClassNode(resolvedClass);
            }
        } catch (Exception e) {
            LOG.warn("Error resolving class call: " + resolvedClass, e);
            return createFallbackClassNode(resolvedClass, callExpression);
        }
    }

    /**
     * 解析变量调用（箭头函数变量）
     */
    public static ResolveResult resolveVariableCall(PsiElement resolvedVariable, JSCallExpression callExpression, boolean enableDebug) {
        try {
            if (enableDebug) {
                System.out.println("[DeepCode]   === resolveVariableCall Debug ===");
                System.out.println("[DeepCode]   Variable class: " + resolvedVariable.getClass().getSimpleName());
                System.out.println("[DeepCode]   Variable text: " + resolvedVariable.getText().substring(0, Math.min(100, resolvedVariable.getText().length())) + "...");
            }

            PsiFile resolvedFile = resolvedVariable.getContainingFile();
            PsiFile callerFile = callExpression.getContainingFile();
            boolean isSameFile = resolvedFile != null && resolvedFile.equals(callerFile);

            if (enableDebug) {
                System.out.println("[DeepCode]   Resolved file: " + (resolvedFile != null ? resolvedFile.getName() : "null"));
                System.out.println("[DeepCode]   Caller file: " + (callerFile != null ? callerFile.getName() : "null"));
                System.out.println("[DeepCode]   Is same file: " + isSameFile);
            }

            boolean isProjectInternal = isProjectInternalVariable(resolvedVariable, callExpression, enableDebug);
            if (enableDebug) {
                System.out.println("[DeepCode]   Is project internal: " + isProjectInternal);
            }

            if (isSameFile) {
                // 同文件内的变量调用
                if (enableDebug) System.out.println("[DeepCode]   ✅ Creating same-file variable node (internal)");
                return createSameFileVariableNode(resolvedVariable, callExpression);
            } else if (isProjectInternal) {
                // 项目内部但不同文件的变量调用
                if (enableDebug) System.out.println("[DeepCode]   ✅ Creating project-internal variable node");
                return createProjectInternalVariableNode(resolvedVariable);
            } else {
                // 外部变量调用（第三方库等）
                if (enableDebug) System.out.println("[DeepCode]   ❌ Creating external variable node");
                return createExternalVariableNode(resolvedVariable);
            }
        } catch (Exception e) {
            LOG.warn("Error resolving variable call: " + resolvedVariable, e);
            if (enableDebug) System.out.println("[DeepCode]   ❌ Using fallback variable node");
            return createFallbackVariableNode(resolvedVariable, callExpression);
        }
    }

    /**
     * 解析属性访问调用（如 obj.method()）
     */
    public static ResolveResult resolvePropertyCall(JSReferenceExpression referenceExpression, JSCallExpression callExpression, String calleeText) {
        try {
            // 分析属性引用
            String[] parts = calleeText.split("\\.");
            if (parts.length >= 2) {
                String objectRef = parts[0]; // 如 "console", "this", "obj"
                String methodName = parts[parts.length - 1]; // 如 "log", "method"

                // 检查是否是 this 调用
                if ("this".equals(objectRef)) {
                    return resolveThisMethodCall(methodName, callExpression, calleeText);
                }

                // 检查是否是参数调用
                if (isParameterCall(objectRef, callExpression)) {
                    return createParameterCallNode(objectRef, methodName, callExpression, calleeText);
                }

                // 检查是否是全局对象调用
                if (isGlobalObjectCall(objectRef)) {
                    return createGlobalObjectCallNode(objectRef, methodName, callExpression, calleeText);
                }

                // 默认创建外部方法调用节点
                return createExternalMethodNodeResult(callExpression, calleeText);
            }

            // 单一方法调用
            return createExternalMethodNodeResult(callExpression, calleeText);
        } catch (Exception e) {
            LOG.warn("Error resolving property call: " + calleeText, e);
            return createExternalMethodNodeResult(callExpression, calleeText);
        }
    }

    /**
     * 解析无法直接解析的调用
     */
    public static ResolveResult resolveUnknownCall(JSCallExpression callExpression, String calleeText) {
        return createExternalMethodNodeResult(callExpression, calleeText);
    }

    /**
     * 创建同文件函数节点
     */
    private static ResolveResult createSameFileFunctionNode(JSFunction resolvedFunction, JSCallExpression callExpression) {
        String functionName = resolvedFunction.getName() != null ? resolvedFunction.getName() : "anonymous";
        String packageName = TypeScriptPsiUtils.getPackageName(resolvedFunction);
        String className = getContainingClassName(resolvedFunction);

        String fullId = packageName + "." + (className != null ? className + "." : "") + functionName;
        String signature = TypeScriptPsiUtils.buildFunctionSignature(resolvedFunction);

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                className != null ? AnalysisNode.NodeType.METHOD : AnalysisNode.NodeType.FUNCTION,
                functionName,
                className,
                packageName,
                TypeScriptPsiUtils.getLineNumber(resolvedFunction),
                signature,
                packageName,
                TypeScriptPsiUtils.getFilePath(resolvedFunction),
                detectLanguageCode(resolvedFunction)
        );

        return new ResolveResult(calleeNode, false);
    }

    /**
     * 创建项目内部函数节点
     */
    private static ResolveResult createProjectInternalFunctionNode(JSFunction resolvedFunction) {
        String functionName = resolvedFunction.getName() != null ? resolvedFunction.getName() : "anonymous";
        String packageName = TypeScriptPsiUtils.getPackageName(resolvedFunction);
        String className = getContainingClassName(resolvedFunction);

        String fullId = packageName + "." + (className != null ? className + "." : "") + functionName;
        String signature = TypeScriptPsiUtils.buildFunctionSignature(resolvedFunction);

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                className != null ? AnalysisNode.NodeType.METHOD : AnalysisNode.NodeType.FUNCTION,
                functionName,
                className,
                packageName,
                TypeScriptPsiUtils.getLineNumber(resolvedFunction),
                signature,
                packageName,
                TypeScriptPsiUtils.getFilePath(resolvedFunction),
                detectLanguageCode(resolvedFunction)
        );

        LOG.debug("Created project internal function node: " + fullId);
        return new ResolveResult(calleeNode, false);
    }

    /**
     * 创建外部函数节点
     */
    private static ResolveResult createExternalFunctionNode(JSFunction resolvedFunction) {
        String functionName = resolvedFunction.getName() != null ? resolvedFunction.getName() : "anonymous";
        String packageName = TypeScriptPsiUtils.getPackageName(resolvedFunction);
        String className = getContainingClassName(resolvedFunction);

        String fullId = packageName + "." + (className != null ? className + "." : "") + functionName;
        String signature = TypeScriptPsiUtils.buildFunctionSignature(resolvedFunction);

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                className != null ? AnalysisNode.NodeType.METHOD : AnalysisNode.NodeType.FUNCTION,
                functionName,
                className,
                packageName,
                TypeScriptPsiUtils.getLineNumber(resolvedFunction),
                signature,
                packageName,
                TypeScriptPsiUtils.getFilePath(resolvedFunction),
                detectLanguageCode(resolvedFunction)
        );

        LOG.debug("Created external function node: " + fullId);
        return new ResolveResult(calleeNode, true);
    }

    /**
     * 创建同文件类节点
     */
    private static ResolveResult createSameFileClassNode(JSClass resolvedClass, JSCallExpression callExpression) {
        String className = resolvedClass.getName() != null ? resolvedClass.getName() : "AnonymousClass";
        String packageName = TypeScriptPsiUtils.getPackageName(resolvedClass);

        String fullId = packageName + "." + className;
        String signature = className + "()"; // 构造函数签名

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.CLASS,
                className,
                className,
                packageName,
                TypeScriptPsiUtils.getLineNumber(resolvedClass),
                signature,
                packageName,
                TypeScriptPsiUtils.getFilePath(resolvedClass),
                detectLanguageCode(resolvedClass)
        );

        return new ResolveResult(calleeNode, false);
    }

    /**
     * 创建项目内部类节点
     */
    private static ResolveResult createProjectInternalClassNode(JSClass resolvedClass) {
        String className = resolvedClass.getName() != null ? resolvedClass.getName() : "AnonymousClass";
        String packageName = TypeScriptPsiUtils.getPackageName(resolvedClass);

        String fullId = packageName + "." + className;
        String signature = className + "()"; // 构造函数签名

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.CLASS,
                className,
                className,
                packageName,
                TypeScriptPsiUtils.getLineNumber(resolvedClass),
                signature,
                packageName,
                TypeScriptPsiUtils.getFilePath(resolvedClass),
                detectLanguageCode(resolvedClass)
        );

        LOG.debug("Created project internal class node: " + fullId);
        return new ResolveResult(calleeNode, false);
    }

    /**
     * 创建外部类节点
     */
    private static ResolveResult createExternalClassNode(JSClass resolvedClass) {
        String className = resolvedClass.getName() != null ? resolvedClass.getName() : "AnonymousClass";
        String packageName = TypeScriptPsiUtils.getPackageName(resolvedClass);

        String fullId = packageName + "." + className;
        String signature = className + "()"; // 构造函数签名

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.CLASS,
                className,
                className,
                packageName,
                TypeScriptPsiUtils.getLineNumber(resolvedClass),
                signature,
                packageName,
                TypeScriptPsiUtils.getFilePath(resolvedClass),
                detectLanguageCode(resolvedClass)
        );

        LOG.debug("Created external class node: " + fullId);
        return new ResolveResult(calleeNode, true);
    }

    /**
     * 解析 this 方法调用
     */
    private static ResolveResult resolveThisMethodCall(String methodName, JSCallExpression callExpression, String calleeText) {
        JSClass containingClass = PsiTreeUtil.getParentOfType(callExpression, JSClass.class);
        if (containingClass != null) {
            String className = containingClass.getName();
            String packageName = TypeScriptPsiUtils.getPackageName(containingClass);

            String fullId = packageName + "." + className + "." + methodName;
            String signature = methodName + "()";

            AnalysisNode calleeNode = new AnalysisNode(
                    fullId,
                    AnalysisNode.NodeType.METHOD,
                    methodName,
                    className,
                    packageName,
                    TypeScriptPsiUtils.getLineNumber(callExpression),
                    signature,
                    packageName,
                    TypeScriptPsiUtils.getFilePath(containingClass),
                    detectLanguageCode(callExpression)
            );

            LOG.debug("Created this method call node: " + fullId);
            return new ResolveResult(calleeNode, false);
        }

        // 回退到外部方法节点
        return createExternalMethodNodeResult(callExpression, calleeText);
    }

    /**
     * 创建参数调用节点
     */
    private static ResolveResult createParameterCallNode(String parameterName, String methodName, JSCallExpression callExpression, String calleeText) {
        JSFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, JSFunction.class);
        if (containingFunction != null) {
            String functionName = containingFunction.getName() != null ? containingFunction.getName() : "anonymous";
            String packageName = TypeScriptPsiUtils.getPackageName(containingFunction);
            String className = getContainingClassName(containingFunction);

            String functionPath = packageName + "." + (className != null ? className + "." : "") + functionName;
            String fullId = functionPath + "." + parameterName;
            String signature = functionPath + "." + parameterName + "." + methodName + "()";

            AnalysisNode calleeNode = new AnalysisNode(
                    fullId,
                    AnalysisNode.NodeType.FUNCTION,
                    methodName,
                    null,
                    null,
                    TypeScriptPsiUtils.getLineNumber(callExpression),
                    signature,
                    packageName,
                    TypeScriptPsiUtils.getFilePath(containingFunction),
                    detectLanguageCode(callExpression)
            );

            LOG.debug("Created parameter call node: " + fullId);
            return new ResolveResult(calleeNode, true);
        }

        // 回退到外部方法节点
        return createExternalMethodNodeResult(callExpression, calleeText);
    }

    /**
     * 创建全局对象调用节点
     */
    private static ResolveResult createGlobalObjectCallNode(String objectName, String methodName, JSCallExpression callExpression, String calleeText) {
        String fullId = "GLOBAL." + objectName + "." + methodName;
        String signature = objectName + "." + methodName + "()";

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.FUNCTION,
                methodName,
                objectName,
                "GLOBAL",
                TypeScriptPsiUtils.getLineNumber(callExpression),
                signature,
                "GLOBAL",
                "GLOBAL",
                detectLanguageCode(callExpression)
        );

        LOG.debug("Created global object call node: " + fullId);
        return new ResolveResult(calleeNode, true);
    }

    /**
     * 创建外部方法节点
     */
    public static AnalysisNode createExternalMethodNode(JSCallExpression callExpression, String calleeText) {
        String methodName = extractMethodName(calleeText);
        String fullId = calleeText;
        String signature = methodName + "()";

        return new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.FUNCTION,
                methodName,
                null,
                "EXTERNAL",
                TypeScriptPsiUtils.getLineNumber(callExpression),
                signature,
                "EXTERNAL",
                "EXTERNAL",
                detectLanguageCode(callExpression)
        );
    }

    /**
     * 创建外部方法节点并返回解析结果
     */
    public static ResolveResult createExternalMethodNodeResult(JSCallExpression callExpression, String calleeText) {
        String methodName = extractMethodName(calleeText);
        String fullId = calleeText;
        String signature = methodName + "()";

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.FUNCTION,
                methodName,
                null,
                "EXTERNAL",
                TypeScriptPsiUtils.getLineNumber(callExpression),
                signature,
                "EXTERNAL",
                "EXTERNAL",
                detectLanguageCode(callExpression)
        );

        LOG.debug("Created external method node: " + fullId);
        return new ResolveResult(calleeNode, true);
    }

    /**
     * 创建回退函数节点
     */
    private static ResolveResult createFallbackFunctionNode(JSFunction resolvedFunction, JSCallExpression callExpression) {
        String functionName = resolvedFunction.getName() != null ? resolvedFunction.getName() : "unknown";
        String fullId = "FALLBACK." + functionName;
        String signature = functionName + "()";

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.FUNCTION,
                functionName,
                null,
                "FALLBACK",
                TypeScriptPsiUtils.getLineNumber(callExpression),
                signature,
                "FALLBACK",
                TypeScriptPsiUtils.getFilePath(callExpression),
                detectLanguageCode(callExpression)
        );

        return new ResolveResult(calleeNode, true);
    }

    /**
     * 创建回退类节点
     */
    private static ResolveResult createFallbackClassNode(JSClass resolvedClass, JSCallExpression callExpression) {
        String className = resolvedClass.getName() != null ? resolvedClass.getName() : "UnknownClass";
        String fullId = "FALLBACK." + className;
        String signature = className + "()";

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.CLASS,
                className,
                className,
                "FALLBACK",
                TypeScriptPsiUtils.getLineNumber(callExpression),
                signature,
                "FALLBACK",
                TypeScriptPsiUtils.getFilePath(callExpression),
                detectLanguageCode(callExpression)
        );

        return new ResolveResult(calleeNode, true);
    }

    /**
     * 创建同文件变量节点
     */
    private static ResolveResult createSameFileVariableNode(PsiElement resolvedVariable, JSCallExpression callExpression) {
        String variableName = extractVariableName(resolvedVariable);
        String packageName = TypeScriptPsiUtils.getPackageName(resolvedVariable);
        String className = getContainingClassNameForElement(resolvedVariable);

        String fullId = packageName + "." + (className != null ? className + "." : "") + variableName;
        String signature = variableName + "()"; // 简化的函数签名

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                className != null ? AnalysisNode.NodeType.METHOD : AnalysisNode.NodeType.FUNCTION,
                variableName,
                className,
                packageName,
                TypeScriptPsiUtils.getLineNumber(resolvedVariable),
                signature,
                packageName,
                TypeScriptPsiUtils.getFilePath(resolvedVariable),
                detectLanguageCode(resolvedVariable)
        );

        return new ResolveResult(calleeNode, false);
    }

    /**
     * 创建项目内部变量节点
     */
    private static ResolveResult createProjectInternalVariableNode(PsiElement resolvedVariable) {
        String variableName = extractVariableName(resolvedVariable);
        String packageName = TypeScriptPsiUtils.getPackageName(resolvedVariable);
        String className = getContainingClassNameForElement(resolvedVariable);

        String fullId = packageName + "." + (className != null ? className + "." : "") + variableName;
        String signature = variableName + "()"; // 简化的函数签名

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                className != null ? AnalysisNode.NodeType.METHOD : AnalysisNode.NodeType.FUNCTION,
                variableName,
                className,
                packageName,
                TypeScriptPsiUtils.getLineNumber(resolvedVariable),
                signature,
                packageName,
                TypeScriptPsiUtils.getFilePath(resolvedVariable),
                detectLanguageCode(resolvedVariable)
        );

        LOG.debug("Created project internal variable node: " + fullId);
        return new ResolveResult(calleeNode, false);
    }

    /**
     * 创建外部变量节点
     */
    private static ResolveResult createExternalVariableNode(PsiElement resolvedVariable) {
        String variableName = extractVariableName(resolvedVariable);
        String packageName = TypeScriptPsiUtils.getPackageName(resolvedVariable);
        String className = getContainingClassNameForElement(resolvedVariable);

        String fullId = packageName + "." + (className != null ? className + "." : "") + variableName;
        String signature = variableName + "()"; // 简化的函数签名

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                className != null ? AnalysisNode.NodeType.METHOD : AnalysisNode.NodeType.FUNCTION,
                variableName,
                className,
                packageName,
                TypeScriptPsiUtils.getLineNumber(resolvedVariable),
                signature,
                packageName,
                TypeScriptPsiUtils.getFilePath(resolvedVariable),
                detectLanguageCode(resolvedVariable)
        );

        LOG.debug("Created external variable node: " + fullId);
        return new ResolveResult(calleeNode, true);
    }

    /**
     * 创建回退变量节点
     */
    private static ResolveResult createFallbackVariableNode(PsiElement resolvedVariable, JSCallExpression callExpression) {
        String variableName = extractVariableName(resolvedVariable);
        String fullId = "FALLBACK." + variableName;
        String signature = variableName + "()";

        AnalysisNode calleeNode = new AnalysisNode(
                fullId,
                AnalysisNode.NodeType.FUNCTION,
                variableName,
                null,
                "FALLBACK",
                TypeScriptPsiUtils.getLineNumber(callExpression),
                signature,
                "FALLBACK",
                TypeScriptPsiUtils.getFilePath(callExpression),
                detectLanguageCode(callExpression)
        );

        return new ResolveResult(calleeNode, true);
    }

    // 辅助方法

    /**
     * 从变量元素中提取变量名
     */
    private static String extractVariableName(PsiElement variable) {
        try {
            // 对于 TypeScriptVariableImpl，尝试获取名称
            String text = variable.getText();
            if (text != null && !text.isEmpty()) {
                // 对于箭头函数，提取变量名
                // 例如：replaceToPlainStr = (str: string): string => str.replace(...
                if (text.contains("=") && text.contains("=>")) {
                    String[] parts = text.split("=", 2);
                    if (parts.length > 0) {
                        return parts[0].trim();
                    }
                }
                // 对于简单的变量名，直接返回
                return text.trim();
            }
            return "unknownVariable";
        } catch (Exception e) {
            LOG.warn("Failed to extract variable name from: " + variable, e);
            return "unknownVariable";
        }
    }

    /**
     * 检查函数是否是项目内部的
     */
    private static boolean isProjectInternalFunction(JSFunction function, JSCallExpression callExpression) {
        return isProjectInternalFunction(function, callExpression, false);
    }

    /**
     * 检查函数是否是项目内部的 - 带调试选项
     */
    private static boolean isProjectInternalFunction(JSFunction function, JSCallExpression callExpression, boolean enableDebug) {
        try {
            // 获取函数所在文件和调用文件
            PsiFile functionFile = function.getContainingFile();
            PsiFile callerFile = callExpression.getContainingFile();

            if (functionFile == null) {
                return false;
            }

            // 获取项目根目录
            String projectBasePath = callExpression.getProject().getBasePath();
            if (projectBasePath == null) {
                return false;
            }

            // 获取函数所在文件的绝对路径
            String functionAbsolutePath = functionFile.getVirtualFile() != null ?
                    functionFile.getVirtualFile().getPath() : null;

            if (functionAbsolutePath == null) {
                return false;
            }

            if (enableDebug) {
                System.out.println("[DeepCode]     Checking if function is project internal:");
                System.out.println("[DeepCode]     Function file: " + functionAbsolutePath);
                System.out.println("[DeepCode]     Project base: " + projectBasePath);
            }

            // 判断是否在项目内部
            boolean isInProject = functionAbsolutePath.startsWith(projectBasePath);
            boolean isInNodeModules = functionAbsolutePath.contains("node_modules");
            boolean isTypeDefinition = functionAbsolutePath.contains(".d.ts");

            boolean isProjectInternal = isInProject && !isInNodeModules && !isTypeDefinition;

            if (enableDebug) {
                System.out.println("[DeepCode]     Is in project: " + isInProject);
                System.out.println("[DeepCode]     Is in node_modules: " + isInNodeModules);
                System.out.println("[DeepCode]     Is type definition: " + isTypeDefinition);
                System.out.println("[DeepCode]     Result: " + isProjectInternal);
            }

            return isProjectInternal;

        } catch (Exception e) {
            System.out.println("[DeepCode]     Error checking if function is project internal: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查类是否是项目内部的
     */
    private static boolean isProjectInternalClass(JSClass jsClass, JSCallExpression callExpression) {
        try {
            // 获取类所在文件和调用文件
            PsiFile classFile = jsClass.getContainingFile();
            PsiFile callerFile = callExpression.getContainingFile();

            if (classFile == null) {
                return false;
            }

            // 获取项目根目录
            String projectBasePath = callExpression.getProject().getBasePath();
            if (projectBasePath == null) {
                return false;
            }

            // 获取类所在文件的绝对路径
            String classAbsolutePath = classFile.getVirtualFile() != null ?
                    classFile.getVirtualFile().getPath() : null;

            if (classAbsolutePath == null) {
                return false;
            }

            LOG.debug("Checking if class is project internal:");
            LOG.debug("  Class file: " + classAbsolutePath);
            LOG.debug("  Project base: " + projectBasePath);

            // 判断是否在项目内部
            boolean isInProject = classAbsolutePath.startsWith(projectBasePath);
            boolean isInNodeModules = classAbsolutePath.contains("node_modules");
            boolean isTypeDefinition = classAbsolutePath.contains(".d.ts");

            boolean isProjectInternal = isInProject && !isInNodeModules && !isTypeDefinition;

            LOG.debug("  Is in project: " + isInProject);
            LOG.debug("  Is in node_modules: " + isInNodeModules);
            LOG.debug("  Is type definition: " + isTypeDefinition);
            LOG.debug("  Result: " + isProjectInternal);

            return isProjectInternal;

        } catch (Exception e) {
            System.out.println("[DeepCode]     Error checking if class is project internal: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查变量是否是项目内部的
     */
    private static boolean isProjectInternalVariable(PsiElement variable, JSCallExpression callExpression) {
        return isProjectInternalVariable(variable, callExpression, false);
    }

    /**
     * 检查变量是否是项目内部的 - 带调试选项
     */
    private static boolean isProjectInternalVariable(PsiElement variable, JSCallExpression callExpression, boolean enableDebug) {
        try {
            // 获取变量所在文件和调用文件
            PsiFile variableFile = variable.getContainingFile();
            PsiFile callerFile = callExpression.getContainingFile();

            if (variableFile == null) {
                return false;
            }

            // 获取项目根目录
            String projectBasePath = callExpression.getProject().getBasePath();
            if (projectBasePath == null) {
                return false;
            }

            // 获取变量所在文件的绝对路径
            String variableAbsolutePath = variableFile.getVirtualFile() != null ?
                    variableFile.getVirtualFile().getPath() : null;

            if (variableAbsolutePath == null) {
                return false;
            }

            if (enableDebug) {
                System.out.println("[DeepCode]     Checking if variable is project internal:");
                System.out.println("[DeepCode]     Variable file: " + variableAbsolutePath);
                System.out.println("[DeepCode]     Project base: " + projectBasePath);
            }

            // 判断是否在项目内部
            boolean isInProject = variableAbsolutePath.startsWith(projectBasePath);
            boolean isInNodeModules = variableAbsolutePath.contains("node_modules");
            boolean isTypeDefinition = variableAbsolutePath.contains(".d.ts");

            boolean isProjectInternal = isInProject && !isInNodeModules && !isTypeDefinition;

            if (enableDebug) {
                System.out.println("[DeepCode]     Is in project: " + isInProject);
                System.out.println("[DeepCode]     Is in node_modules: " + isInNodeModules);
                System.out.println("[DeepCode]     Is type definition: " + isTypeDefinition);
                System.out.println("[DeepCode]     Result: " + isProjectInternal);
            }

            return isProjectInternal;

        } catch (Exception e) {
            System.out.println("[DeepCode]     Error checking if variable is project internal: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取包含的类名（JSFunction版本）
     */
    private static String getContainingClassName(JSFunction function) {
        JSClass containingClass = PsiTreeUtil.getParentOfType(function, JSClass.class);
        return containingClass != null ? containingClass.getName() : null;
    }

    /**
     * 获取包含的类名（PsiElement版本）
     */
    private static String getContainingClassNameForElement(PsiElement element) {
        JSClass containingClass = PsiTreeUtil.getParentOfType(element, JSClass.class);
        return containingClass != null ? containingClass.getName() : null;
    }

    /**
     * 检查是否是参数调用
     */
    private static boolean isParameterCall(String objectRef, JSCallExpression callExpression) {
        JSFunction containingFunction = PsiTreeUtil.getParentOfType(callExpression, JSFunction.class);
        if (containingFunction != null) {
            JSParameterListElement[] parameters = containingFunction.getParameters();
            for (JSParameterListElement param : parameters) {
                if (param instanceof JSParameter jsParam) {
                    if (objectRef.equals(jsParam.getName())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 检查是否是全局对象调用
     */
    private static boolean isGlobalObjectCall(String objectRef) {
        // 常见的全局对象
        return "console".equals(objectRef) || "window".equals(objectRef) ||
               "document".equals(objectRef) || "Math".equals(objectRef) ||
               "JSON".equals(objectRef) || "Object".equals(objectRef) ||
               "Array".equals(objectRef) || "Promise".equals(objectRef);
    }

    /**
     * 从调用文本中提取方法名
     */
    private static String extractMethodName(String calleeText) {
        if (calleeText.contains(".")) {
            String[] parts = calleeText.split("\\.");
            return parts[parts.length - 1];
        }
        return calleeText;
    }

    /**
     * 检测语言代码
     */
    private static String detectLanguageCode(PsiElement element) {
        try {
            PsiFile containingFile = element.getContainingFile();
            if (containingFile instanceof JSFile jsFile) {
                return TypeScriptPsiUtils.detectLanguage(jsFile).getCode();
            }
            return Language.JAVASCRIPT.getCode();
        } catch (Exception e) {
            return Language.JAVASCRIPT.getCode();
        }
    }
}

