package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 模块路径解析器 - 处理相对路径解析和模块路径转换
 */
public final class ModulePathResolver {

    private static final Logger LOG = Logger.getInstance(ModulePathResolver.class);

    private ModulePathResolver() {
        // 工具类不允许实例化
    }

    /**
     * 解析模块路径，处理相对路径和别名
     */
    public static String resolveModulePath(String moduleSpecifier) {
        return resolveModulePath(moduleSpecifier, null);
    }

    /**
     * 解析模块路径，处理相对路径和别名
     */
    public static String resolveModulePath(String moduleSpecifier, PsiElement contextElement) {
        if (moduleSpecifier == null || moduleSpecifier.trim().isEmpty()) {
            return "unknown";
        }

        String module = moduleSpecifier.trim();

        // 1. 如果是相对路径，需要解析成绝对模块路径
        if (module.startsWith("./") || module.startsWith("../")) {
            return resolveRelativeModulePath(module, contextElement);
        }

        // 2. 如果是别名路径（如 @page/actForm/utils），保持原样
        if (module.startsWith("@")) {
            return module;
        }

        // 3. 如果是第三方模块（如 react），保持原样
        return module;
    }

    /**
     * 解析相对路径为绝对模块路径
     * 类似 Python 的相对导入处理方式
     */
    public static String resolveRelativeModulePath(String relativePath, PsiElement contextElement) {
        try {
            // 1. 获取当前文件的路径信息
            String currentFileAbsolutePath = null;
            String projectRootPath = null;

            if (contextElement != null) {
                PsiFile currentFile = contextElement.getContainingFile();
                if (currentFile != null && currentFile.getVirtualFile() != null) {
                    currentFileAbsolutePath = currentFile.getVirtualFile().getPath();
                }

                VirtualFile projectRoot = ImportUtils.getProjectRoot(contextElement);
                if (projectRoot != null) {
                    projectRootPath = projectRoot.getPath();
                }
            }

            // 2. 如果无法获取上下文，尝试清理相对路径
            if (currentFileAbsolutePath == null || projectRootPath == null) {
                return cleanRelativePath(relativePath);
            }

            // 3. 计算当前文件相对于项目根的路径
            String currentFileRelativePath = currentFileAbsolutePath.substring(projectRootPath.length());
            if (currentFileRelativePath.startsWith("/")) {
                currentFileRelativePath = currentFileRelativePath.substring(1);
            }

            // 4. 移除当前文件名，获取目录路径
            int lastSlash = currentFileRelativePath.lastIndexOf('/');
            String currentDirectory = "";
            if (lastSlash > 0) {
                currentDirectory = currentFileRelativePath.substring(0, lastSlash);
            }

            // 5. 解析相对路径
            String targetPath = resolveRelativePathSteps(currentDirectory, relativePath);

            // 6. 转换为文件路径格式
            String modulePath = pathToModulePath(targetPath);

            return modulePath;

        } catch (Exception e) {
            LOG.debug("Failed to resolve relative path: " + relativePath + ", error: " + e.getMessage());
            return cleanRelativePath(relativePath);
        }
    }

    /**
     * 解析相对路径步骤（处理 ../ 和 ./）
     */
    private static String resolveRelativePathSteps(String currentDirectory, String relativePath) {
        // 清理相对路径，移除文件扩展名（包括 CSS/样式文件）
        String cleanPath = relativePath;
        if (cleanPath.endsWith(".js") || cleanPath.endsWith(".ts") ||
            cleanPath.endsWith(".jsx") || cleanPath.endsWith(".tsx") ||
            cleanPath.endsWith(".css") || cleanPath.endsWith(".scss") ||
            cleanPath.endsWith(".sass") || cleanPath.endsWith(".less") ||
            cleanPath.endsWith(".styl") || cleanPath.endsWith(".stylus")) {
            int lastDot = cleanPath.lastIndexOf('.');
            if (lastDot > 0) {
                cleanPath = cleanPath.substring(0, lastDot);
            }
        }

        // 分割当前目录路径
        String[] currentParts = currentDirectory.isEmpty() ? new String[0] : currentDirectory.split("/");

        // 处理相对路径
        List<String> resultParts = new ArrayList<>();
        for (String part : currentParts) {
            if (!part.isEmpty()) {
                resultParts.add(part);
            }
        }

        // 处理相对路径步骤
        String[] relativeParts = cleanPath.split("/");
        for (String part : relativeParts) {
            if (part.equals("..")) {
                // 上级目录
                if (!resultParts.isEmpty()) {
                    resultParts.remove(resultParts.size() - 1);
                }
            } else if (!part.equals(".") && !part.isEmpty()) {
                // 当前目录或子目录
                resultParts.add(part);
            }
        }

        return String.join("/", resultParts);
    }

    /**
     * 将文件路径转换为模块路径格式（保持文件路径格式）
     */
    private static String pathToModulePath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "unknown";
        }

        // 保持文件路径格式，不再转换为点分隔
        String modulePath = filePath;

        // 移除开头的路径分隔符（如果有）
        if (modulePath.startsWith("/")) {
            modulePath = modulePath.substring(1);
        }

        return modulePath.isEmpty() ? "root" : modulePath;
    }

    /**
     * 清理相对路径，移除文件扩展名等（回退方法）
     */
    private static String cleanRelativePath(String relativePath) {
        String cleaned = relativePath;

        // 移除文件扩展名
        if (cleaned.endsWith(".js") || cleaned.endsWith(".ts") ||
            cleaned.endsWith(".jsx") || cleaned.endsWith(".tsx")) {
            int lastDot = cleaned.lastIndexOf('.');
            if (lastDot > 0) {
                cleaned = cleaned.substring(0, lastDot);
            }
        }

        // 移除 index 文件名（如果路径以 /index 结尾）
        if (cleaned.endsWith("/index")) {
            cleaned = cleaned.substring(0, cleaned.length() - 6);
        }

        // 确保路径以点开头（相对路径标识）
        if (!cleaned.startsWith("./") && !cleaned.startsWith("../")) {
            cleaned = "./" + cleaned;
        }

        return cleaned;
    }

    /**
     * 添加模块前缀到导入名称，生成完整的模块路径
     */
    public static String addModulePrefix(String importName, String moduleSpecifier, boolean isDefault) {
        return addModulePrefix(importName, moduleSpecifier, isDefault, null);
    }

    /**
     * 添加模块前缀到导入名称，生成完整的模块路径（带PSI上下文）
     */
    public static String addModulePrefix(String importName, String moduleSpecifier, boolean isDefault, PsiElement contextElement) {
        if (importName == null || importName.trim().isEmpty()) {
            return importName;
        }

        if (moduleSpecifier == null || moduleSpecifier.trim().isEmpty()) {
            return importName; // 没有模块说明符，返回原名称
        }

        // 解析模块路径，处理相对路径
        String resolvedModulePath = resolveModulePath(moduleSpecifier, contextElement);

        // 生成完整的模块路径（对于文件路径，使用斜杠连接）
        if (resolvedModulePath.contains("/") || moduleSpecifier.contains("/")) {
            // 文件路径格式，使用斜杠连接
            return resolvedModulePath + "/" + importName.trim();
        } else {
            // 模块包格式，使用点连接
            return resolvedModulePath + "." + importName.trim();
        }
    }
}

