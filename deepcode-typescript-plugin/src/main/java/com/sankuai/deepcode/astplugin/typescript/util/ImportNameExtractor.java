package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.javascript.psi.JSLiteralExpression;
import com.intellij.lang.javascript.psi.ecma6.TypeScriptImportStatement;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.util.PsiTreeUtil;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 导入名称提取器 - 从导入语句中提取导入的名称
 * <AUTHOR>
 */
public final class ImportNameExtractor {

    private static final Logger LOG = Logger.getInstance(ImportNameExtractor.class);

    private ImportNameExtractor() {
    }

    public static List<String> extractImportedNamesFromText(String importText, String moduleSpecifier, PsiElement contextElement) {
        List<String> names = new ArrayList<>();

        try {
            if (ImportUtils.isStaticResourceImport(importText)) {
                String cleanModuleSpecifier = ImportUtils.extractModuleSpecifierFromText(importText);
                if (cleanModuleSpecifier != null) {
                    // 对于静态资源文件的直接导入，直接使用解析后的模块路径
                    String resolvedModulePath = ModulePathResolver.resolveModulePath(cleanModuleSpecifier, contextElement);
                    names.add(resolvedModulePath);

                    // 使用 PSI 解析资源路径
                    String resolvedPath = ImportUtils.resolveResourcePathWithPSI(contextElement, cleanModuleSpecifier);
                    if (!resolvedPath.equals(cleanModuleSpecifier)) {
                        String resourceType = ImportUtils.isCssOrStyleImport(importText) ? "CSS/Style" : "Asset";
                        LOG.debug("Resolved " + resourceType + " path for import name: " + cleanModuleSpecifier + " -> " + resolvedPath);
                    }
                }
                return names;
            }

            if (importText.contains(" from ")) {
                String[] parts = importText.split("\\s+from\\s+");
                if (parts.length > 0) {
                    String importPart = parts[0].replace("import", "").replace("type", "").trim();

                    if (importPart.contains("{") && importPart.contains("}")) {
                        String beforeBrace = importPart.substring(0, importPart.indexOf("{")).trim();

                        if (!beforeBrace.isEmpty()) {
                            String defaultImport = beforeBrace.replaceAll(",$", "").trim();
                            if (!defaultImport.isEmpty()) {
                                String fullName = ModulePathResolver.addModulePrefix(defaultImport, moduleSpecifier, true, contextElement);
                                names.add(fullName);
                            }
                        }

                        int start = importPart.indexOf("{");
                        int end = importPart.indexOf("}");
                        if (start < end) {
                            String namedImports = importPart.substring(start + 1, end);
                            String[] imports = namedImports.split(",");
                            for (String imp : imports) {
                                String cleanName = imp.trim().split("\\s+as\\s+")[0].trim();
                                if (!cleanName.isEmpty()) {
                                    String fullName = ModulePathResolver.addModulePrefix(cleanName, moduleSpecifier, false, contextElement);
                                    names.add(fullName);
                                }
                            }
                        }
                    } else {
                        if (!importPart.isEmpty() && !importPart.equals("*")) {
                            String fullName = ModulePathResolver.addModulePrefix(importPart, moduleSpecifier, true, contextElement);
                            names.add(fullName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to parse import names from text: " + importText);
        }

        return names;
    }

    /**
     * 从 TypeScript 导入语句中提取导入名称 - 充分利用 PSI 语义分析
     */
    public static List<String> extractImportedNamesFromTypeScriptImport(PsiElement importStatement) {
        List<String> names = new ArrayList<>();

        try {
            if (!(importStatement instanceof TypeScriptImportStatement tsImport)) {
                LOG.debug("Element is not TypeScriptImportStatement, falling back to text analysis");
                String moduleSpecifier = ImportUtils.extractModuleSpecifierFromText(importStatement.getText());
                return extractImportedNamesFromText(importStatement.getText(), moduleSpecifier, importStatement);
            }

            // 获取模块路径
            String moduleSpecifier = extractModuleSpecifierFromPSI(tsImport);
            if (moduleSpecifier == null) {
                LOG.debug("Could not extract module specifier from PSI, falling back to text analysis");
                moduleSpecifier = ImportUtils.extractModuleSpecifierFromText(importStatement.getText());
            }

            if (moduleSpecifier == null) {
                LOG.warn("Could not extract module specifier from import statement");
                return names;
            }

            // 使用 PSI 分析导入的名称
            names = extractImportNamesFromPSI(tsImport, moduleSpecifier);

            // 如果 PSI 分析没有结果，回退到文本分析
            if (names.isEmpty()) {
                LOG.debug("PSI analysis returned no names, falling back to text analysis");
                names = extractImportedNamesFromText(importStatement.getText(), moduleSpecifier, importStatement);
            }

        } catch (Exception e) {
            LOG.debug("Failed to extract imported names from TypeScript import: " + e.getMessage());
            // 最后的回退
            String moduleSpecifier = ImportUtils.extractModuleSpecifierFromText(importStatement.getText());
            if (moduleSpecifier != null) {
                names = extractImportedNamesFromText(importStatement.getText(), moduleSpecifier, importStatement);
            }
        }

        return names;
    }

    /**
     * 从 TypeScript 导入语句的 PSI 结构中提取导入名称
     */
    private static List<String> extractImportNamesFromPSI(TypeScriptImportStatement importStatement, String moduleSpecifier) {
        List<String> names = new ArrayList<>();

        try {
            // 检查是否是静态资源文件导入（CSS/图片/字体等）
            if (ImportUtils.isStaticResourceImport(importStatement.getText())) {
                // 对于静态资源文件的直接导入，直接使用解析后的模块路径
                String resolvedModulePath = ModulePathResolver.resolveModulePath(moduleSpecifier, importStatement);
                names.add(resolvedModulePath);

                // 使用 PSI 解析资源路径
                String resolvedPath = ImportUtils.resolveResourcePathWithPSI(importStatement, moduleSpecifier);
                if (!resolvedPath.equals(moduleSpecifier)) {
                    String resourceType = ImportUtils.isCssOrStyleImport(importStatement.getText()) ? "CSS/Style" : "Asset";
                    LOG.debug("Resolved " + resourceType + " path from PSI: " + moduleSpecifier + " -> " + resolvedPath);
                }

                return names;
            }

            // 使用 PSI 分析导入结构
            LOG.debug("Analyzing import names using PSI");

            // 1. 默认导入 (import React from 'react')
            extractDefaultImportFromPSI(importStatement, moduleSpecifier, names, importStatement);

            // 2. 命名导入 (import { useState, useEffect } from 'react')
            extractNamedImportsFromPSI(importStatement, moduleSpecifier, names, importStatement);

            // 3. 命名空间导入 (import * as React from 'react')
            extractNamespaceImportFromPSI(importStatement, moduleSpecifier, names, importStatement);

        } catch (Exception e) {
            LOG.debug("Failed to extract import names from PSI: " + e.getMessage());
        }

        return names;
    }

    /**
     * 从 PSI 中提取默认导入
     */
    private static void extractDefaultImportFromPSI(TypeScriptImportStatement importStatement, String moduleSpecifier,
                                                   List<String> names, PsiElement contextElement) {
        try {
            String importText = importStatement.getText();

            // 对于默认导入，提取 import 和 from 之间的内容
            if (importText.contains(" from ")) {
                String[] parts = importText.split("\\s+from\\s+");
                if (parts.length >= 2) {
                    String importPart = parts[0].replace("import", "").replace("type", "").trim();

                    // 排除命名导入和命名空间导入
                    if (!importPart.startsWith("{") && !importPart.startsWith("*")) {
                        // 处理 "React," 这种情况（默认导入 + 命名导入）
                        String defaultName = importPart.replaceAll(",$", "").trim();
                        if (!defaultName.isEmpty() && !defaultName.contains("{") && !defaultName.contains("}")) {
                            String fullName = ModulePathResolver.addModulePrefix(defaultName, moduleSpecifier, true, contextElement);
                            names.add(fullName);
                            LOG.debug("Extracted default import: " + defaultName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to extract default import from PSI: " + e.getMessage());
        }
    }

    /**
     * 从 PSI 中提取命名导入
     */
    private static void extractNamedImportsFromPSI(TypeScriptImportStatement importStatement, String moduleSpecifier,
                                                  List<String> names, PsiElement contextElement) {
        try {
            String importText = importStatement.getText();

            // 查找命名导入 (import { ... } from ...)
            if (importText.contains("{") && importText.contains("}")) {
                int startBrace = importText.indexOf("{");
                int endBrace = importText.indexOf("}");

                if (startBrace < endBrace) {
                    String namedImports = importText.substring(startBrace + 1, endBrace);
                    String[] imports = namedImports.split(",");

                    for (String imp : imports) {
                        String cleanName = imp.trim();

                        // 处理 "useState as state" 这种情况
                        if (cleanName.contains(" as ")) {
                            String[] parts = cleanName.split("\\s+as\\s+");
                            if (parts.length >= 2) {
                                cleanName = parts[0].trim(); // 使用原始名称
                            }
                        }

                        if (!cleanName.isEmpty()) {
                            String fullName = ModulePathResolver.addModulePrefix(cleanName, moduleSpecifier, false, contextElement);
                            names.add(fullName);
                            LOG.debug("Extracted named import: " + cleanName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to extract named imports from PSI: " + e.getMessage());
        }
    }

    /**
     * 从 PSI 中提取命名空间导入
     */
    private static void extractNamespaceImportFromPSI(TypeScriptImportStatement importStatement, String moduleSpecifier,
                                                     List<String> names, PsiElement contextElement) {
        try {
            String importText = importStatement.getText();

            // 查找命名空间导入 (import * as name)
            if (importText.contains("*") && importText.contains(" as ")) {
                // 提取 import 和 from 之间的内容
                if (importText.contains(" from ")) {
                    String[] parts = importText.split("\\s+from\\s+");
                    if (parts.length >= 2) {
                        String importPart = parts[0].replace("import", "").replace("type", "").trim();

                        // 解析 "* as React" 格式
                        if (importPart.contains("* as ")) {
                            String[] asParts = importPart.split("\\s*\\*\\s*as\\s+");
                            if (asParts.length >= 2) {
                                String namespaceName = asParts[1].trim();
                                if (!namespaceName.isEmpty()) {
                                    String fullName = ModulePathResolver.addModulePrefix(namespaceName, moduleSpecifier, true, contextElement);
                                    names.add(fullName);
                                    LOG.debug("Extracted namespace import: " + namespaceName);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to extract namespace import from PSI: " + e.getMessage());
        }
    }

    /**
     * 从 PSI 中提取模块说明符
     */
    private static String extractModuleSpecifierFromPSI(TypeScriptImportStatement importStatement) {
        try {
            // 查找字符串字面量
            Collection<JSLiteralExpression> literals = PsiTreeUtil.findChildrenOfType(importStatement, JSLiteralExpression.class);

            for (JSLiteralExpression literal : literals) {
                if (literal.isStringLiteral()) {
                    String value = literal.getStringValue();
                    if (value != null && !value.isEmpty()) {
                        return value;
                    }
                }
            }

        } catch (Exception e) {
            LOG.debug("Failed to extract module specifier from PSI: " + e.getMessage());
        }

        return null;
    }
}

