package com.sankuai.deepcode.astplugin.typescript;

import com.intellij.openapi.diagnostic.Logger;
import com.sankuai.deepcode.astplugin.analyzer.AnalyzerService;

/**
 * TypeScript分析器服务
 * 负责管理TypeScript分析器的注册和生命周期
 *
 * <AUTHOR>
 */
public class TypeScriptAnalyzerService {

    private static final Logger LOG = Logger.getInstance(TypeScriptAnalyzerService.class);
    private boolean analyzerRegistered = false;

    /**
     * 确保分析器已注册
     */
    public void ensureAnalyzerRegistered() {
        if (!analyzerRegistered) {
            try {
                AnalyzerService analyzerService = AnalyzerService.getInstance();
                TypeScriptASTAnalyzer analyzer = new TypeScriptASTAnalyzer();
                analyzerService.registerAnalyzer(analyzer);
                analyzerRegistered = true;
                LOG.info("✅ TypeScript analyzer registered successfully");
            } catch (Exception e) {
                LOG.error("❌ Failed to register TypeScript analyzer", e);
            }
        }
    }

    /**
     * 检查分析器是否已注册
     */
    public boolean isAnalyzerRegistered() {
        return analyzerRegistered;
    }
}

