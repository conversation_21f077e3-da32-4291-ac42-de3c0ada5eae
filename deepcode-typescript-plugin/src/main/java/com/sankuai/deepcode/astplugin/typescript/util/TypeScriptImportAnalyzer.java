package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.ecmascript6.psi.ES6ImportDeclaration;
import com.intellij.lang.javascript.psi.*;
import com.intellij.lang.javascript.psi.ecma6.TypeScriptImportStatement;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

import java.util.Collection;
import java.util.List;

/**
 * TypeScript/JavaScript 导入分析器
 * 充分利用 PSI 的语义分析能力，基于实际存在的 PSI 类型
 *
 * <AUTHOR>
 */
public final class TypeScriptImportAnalyzer {

    private static final Logger LOG = Logger.getInstance(TypeScriptImportAnalyzer.class);

    private TypeScriptImportAnalyzer() {
        // 工具类不允许实例化
    }

    /**
     * 分析导入语句 - 直接使用 PSI 提供的导入接口
     */
    public static void analyzeImports(JSFile jsFile, AnalysisResult result) {
        String filePath = TypeScriptPsiUtils.getFilePath(jsFile);

        LOG.info("Starting import analysis for: " + jsFile.getName());

        try {
            // **主方法**：使用 PSI 提供的专门导入接口
            analyzeImportsUsingPSIInterfaces(jsFile, result, filePath);

            // **补充方法**：处理可能遗漏的特殊导入类型（如动态导入、require等）
            analyzeSupplementaryImports(jsFile, result, filePath);

            LOG.info("Import analysis completed. Found " + result.getImports().size() + " imports");

        } catch (Exception e) {
            LOG.warn("Failed to analyze imports: " + e.getMessage(), e);
            result.addError("Import analysis failed: " + e.getMessage());
        }
    }

    /**
     * 使用 PSI 提供的专门导入接口进行分析（主要方法）
     */
    private static void analyzeImportsUsingPSIInterfaces(JSFile jsFile, AnalysisResult result, String filePath) {
        try {
            // 1. 分析 TypeScript Import 语句
            analyzeTypeScriptImportsViaPSI(jsFile, result, filePath);

            // 2. 分析 ES6 Import 声明
            analyzeES6ImportsViaPSI(jsFile, result, filePath);

            // 3. 分析 CommonJS require 调用
            analyzeRequireCallsViaPSI(jsFile, result, filePath);

        } catch (Exception e) {
            LOG.warn("Failed to analyze imports using PSI interfaces", e);
            throw e;
        }
    }

    /**
     * 使用 PSI 专门接口分析 TypeScript 导入语句
     */
    private static void analyzeTypeScriptImportsViaPSI(JSFile jsFile, AnalysisResult result, String filePath) {
        try {
            Collection<TypeScriptImportStatement> importStatements =
                    PsiTreeUtil.findChildrenOfType(jsFile, TypeScriptImportStatement.class);

            for (TypeScriptImportStatement importStatement : importStatements) {
                analyzeTypeScriptImportStatement(importStatement, result, filePath);
            }

        } catch (Exception e) {
            LOG.warn("Failed to analyze TypeScript imports", e);
        }
    }

    /**
     * 使用 PSI 专门接口分析 ES6 导入声明
     */
    private static void analyzeES6ImportsViaPSI(JSFile jsFile, AnalysisResult result, String filePath) {
        try {
            Collection<ES6ImportDeclaration> importDeclarations =
                    PsiTreeUtil.findChildrenOfType(jsFile, ES6ImportDeclaration.class);

            for (ES6ImportDeclaration importDeclaration : importDeclarations) {
                ImportInfo importInfo = analyzeES6ImportDeclaration(importDeclaration, filePath);
                if (importInfo != null && !ImportUtils.isDuplicateImport(result, importInfo)) {
                    result.addImport(importInfo);
                }
            }

        } catch (Exception e) {
            LOG.warn("Failed to analyze ES6 imports", e);
        }
    }



    /**
     * 使用 PSI 专门接口分析 CommonJS require 调用
     */
    private static void analyzeRequireCallsViaPSI(JSFile jsFile, AnalysisResult result, String filePath) {
        try {
            Collection<JSCallExpression> callExpressions =
                    PsiTreeUtil.findChildrenOfType(jsFile, JSCallExpression.class);

            for (JSCallExpression callExpression : callExpressions) {
                if (isRequireCallPSI(callExpression)) {
                    analyzeRequireCallPSI(callExpression, result, filePath);
                }
            }

        } catch (Exception e) {
            LOG.warn("Failed to analyze require calls", e);
        }
    }

    /**
     * 分析补充的导入类型（动态导入等）
     */
    private static void analyzeSupplementaryImports(JSFile jsFile, AnalysisResult result, String filePath) {
        LOG.info("=== Analyzing Supplementary Import Types ===");

        try {
            // 1. 分析动态导入 import() 函数
            analyzeDynamicImports(jsFile, result, filePath);

            // 2. 分析 AMD require 调用
            analyzeAMDImports(jsFile, result, filePath);

            // 3. 检查是否有遗漏的特殊导入语法
            analyzeMiscellaneousImports(jsFile, result, filePath);

            LOG.info("Supplementary analysis completed");

        } catch (Exception e) {
            LOG.warn("Failed to analyze supplementary imports", e);
        }
    }

    /**
     * 分析动态导入 import() 函数调用
     */
    private static void analyzeDynamicImports(JSFile jsFile, AnalysisResult result, String filePath) {
        LOG.debug("--- Analyzing Dynamic Imports ---");

        try {
            Collection<JSCallExpression> callExpressions =
                    PsiTreeUtil.findChildrenOfType(jsFile, JSCallExpression.class);

            int dynamicImportCount = 0;
            for (JSCallExpression callExpression : callExpressions) {
                if (isDynamicImportCall(callExpression)) {
                    analyzeDynamicImportCall(callExpression, result, filePath);
                    dynamicImportCount++;
                }
            }

            if (dynamicImportCount > 0) {
                LOG.info("Found " + dynamicImportCount + " dynamic import() calls");
            }

        } catch (Exception e) {
            LOG.debug("Failed to analyze dynamic imports", e);
        }
    }

    /**
     * 分析 AMD require 调用
     */
    private static void analyzeAMDImports(JSFile jsFile, AnalysisResult result, String filePath) {
        LOG.debug("--- Analyzing AMD Imports ---");

        try {
            // AMD require 通常格式: require(['module1', 'module2'], function(mod1, mod2) {...})
            Collection<JSCallExpression> callExpressions =
                    PsiTreeUtil.findChildrenOfType(jsFile, JSCallExpression.class);

            int amdImportCount = 0;
            for (JSCallExpression callExpression : callExpressions) {
                if (isAMDRequireCall(callExpression)) {
                    analyzeAMDRequireCall(callExpression, result, filePath);
                    amdImportCount++;
                }
            }

            if (amdImportCount > 0) {
                LOG.info("Found " + amdImportCount + " AMD require calls");
            }

        } catch (Exception e) {
            LOG.debug("Failed to analyze AMD imports", e);
        }
    }

    /**
     * 分析其他杂项导入语法
     */
    private static void analyzeMiscellaneousImports(JSFile jsFile, AnalysisResult result, String filePath) {
        LOG.debug("--- Analyzing Miscellaneous Imports ---");

        try {
            // 这里可以添加对其他特殊导入语法的支持
            // 比如 System.import(), requirejs(), 等等

            // 目前暂不实现，保留扩展性
            LOG.debug("Miscellaneous import analysis completed (no special cases found)");

        } catch (Exception e) {
            LOG.debug("Failed to analyze miscellaneous imports", e);
        }
    }



    /**
     * 判断是否为动态导入 import() 调用
     */
    private static boolean isDynamicImportCall(JSCallExpression callExpression) {
        try {
            JSExpression methodExpression = callExpression.getMethodExpression();

            // 动态导入不是方法调用，而是 import 关键字的函数调用形式
            // 在 PSI 中可能表现为特殊的调用表达式
            return methodExpression != null &&
                    methodExpression.getText().equals("import");

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 分析动态导入调用
     */
    private static void analyzeDynamicImportCall(JSCallExpression callExpression, AnalysisResult result, String filePath) {
        try {
            JSExpression[] arguments = callExpression.getArguments();
            if (arguments.length > 0 && arguments[0] instanceof JSLiteralExpression literal) {
                if (literal.isStringLiteral()) {
                    String moduleSpecifier = literal.getStringValue();
                    if (moduleSpecifier != null) {
                        String importText = "import('" + moduleSpecifier + "')";
                        int lineNumber = TypeScriptPsiUtils.getLineNumber(callExpression);
                        boolean isExternal = ImportClassifier.isExternalModule(moduleSpecifier);

                        // 解析被导入的目标文件路径
                        String targetFilePath = resolveTypeScriptTargetFilePathFromText(moduleSpecifier, isExternal, importText);

                        ImportInfo importInfo = new ImportInfo(
                                importText, lineNumber, ImportInfo.ImportType.SINGLE, isExternal, List.of(moduleSpecifier), filePath, targetFilePath
                        );

                        if (!ImportUtils.isDuplicateImport(result, importInfo)) {
                            result.addImport(importInfo);
                            LOG.info("Added dynamic import: " + moduleSpecifier);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to analyze dynamic import call", e);
        }
    }

    /**
     * 判断是否为 AMD require 调用
     */
    private static boolean isAMDRequireCall(JSCallExpression callExpression) {
        try {
            JSExpression methodExpression = callExpression.getMethodExpression();

            if (methodExpression instanceof JSReferenceExpression reference) {
                if ("require".equals(reference.getReferenceName())) {
                    // 区分 AMD require 和 CommonJS require
                    // AMD require 第一个参数通常是数组
                    JSExpression[] arguments = callExpression.getArguments();
                    if (arguments.length >= 2 && arguments[0] instanceof JSArrayLiteralExpression) {
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 分析 AMD require 调用
     */
    private static void analyzeAMDRequireCall(JSCallExpression callExpression, AnalysisResult result, String filePath) {
        try {
            JSExpression[] arguments = callExpression.getArguments();
            if (arguments.length >= 1 && arguments[0] instanceof JSArrayLiteralExpression arrayLiteral) {

                JSExpression[] elements = arrayLiteral.getExpressions();
                for (JSExpression element : elements) {
                    if (element instanceof JSLiteralExpression literal && literal.isStringLiteral()) {
                        String moduleSpecifier = literal.getStringValue();
                        if (moduleSpecifier != null) {
                            String importText = "require(['" + moduleSpecifier + "'], ...)";
                            int lineNumber = TypeScriptPsiUtils.getLineNumber(callExpression);
                            boolean isExternal = ImportClassifier.isExternalModule(moduleSpecifier);

                            // 解析被导入的目标文件路径
                            String targetFilePath = resolveTypeScriptTargetFilePathFromText(moduleSpecifier, isExternal, importText);

                            ImportInfo importInfo = new ImportInfo(
                                    importText, lineNumber, ImportInfo.ImportType.SINGLE, isExternal, List.of(moduleSpecifier), filePath, targetFilePath
                            );

                            if (!ImportUtils.isDuplicateImport(result, importInfo)) {
                                result.addImport(importInfo);
                                LOG.info("Added AMD require: " + moduleSpecifier);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to analyze AMD require call", e);
        }
    }

    /**
     * 直接分析 ES6ImportDeclaration
     */
    private static ImportInfo analyzeES6ImportDeclaration(ES6ImportDeclaration importDeclaration, String filePath) {
        try {
            String importText = importDeclaration.getText();
            LOG.debug("Analyzing ES6 import declaration: " + importText);

            String moduleSpecifier = extractModuleSpecifierFromES6Import(importDeclaration);
            LOG.debug("Initial PSI extraction result: " + moduleSpecifier);

            if (moduleSpecifier == null) {
                LOG.debug("PSI extraction failed, trying text extraction");
                moduleSpecifier = ImportUtils.extractModuleSpecifierFromText(importText);
                LOG.debug("Text extraction result: " + moduleSpecifier);
            }

            if (moduleSpecifier == null) {
                LOG.warn("Could not extract module specifier from: " + importText);
                LOG.warn("Skipping import analysis due to failed module extraction");
                return null; // 如果无法提取模块，直接返回null而不是创建无效的ImportInfo
            }

            LOG.debug("Successfully extracted module specifier: " + moduleSpecifier);

            List<String> importedNames = ImportNameExtractor.extractImportedNamesFromText(importText, moduleSpecifier, importDeclaration);
            ImportInfo.ImportType type = determineImportTypeFromText(importText);
            // 使用 PSI 能力判断是否为外部模块（ES6 版本）
            boolean isExternal = ImportClassifier.isExternalModuleWithES6PSI(importDeclaration, moduleSpecifier);
            int lineNumber = TypeScriptPsiUtils.getLineNumber(importDeclaration);

            // 解析被导入的目标文件路径
            String targetFilePath = resolveTypeScriptTargetFilePath(importDeclaration, moduleSpecifier, isExternal);

            return new ImportInfo(importText, lineNumber, type, isExternal, importedNames, filePath, targetFilePath);

        } catch (Exception e) {
            LOG.warn("Failed to analyze ES6 import declaration directly", e);
            return null;
        }
    }

    /**
     * 从文本分析导入信息
     */
    private static ImportInfo analyzeImportFromText(String importText, int lineNumber, String filePath) {
        try {
            importText = importText.trim();

            // 提取模块路径
            String moduleSpecifier = ImportUtils.extractModuleSpecifierFromText(importText);
            if (moduleSpecifier == null) {
                LOG.debug("Could not extract module specifier from text: " + importText);
                return null;
            }

            // 提取导入名称
            List<String> importedNames = ImportNameExtractor.extractImportedNamesFromText(importText, moduleSpecifier, null);

            // 判断导入类型
            ImportInfo.ImportType type = determineImportTypeFromText(importText);

            // 判断是否为外部模块 - 使用PSI解析能力
            boolean isExternal;
            if (ImportUtils.isStaticResourceImport(importText)) {
                // 静态资源文件（CSS、图片等）：使用PSI解析绝对路径
                ImportUtils.ResourceResolutionResult resolutionResult =
                    ImportUtils.resolveResourceWithPSI(null, moduleSpecifier);

                if (resolutionResult.getAbsolutePath() != null) {
                    isExternal = !resolutionResult.isInternal();
                    String resourceType = ImportUtils.isCssOrStyleImport(importText) ? "CSS/Style" : "Asset";
                } else {
                    // 回退到简单的路径判断
                    isExternal = !(moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../") || moduleSpecifier.startsWith("@/"));
                    String resourceType = ImportUtils.isCssOrStyleImport(importText) ? "CSS/Style" : "Asset";
                }
            } else {
                isExternal = ImportClassifier.isExternalModule(moduleSpecifier);
            }

            // 解析被导入的目标文件路径
            String targetFilePath = resolveTypeScriptTargetFilePathFromText(moduleSpecifier, isExternal, importText);

            return new ImportInfo(importText, lineNumber, type, isExternal, importedNames, filePath, targetFilePath);

        } catch (Exception e) {
            LOG.warn("Failed to analyze import from text: " + importText, e);
            return null;
        }
    }

    /**
     * 分析单个 TypeScript 导入语句 - 充分利用 PSI 语义信息
     */
    private static void analyzeTypeScriptImportStatement(TypeScriptImportStatement importStatement, AnalysisResult result, String filePath) {
        try {
            String importText = importStatement.getText();
            LOG.info("Analyzing TypeScript import statement: " + importText);

            String moduleSpecifier = extractModuleSpecifierFromTypeScriptImport(importStatement);
            LOG.info("Extracted module specifier: " + moduleSpecifier);

            if (moduleSpecifier == null) {
                LOG.warn("No module specifier found for import: " + importText);
                return;
            }

            // 使用 PSI 获取导入信息
            List<String> importedNames = ImportNameExtractor.extractImportedNamesFromTypeScriptImport(importStatement);
            ImportInfo.ImportType type = determineImportTypeFromTypeScriptImport(importStatement);
            boolean isExternal = ImportClassifier.isExternalModuleWithPSI(importStatement, moduleSpecifier);
            int lineNumber = TypeScriptPsiUtils.getLineNumber(importStatement);

            // 解析被导入的目标文件路径
            String targetFilePath = resolveTypeScriptTargetFilePath(importStatement, moduleSpecifier, isExternal);

            ImportInfo importInfo = new ImportInfo(importText, lineNumber, type, isExternal, importedNames, filePath, targetFilePath);

            // 检查是否已存在相同的导入，避免重复
            if (!ImportUtils.isDuplicateImport(result, importInfo)) {
                result.addImport(importInfo);
                LOG.info("Successfully analyzed TypeScript import: " + moduleSpecifier + " -> " + importedNames + " (type: " + type + ", external: " + isExternal + ", line: " + lineNumber + ")");
            } else {
                LOG.info("Skipped duplicate TypeScript import: " + importText);
            }

        } catch (Exception e) {
            LOG.warn("Failed to analyze TypeScript import statement: " + importStatement.getText(), e);
        }
    }

    /**
     * 从 ES6 导入语句中提取模块路径 - 充分利用 PSI 语义分析
     */
    private static String extractModuleSpecifierFromES6Import(ES6ImportDeclaration importDeclaration) {
        try {
            String importText = importDeclaration.getText();
            LOG.debug("Extracting module specifier from ES6 import using PSI: " + importText);

            // 方法1：直接查找字符串字面量（最可靠的方法）
            Collection<JSLiteralExpression> literals = PsiTreeUtil.findChildrenOfType(importDeclaration, JSLiteralExpression.class);
            LOG.debug("Found " + literals.size() + " literal expressions in ES6 import");

            for (JSLiteralExpression literal : literals) {
                if (literal.isStringLiteral()) {
                    String moduleSpec = literal.getStringValue();
                    if (moduleSpec != null && !moduleSpec.isEmpty()) {
                        LOG.debug("Extracted module specifier via PSI string literal: " + moduleSpec);
                        return moduleSpec;
                    }
                }
            }

            // 方法2：如果 PSI 方法失败，回退到文本分析
            LOG.debug("PSI string literal extraction failed, falling back to text parsing for: " + importText);
            String textResult = ImportUtils.extractModuleSpecifierFromText(importText);
            if (textResult != null && !textResult.isEmpty()) {
                LOG.debug("Successfully extracted module specifier via text parsing: " + textResult);
                return textResult;
            }

            LOG.warn("Both PSI and text parsing failed to extract module specifier from: " + importText);
            return null;

        } catch (Exception e) {
            LOG.debug("Exception occurred while extracting module specifier, trying text parsing: " + e.getMessage());
            try {
                String fallbackResult = ImportUtils.extractModuleSpecifierFromText(importDeclaration.getText());
                if (fallbackResult != null) {
                    LOG.debug("Fallback text parsing successful: " + fallbackResult);
                }
                return fallbackResult;
            } catch (Exception e2) {
                LOG.warn("All extraction methods failed: " + e2.getMessage());
                return null;
            }
        }
    }

    /**
     * 从 TypeScript 导入语句中提取模块路径 - 充分利用 PSI 语义分析
     */
    private static String extractModuleSpecifierFromTypeScriptImport(TypeScriptImportStatement importStatement) {
        try {
            LOG.info("=== PSI-based Module Specifier Extraction ===");

            // 方法1：直接查找所有字符串字面量（最可靠的方法）
            Collection<JSLiteralExpression> literals = PsiTreeUtil.findChildrenOfType(importStatement, JSLiteralExpression.class);
            LOG.info("Found " + literals.size() + " literal expressions in import statement");

            for (JSLiteralExpression literal : literals) {
                if (literal.isStringLiteral()) {
                    String moduleSpec = literal.getStringValue();
                    if (moduleSpec != null && !moduleSpec.isEmpty()) {
                        LOG.info("Extracted module specifier via PSI string literal: " + moduleSpec);
                        return moduleSpec;
                    }
                }
            }

            // 方法2：分析导入语句的结构，寻找 "from" 关键字后的模块路径
            PsiElement[] children = importStatement.getChildren();
            for (PsiElement child : children) {
                // 查找包含 "from" 关键字的元素
                if (child.getText().contains("from")) {
                    Collection<JSLiteralExpression> fromLiterals = PsiTreeUtil.findChildrenOfType(child, JSLiteralExpression.class);
                    for (JSLiteralExpression literal : fromLiterals) {
                        if (literal.isStringLiteral()) {
                            String moduleSpec = literal.getStringValue();
                            if (moduleSpec != null && !moduleSpec.isEmpty()) {
                                LOG.info("Extracted module specifier via from clause analysis: " + moduleSpec);
                                return moduleSpec;
                            }
                        }
                    }
                }
            }

            // 方法3：检查 TypeScript 特定的模块引用
            if (importStatement.getExternalModuleReference() != null) {
                JSLiteralExpression moduleRef = PsiTreeUtil.findChildOfType(
                        importStatement.getExternalModuleReference(), JSLiteralExpression.class);
                if (moduleRef != null && moduleRef.isStringLiteral()) {
                    String moduleSpec = moduleRef.getStringValue();
                    if (moduleSpec != null && !moduleSpec.isEmpty()) {
                        LOG.info("Extracted module specifier via external reference: " + moduleSpec);
                        return moduleSpec;
                    }
                }
            }

            // 方法4：检查内部模块引用
            if (importStatement.getInternalModuleReference() != null) {
                JSLiteralExpression moduleRef = PsiTreeUtil.findChildOfType(
                        importStatement.getInternalModuleReference(), JSLiteralExpression.class);
                if (moduleRef != null && moduleRef.isStringLiteral()) {
                    String moduleSpec = moduleRef.getStringValue();
                    if (moduleSpec != null && !moduleSpec.isEmpty()) {
                        LOG.info("Extracted module specifier via internal reference: " + moduleSpec);
                        return moduleSpec;
                    }
                }
            }

            LOG.warn("All PSI methods failed to extract module specifier, no fallback to text parsing");
            return null;

        } catch (Exception e) {
            LOG.warn("Failed to extract module specifier from TypeScript import using PSI", e);
            return null;
        }
    }

    /**
     * 基于 TypeScript 导入语句判断导入类型 - 充分利用 PSI 语义分析
     */
    private static ImportInfo.ImportType determineImportTypeFromTypeScriptImport(TypeScriptImportStatement importStatement) {
        try {
            LOG.debug("Determining import type from TypeScript import PSI");

            // 检查是否是类型导入
            if (importStatement.isTypeImport()) {
                LOG.debug("Detected type import, using FROM_IMPORT type");
                return ImportInfo.ImportType.FROM_IMPORT;
            }

            // 使用 PSI 分析导入结构 - 采用更通用的方法
            String importText = importStatement.getText();

            // 检查是否是命名空间导入 (import * as name)
            if (importText.contains("import * as ") || importText.contains("import *")) {
                LOG.debug("Detected wildcard import via text analysis");
                return ImportInfo.ImportType.WILDCARD;
            }

            // 检查是否有命名导入 (import { ... })
            if (importText.contains("import {") && importText.contains("}")) {
                LOG.debug("Detected named imports via text analysis");
                return ImportInfo.ImportType.FROM_IMPORT;
            }

            // 如果没有命名导入，可能是默认导入
            LOG.debug("No named imports detected, assuming default import");
            return ImportInfo.ImportType.SINGLE;


        } catch (Exception e) {
            LOG.debug("Failed to determine import type from TypeScript import PSI, defaulting to SINGLE: " + e.getMessage());
            return ImportInfo.ImportType.SINGLE;
        }
    }

    /**
     * 使用 PSI 判断是否为 require 调用
     */
    private static boolean isRequireCallPSI(JSCallExpression callExpression) {
        try {
            JSExpression methodExpression = callExpression.getMethodExpression();

            if (methodExpression instanceof JSReferenceExpression reference) {
                return "require".equals(reference.getReferenceName());
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 分析 require 调用 - 使用 PSI
     */
    private static void analyzeRequireCallPSI(JSCallExpression callExpression, AnalysisResult result, String filePath) {
        try {
            JSExpression[] arguments = callExpression.getArguments();
            if (arguments.length == 0) {
                return;
            }

            JSExpression firstArg = arguments[0];
            if (!(firstArg instanceof JSLiteralExpression literal)) {
                return;
            }

            if (!literal.isStringLiteral()) {
                return;
            }

            String moduleSpecifier = literal.getStringValue();
            if (moduleSpecifier == null) {
                return;
            }

            String variableName = getRequireVariableNamePSI(callExpression);
            String importText = (variableName != null) ?
                    "const " + variableName + " = require('" + moduleSpecifier + "')" :
                    "require('" + moduleSpecifier + "')";

            int lineNumber = TypeScriptPsiUtils.getLineNumber(callExpression);
            // 对于 CommonJS require，也尝试使用 PSI 分析，但回退到基础分析
            boolean isExternal = isExternalModuleForRequire(callExpression, moduleSpecifier);
            List<String> resolvedModules = List.of(moduleSpecifier);

            // 解析被导入的目标文件路径
            String targetFilePath = resolveTypeScriptRequireTargetFilePath(callExpression, moduleSpecifier, isExternal);

            ImportInfo importInfo = new ImportInfo(
                    importText,
                    lineNumber,
                    ImportInfo.ImportType.SINGLE,
                    isExternal,
                    resolvedModules,
                    filePath,
                    targetFilePath
            );

            // 检查是否已存在相同的导入，避免重复
            if (!ImportUtils.isDuplicateImport(result, importInfo)) {
                result.addImport(importInfo);
                LOG.info("Successfully analyzed CommonJS require: " + moduleSpecifier + " (external: " + isExternal + ")");
            } else {
                LOG.info("Skipped duplicate require: " + importText);
            }

        } catch (Exception e) {
            LOG.warn("Failed to analyze require call: " + callExpression.getText(), e);
        }
    }

    /**
     * 为 CommonJS require 判断是否为外部模块
     * CommonJS require 没有 findReferencedElements 方法，所以回退到基础分析
     */
    private static boolean isExternalModuleForRequire(JSCallExpression callExpression, String moduleSpecifier) {
        try {
            // CommonJS require 调用通常没有复杂的引用解析
            // 直接使用基础的模块路径分析
            return ImportClassifier.isExternalModule(moduleSpecifier);
        } catch (Exception e) {
            LOG.debug("Failed to analyze require module: " + moduleSpecifier);
            return ImportClassifier.isExternalModule(moduleSpecifier);
        }
    }

    /**
     * 使用 PSI 获取 require 的变量名
     */
    private static String getRequireVariableNamePSI(JSCallExpression callExpression) {
        try {
            PsiElement parent = callExpression.getParent();

            if (parent instanceof JSVarStatement varStatement) {
                JSVariable[] variables = varStatement.getVariables();
                if (variables.length > 0) {
                    return variables[0].getName();
                }
            }

            if (parent instanceof JSAssignmentExpression assignment) {
                JSExpression lhs = assignment.getLOperand();
                return lhs.getText();
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    // ============ 辅助方法 ============

    /**
     * 从文本中解析导入类型
     */
    private static ImportInfo.ImportType determineImportTypeFromText(String importText) {
        // 检查是否是 CSS/样式文件导入
        if (ImportUtils.isCssOrStyleImport(importText)) {
            return ImportInfo.ImportType.SINGLE; // CSS 导入使用 SINGLE 类型，但会有特殊标记
        }

        if (importText.contains("import * as ") || importText.contains("import *")) {
            return ImportInfo.ImportType.WILDCARD;
        } else if (importText.contains("import {") && importText.contains("}")) {
            return ImportInfo.ImportType.FROM_IMPORT;
        } else {
            return ImportInfo.ImportType.SINGLE;
        }
    }

    /**
     * 调试方法：打印所有找到的导入语句（用于问题诊断）
     */
    public static void debugImports(JSFile jsFile) {
        LOG.info("=== DEBUG: Analyzing all import-related elements ===");

        // 1. 检查所有 TypeScript import 语句
        Collection<TypeScriptImportStatement> tsImports = PsiTreeUtil.findChildrenOfType(jsFile, TypeScriptImportStatement.class);
        LOG.info("Found " + tsImports.size() + " TypeScript import statements:");
        for (TypeScriptImportStatement imp : tsImports) {
            LOG.info("  TS Import: " + imp.getText());
        }

        // 2. 检查所有 JS 语句中的 import
        Collection<JSStatement> allStatements = PsiTreeUtil.findChildrenOfType(jsFile, JSStatement.class);
        LOG.info("Found " + allStatements.size() + " total JS statements, checking for imports:");
        int importCount = 0;
        for (JSStatement statement : allStatements) {
            String text = statement.getText();
            if (text != null && text.trim().startsWith("import")) {
                importCount++;
                LOG.info("  JS Import: " + text.trim() + " (Type: " + statement.getClass().getSimpleName() + ")");
            }
        }
        LOG.info("Found " + importCount + " JS import statements");

        // 3. 检查所有函数调用中的 require
        Collection<JSCallExpression> allCalls = PsiTreeUtil.findChildrenOfType(jsFile, JSCallExpression.class);
        LOG.info("Found " + allCalls.size() + " total call expressions, checking for require:");
        int requireCount = 0;
        for (JSCallExpression call : allCalls) {
            if (isRequireCallPSI(call)) {
                requireCount++;
                LOG.info("  Require call: " + call.getText());
            }
        }
        LOG.info("Found " + requireCount + " require calls");

        LOG.info("=== END DEBUG ===");
    }

    /**
     * 解析TypeScript导入语句的目标文件路径（完全基于 PSI）
     */
    private static String resolveTypeScriptTargetFilePath(PsiElement importElement, String moduleSpecifier, boolean isExternal) {
        String methodMsg = "=== resolveTypeScriptTargetFilePath() for: " + moduleSpecifier + " (isExternal: " + isExternal + ") ===";
        LOG.debug(methodMsg);

        try {
            // 方法1: 使用 PSI 的 findReferencedElements() 获取实际目标文件
            Collection<? extends PsiElement> referencedElements = null;

            if (importElement instanceof TypeScriptImportStatement) {
                TypeScriptImportStatement tsImport = (TypeScriptImportStatement) importElement;
                referencedElements = tsImport.findReferencedElements();
                String tsMsg = "TypeScript import found " + referencedElements.size() + " referenced elements";
                LOG.debug(tsMsg);
            } else if (importElement instanceof ES6ImportDeclaration) {
                // ES6ImportDeclaration 处理
                String es6Msg = "ES6 import detected, trying to get referenced elements...";
                LOG.debug(es6Msg);

                try {
                    ES6ImportDeclaration es6Import = (ES6ImportDeclaration) importElement;

                    // 方法1: 尝试直接获取引用元素（如果方法存在）
                    try {
                        // 使用反射尝试调用 findReferencedElements 方法
                        java.lang.reflect.Method method = es6Import.getClass().getMethod("findReferencedElements");
                        Object result = method.invoke(es6Import);
                        if (result instanceof Collection) {
                            referencedElements = (Collection<? extends PsiElement>) result;
                            String foundMsg = "ES6 import found " + referencedElements.size() + " referenced elements via reflection";
                            LOG.debug(foundMsg);
                        }
                    } catch (Exception reflectionException) {
                        String reflectionMsg = "ES6 findReferencedElements method not available: " + reflectionException.getMessage();
                        LOG.debug(reflectionMsg);
                    }

                    // 方法2: 如果反射失败，尝试通过 FromClause 获取模块引用
                    if (referencedElements == null || referencedElements.isEmpty()) {
                        String fromClauseMsg = "Trying to resolve via FromClause...";
                        LOG.debug(fromClauseMsg);

                        if (es6Import.getFromClause() != null) {
                            // 尝试获取 FromClause 的引用
                            PsiElement fromClause = es6Import.getFromClause();
                            String fromClauseInfo = "FromClause type: " + fromClause.getClass().getSimpleName();
                            LOG.debug(fromClauseInfo);

                            // 尝试通过 FromClause 解析模块
                            referencedElements = resolveES6FromClause(fromClause, moduleSpecifier);
                        } else {
                            String noFromClauseMsg = "ES6 import has no FromClause";
                            LOG.debug(noFromClauseMsg);
                        }
                    }

                    // 方法3: 如果前面的方法都失败，尝试直接通过 ES6ImportDeclaration 的其他方法
                    if (referencedElements == null || referencedElements.isEmpty()) {
                        String directResolveMsg = "Trying direct ES6 resolution methods...";
                        LOG.debug(directResolveMsg);

                        referencedElements = tryDirectES6Resolution(es6Import, moduleSpecifier);
                    }

                } catch (Exception e) {
                    String es6ErrorMsg = "Error processing ES6 import: " + e.getMessage();
                    LOG.debug(es6ErrorMsg);
                }
            }

            // 分析引用的元素，找到文件
            if (referencedElements != null && !referencedElements.isEmpty()) {
                for (PsiElement element : referencedElements) {
                    String elementMsg = "Analyzing referenced element: " + element.getClass().getSimpleName();
                    LOG.debug(elementMsg);

                    PsiFile containingFile = element.getContainingFile();
                    if (containingFile != null && containingFile.getVirtualFile() != null) {
                        com.intellij.openapi.vfs.VirtualFile virtualFile = containingFile.getVirtualFile();
                        String absolutePath = virtualFile.getPath();

                        String foundFileMsg = "Found target file: " + absolutePath;
                        LOG.debug(foundFileMsg);

                        // 尝试获取项目相对路径
                        String relativePath = getProjectRelativeFilePath(containingFile);
                        if (relativePath != null) {
                            String relativeMsg = "Converted to relative path: " + relativePath;
                            LOG.debug(relativeMsg);
                            return relativePath;
                        } else {
                            // 如果无法获取相对路径，返回绝对路径（通常是外部文件）
                            String absoluteMsg = "Using absolute path for external file: " + absolutePath;
                            LOG.debug(absoluteMsg);
                            return absolutePath;
                        }
                    }
                }
            }

            // 方法2: 如果 findReferencedElements() 失败，尝试使用增强的文件解析
            String fallbackMsg = "PSI reference resolution failed, trying enhanced file resolution...";
            LOG.debug(fallbackMsg);

            ImportUtils.ResourceResolutionResult resolutionResult =
                ImportUtils.resolveResourceWithPSI(importElement, moduleSpecifier);

            if (resolutionResult.getAbsolutePath() != null) {
                String enhancedMsg = "Enhanced resolution found: " + resolutionResult.getAbsolutePath();
                LOG.debug(enhancedMsg);

                if (resolutionResult.isInternal()) {
                    // 对于内部文件，尝试转换为相对路径
                    String absolutePath = resolutionResult.getAbsolutePath();
                    String relativePath = convertToProjectRelativePath(absolutePath, importElement);
                    return relativePath != null ? relativePath : absolutePath;
                } else {
                    // 返回绝对路径
                    return resolutionResult.getAbsolutePath();
                }
            }

            // 方法3: 如果所有 PSI 方法都失败，对于外部模块尝试文件系统查找
            if (isExternal) {
                String fileSystemMsg = "Trying file system resolution for external module...";
                LOG.debug(fileSystemMsg);

                String fileSystemPath = resolveExternalModuleByFileSystem(importElement, moduleSpecifier);
                if (fileSystemPath != null) {
                    String fileSystemFoundMsg = "File system resolution found: " + fileSystemPath;
                    LOG.debug(fileSystemFoundMsg);

                    // 转换为项目相对路径
                    String relativePath = convertToProjectRelativePath(fileSystemPath, importElement);
                    if (relativePath != null) {
                        String relativeMsg = "Converted to project relative path: " + relativePath;
                        LOG.debug(relativeMsg);
                        return relativePath;
                    } else {
                        // 如果转换失败，返回绝对路径作为回退
                        String pathFallbackMsg = "Could not convert to relative path, using absolute path";
                        LOG.debug(pathFallbackMsg);
                        return fileSystemPath;
                    }
                }
            }

            String noResolutionMsg = "Could not resolve target file path for module: " + moduleSpecifier;
            LOG.debug(noResolutionMsg);
            return null;

        } catch (Exception e) {
            String errorMsg = "Error resolving target file path for '" + moduleSpecifier + "': " + e.getMessage();
            LOG.debug(errorMsg, e);
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 通过文件系统解析外部模块
     */
    private static String resolveExternalModuleByFileSystem(PsiElement context, String moduleSpecifier) {
        String methodMsg = "=== resolveExternalModuleByFileSystem() for: " + moduleSpecifier + " ===";
        LOG.debug(methodMsg);

        try {
            // 获取项目根目录
            com.intellij.openapi.project.Project project = context.getProject();
            if (project == null) {
                String noProjectMsg = "No project found";
                LOG.debug(noProjectMsg);
                return null;
            }

            String projectBasePath = project.getBasePath();
            if (projectBasePath == null) {
                String noBasePathMsg = "No project base path found";
                LOG.debug(noBasePathMsg);
                return null;
            }

            String projectMsg = "Project base path: " + projectBasePath;
            LOG.debug(projectMsg);

            // 构建 node_modules 路径
            String nodeModulesPath = projectBasePath + "/node_modules";
            java.io.File nodeModulesDir = new java.io.File(nodeModulesPath);

            if (!nodeModulesDir.exists()) {
                String noNodeModulesMsg = "node_modules directory not found: " + nodeModulesPath;
                LOG.debug(noNodeModulesMsg);
                return null;
            }

            String nodeModulesMsg = "Found node_modules: " + nodeModulesPath;
            LOG.debug(nodeModulesMsg);

            // 解析模块路径
            String resolvedPath = resolveNodeModulePath(nodeModulesPath, moduleSpecifier);
            if (resolvedPath != null) {
                String resolvedMsg = "Resolved module path: " + resolvedPath;
                LOG.debug(resolvedMsg);
                return resolvedPath;
            }

            String notFoundMsg = "Could not resolve module in node_modules: " + moduleSpecifier;
            LOG.debug(notFoundMsg);
            return null;

        } catch (Exception e) {
            String errorMsg = "Error in file system resolution: " + e.getMessage();
            LOG.debug(errorMsg, e);
            return null;
        }
    }

    /**
     * 解析 node_modules 中的模块路径
     */
    private static String resolveNodeModulePath(String nodeModulesPath, String moduleSpecifier) {
        String methodMsg = "=== resolveNodeModulePath() for: " + moduleSpecifier + " ===";
        LOG.debug(methodMsg);

        try {
            // 处理 scoped 包 (如 @ant-design/icons)
            String modulePath;
            if (moduleSpecifier.startsWith("@")) {
                // Scoped package
                modulePath = nodeModulesPath + "/" + moduleSpecifier;
            } else {
                // Regular package
                modulePath = nodeModulesPath + "/" + moduleSpecifier;
            }

            String searchingMsg = "Searching for module at: " + modulePath;
            LOG.debug(searchingMsg);

            java.io.File moduleDir = new java.io.File(modulePath);
            if (!moduleDir.exists()) {
                String notExistMsg = "Module directory does not exist: " + modulePath;
                LOG.debug(notExistMsg);
                return null;
            }

            // 尝试查找入口文件
            String[] possibleEntries = {
                "index.d.ts",
                "index.ts",
                "index.js",
                "lib/index.d.ts",
                "lib/index.ts",
                "lib/index.js",
                "dist/index.d.ts",
                "dist/index.ts",
                "dist/index.js"
            };

            for (String entry : possibleEntries) {
                String entryPath = modulePath + "/" + entry;
                java.io.File entryFile = new java.io.File(entryPath);
                if (entryFile.exists()) {
                    String foundMsg = "Found entry file: " + entryPath;
                    LOG.debug(foundMsg);
                    return entryPath;
                }
            }

            // 尝试读取 package.json 获取入口文件
            String packageJsonPath = modulePath + "/package.json";
            java.io.File packageJsonFile = new java.io.File(packageJsonPath);
            if (packageJsonFile.exists()) {
                String packageJsonMsg = "Found package.json, trying to parse entry point";
                LOG.debug(packageJsonMsg);

                // 简单的 package.json 解析（查找 main 或 types 字段）
                String entryFromPackageJson = parsePackageJsonEntry(packageJsonPath, modulePath);
                if (entryFromPackageJson != null) {
                    return entryFromPackageJson;
                }
            }

            String noEntryMsg = "No entry file found for module: " + moduleSpecifier;
            LOG.debug(noEntryMsg);
            return null;

        } catch (Exception e) {
            String errorMsg = "Error resolving node module path: " + e.getMessage();
            LOG.debug(errorMsg, e);
            return null;
        }
    }

    /**
     * 解析 package.json 获取入口文件
     */
    private static String parsePackageJsonEntry(String packageJsonPath, String modulePath) {
        try {
            String content = new String(java.nio.file.Files.readAllBytes(java.nio.file.Paths.get(packageJsonPath)));

            // 简单的 JSON 解析（查找 types, typings, main 字段）
            String[] entryFields = {"\"types\":", "\"typings\":", "\"main\":"};

            for (String field : entryFields) {
                int fieldIndex = content.indexOf(field);
                if (fieldIndex != -1) {
                    int startQuote = content.indexOf("\"", fieldIndex + field.length());
                    if (startQuote != -1) {
                        int endQuote = content.indexOf("\"", startQuote + 1);
                        if (endQuote != -1) {
                            String entryFile = content.substring(startQuote + 1, endQuote);
                            String fullPath = modulePath + "/" + entryFile;
                            java.io.File file = new java.io.File(fullPath);
                            if (file.exists()) {
                                String foundEntryMsg = "Found entry from package.json: " + fullPath;
                                LOG.debug(foundEntryMsg);
                                return fullPath;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            String errorMsg = "Error parsing package.json: " + e.getMessage();
            LOG.debug(errorMsg);
        }
        return null;
    }

    /**
     * 尝试直接解析 ES6ImportDeclaration
     */
    private static Collection<? extends PsiElement> tryDirectES6Resolution(ES6ImportDeclaration es6Import, String moduleSpecifier) {
        String methodMsg = "=== tryDirectES6Resolution() for: " + moduleSpecifier + " ===";
        LOG.debug(methodMsg);

        try {
            // 方法1: 尝试通过 ES6ImportDeclaration 的 resolve 方法
            try {
                java.lang.reflect.Method resolveMethod = es6Import.getClass().getMethod("resolve");
                Object resolved = resolveMethod.invoke(es6Import);
                if (resolved instanceof PsiElement) {
                    PsiElement resolvedElement = (PsiElement) resolved;
                    String resolvedMsg = "ES6ImportDeclaration resolved to: " + resolvedElement.getClass().getSimpleName();
                    LOG.debug(resolvedMsg);
                    return java.util.Collections.singletonList(resolvedElement);
                }
            } catch (Exception resolveException) {
                String resolveErrorMsg = "ES6ImportDeclaration resolve method failed: " + resolveException.getMessage();
                LOG.debug(resolveErrorMsg);
            }

            // 方法2: 尝试通过 ES6ImportDeclaration 的 getReference 方法
            try {
                java.lang.reflect.Method getReferenceMethod = es6Import.getClass().getMethod("getReference");
                Object reference = getReferenceMethod.invoke(es6Import);
                if (reference != null) {
                    String referenceMsg = "ES6ImportDeclaration reference: " + reference.getClass().getSimpleName();
                    LOG.debug(referenceMsg);

                    // 尝试解析引用
                    try {
                        java.lang.reflect.Method refResolveMethod = reference.getClass().getMethod("resolve");
                        Object refResolved = refResolveMethod.invoke(reference);
                        if (refResolved instanceof PsiElement) {
                            PsiElement resolvedElement = (PsiElement) refResolved;
                            String refResolvedMsg = "ES6 Reference resolved to: " + resolvedElement.getClass().getSimpleName();
                            LOG.debug(refResolvedMsg);
                            return java.util.Collections.singletonList(resolvedElement);
                        }
                    } catch (Exception refResolveException) {
                        String refResolveErrorMsg = "ES6 Reference resolve failed: " + refResolveException.getMessage();
                        LOG.debug(refResolveErrorMsg);
                    }
                }
            } catch (Exception referenceException) {
                String referenceErrorMsg = "ES6ImportDeclaration getReference method failed: " + referenceException.getMessage();
                LOG.debug(referenceErrorMsg);
            }

            // 方法3: 尝试获取所有子元素并查找可解析的引用
            try {
                PsiElement[] children = es6Import.getChildren();
                String childrenMsg = "ES6ImportDeclaration has " + children.length + " children";
                LOG.debug(childrenMsg);

                for (PsiElement child : children) {
                    String childMsg = "Checking child: " + child.getClass().getSimpleName() + " - " + child.getText();
                    LOG.debug(childMsg);

                    // 如果子元素包含模块名，尝试解析它
                    if (child.getText().contains(moduleSpecifier)) {
                        try {
                            java.lang.reflect.Method childResolveMethod = child.getClass().getMethod("resolve");
                            Object childResolved = childResolveMethod.invoke(child);
                            if (childResolved instanceof PsiElement) {
                                PsiElement resolvedElement = (PsiElement) childResolved;
                                String childResolvedMsg = "Child resolved to: " + resolvedElement.getClass().getSimpleName();
                                LOG.debug(childResolvedMsg);
                                return java.util.Collections.singletonList(resolvedElement);
                            }
                        } catch (Exception childResolveException) {
                            // 忽略子元素解析失败
                        }
                    }
                }
            } catch (Exception childrenException) {
                String childrenErrorMsg = "Error checking ES6 children: " + childrenException.getMessage();
                LOG.debug(childrenErrorMsg);
            }

            String noDirectResolutionMsg = "Could not directly resolve ES6ImportDeclaration for: " + moduleSpecifier;
            LOG.debug(noDirectResolutionMsg);
            return java.util.Collections.emptyList();

        } catch (Exception e) {
            String errorMsg = "Error in tryDirectES6Resolution: " + e.getMessage();
            LOG.debug(errorMsg, e);
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 解析 ES6 FromClause 获取引用元素
     */
    private static Collection<? extends PsiElement> resolveES6FromClause(PsiElement fromClause, String moduleSpecifier) {
        String methodMsg = "=== resolveES6FromClause() for: " + moduleSpecifier + " ===";
        LOG.debug(methodMsg);

        try {
            // 方法1: 尝试通过 FromClause 的 resolve 方法
            try {
                java.lang.reflect.Method resolveMethod = fromClause.getClass().getMethod("resolve");
                Object resolved = resolveMethod.invoke(fromClause);
                if (resolved instanceof PsiElement) {
                    PsiElement resolvedElement = (PsiElement) resolved;
                    String resolvedMsg = "FromClause resolved to: " + resolvedElement.getClass().getSimpleName();
                    LOG.debug(resolvedMsg);

                    // 返回包含这个元素的集合
                    return java.util.Collections.singletonList(resolvedElement);
                }
            } catch (Exception resolveException) {
                String resolveErrorMsg = "FromClause resolve method failed: " + resolveException.getMessage();
                LOG.debug(resolveErrorMsg);
            }

            // 方法2: 尝试通过 FromClause 的 getReference 方法
            try {
                java.lang.reflect.Method getReferenceMethod = fromClause.getClass().getMethod("getReference");
                Object reference = getReferenceMethod.invoke(fromClause);
                if (reference != null) {
                    String referenceMsg = "FromClause reference: " + reference.getClass().getSimpleName();
                    LOG.debug(referenceMsg);

                    // 尝试解析引用
                    try {
                        java.lang.reflect.Method refResolveMethod = reference.getClass().getMethod("resolve");
                        Object refResolved = refResolveMethod.invoke(reference);
                        if (refResolved instanceof PsiElement) {
                            PsiElement resolvedElement = (PsiElement) refResolved;
                            String refResolvedMsg = "Reference resolved to: " + resolvedElement.getClass().getSimpleName();
                            LOG.debug(refResolvedMsg);

                            return java.util.Collections.singletonList(resolvedElement);
                        }
                    } catch (Exception refResolveException) {
                        String refResolveErrorMsg = "Reference resolve failed: " + refResolveException.getMessage();
                        LOG.debug(refResolveErrorMsg);
                    }
                }
            } catch (Exception referenceException) {
                String referenceErrorMsg = "FromClause getReference method failed: " + referenceException.getMessage();
                LOG.debug(referenceErrorMsg);
            }

            String noResolutionMsg = "Could not resolve ES6 FromClause for: " + moduleSpecifier;
            LOG.debug(noResolutionMsg);
            return java.util.Collections.emptyList();

        } catch (Exception e) {
            String errorMsg = "Error in resolveES6FromClause: " + e.getMessage();
            LOG.debug(errorMsg, e);
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 将绝对路径转换为项目相对路径
     */
    private static String convertToProjectRelativePath(String absolutePath, PsiElement context) {
        try {
            if (absolutePath == null || context == null) {
                return null;
            }

            // 获取项目根目录
            com.intellij.openapi.project.Project project = context.getProject();
            if (project == null) {
                return null;
            }

            String projectBasePath = project.getBasePath();
            if (projectBasePath == null) {
                return null;
            }

            // 如果文件在项目目录下，转换为相对路径
            if (absolutePath.startsWith(projectBasePath)) {
                String relativePath = absolutePath.substring(projectBasePath.length());
                if (relativePath.startsWith("/")) {
                    relativePath = relativePath.substring(1);
                }
                return relativePath;
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error converting to project relative path: " + e.getMessage());
            return null;
        }
    }

    /**
     * 增强的内部模块解析逻辑
     */
    private static String resolveInternalModuleWithEnhancedLogic(PsiElement context, String moduleSpecifier) {
        try {
            com.intellij.openapi.project.Project project = context.getProject();
            String projectBasePath = project.getBasePath();
            if (projectBasePath == null) {
                return null;
            }

            com.intellij.openapi.vfs.VirtualFile projectDir = com.intellij.openapi.vfs.LocalFileSystem.getInstance().findFileByPath(projectBasePath);
            if (projectDir == null) {
                return null;
            }

            // 1. 处理相对路径
            if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
                return resolveRelativeModulePath(context, moduleSpecifier, projectDir);
            }

            // 2. 处理各种别名模式
            if (moduleSpecifier.startsWith("@")) {
                return resolveAliasModulePath(moduleSpecifier, projectDir);
            }

            // 3. 处理绝对路径（从项目根开始）
            return resolveAbsoluteModulePath(moduleSpecifier, projectDir);

        } catch (Exception e) {
            LOG.debug("Error in enhanced internal module resolution: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析相对路径模块
     */
    private static String resolveRelativeModulePath(PsiElement context, String moduleSpecifier, com.intellij.openapi.vfs.VirtualFile projectDir) {
        try {
            PsiFile currentFile = context.getContainingFile();
            if (currentFile == null || currentFile.getVirtualFile() == null) {
                return null;
            }

            com.intellij.openapi.vfs.VirtualFile currentDir = currentFile.getVirtualFile().getParent();
            com.intellij.openapi.vfs.VirtualFile targetFile = resolveRelativePath(currentDir, moduleSpecifier);

            if (targetFile != null) {
                // 计算相对于项目根的路径
                String relativePath = com.intellij.openapi.vfs.VfsUtilCore.getRelativePath(targetFile, projectDir, '/');
                if (relativePath != null) {
                    LOG.debug("Resolved relative path '{}' to: {}", moduleSpecifier, relativePath);
                    return relativePath;
                }
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error resolving relative module path: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析别名路径模块
     */
    private static String resolveAliasModulePath(String moduleSpecifier, com.intellij.openapi.vfs.VirtualFile projectDir) {
        try {
            // 1. @/ 别名（指向 src 或项目根）
            if (moduleSpecifier.startsWith("@/")) {
                String aliasPath = moduleSpecifier.substring(2);

                // 优先在 src 目录查找
                com.intellij.openapi.vfs.VirtualFile srcDir = projectDir.findChild("src");
                if (srcDir != null) {
                    com.intellij.openapi.vfs.VirtualFile targetFile = findFileWithExtensions(srcDir, aliasPath);
                    if (targetFile != null) {
                        String relativePath = com.intellij.openapi.vfs.VfsUtilCore.getRelativePath(targetFile, projectDir, '/');
                        if (relativePath != null) {
                            LOG.debug("Resolved @/ alias '{}' to: {}", moduleSpecifier, relativePath);
                            return relativePath;
                        }
                    }
                }

                // 回退到项目根目录
                com.intellij.openapi.vfs.VirtualFile targetFile = findFileWithExtensions(projectDir, aliasPath);
                if (targetFile != null) {
                    String relativePath = com.intellij.openapi.vfs.VfsUtilCore.getRelativePath(targetFile, projectDir, '/');
                    if (relativePath != null) {
                        LOG.debug("Resolved @/ alias '{}' in project root to: {}", moduleSpecifier, relativePath);
                        return relativePath;
                    }
                }
            }

            // 2. 自定义别名（如 @page/, @components/ 等）
            if (moduleSpecifier.startsWith("@") && moduleSpecifier.contains("/")) {
                return resolveCustomAlias(moduleSpecifier, projectDir);
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error resolving alias module path: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析自定义别名
     */
    private static String resolveCustomAlias(String moduleSpecifier, com.intellij.openapi.vfs.VirtualFile projectDir) {
        try {
            int slashIndex = moduleSpecifier.indexOf("/");
            if (slashIndex <= 1) {
                return null;
            }

            String aliasPrefix = moduleSpecifier.substring(1, slashIndex); // 移除 @ 符号
            String remainingPath = moduleSpecifier.substring(slashIndex + 1);

            // 常见的源码目录
            String[] srcDirs = {"src", "static", "app", "lib", "source"};

            for (String srcDirName : srcDirs) {
                com.intellij.openapi.vfs.VirtualFile srcDir = projectDir.findChild(srcDirName);
                if (srcDir != null) {
                    // 直接查找别名目录
                    com.intellij.openapi.vfs.VirtualFile aliasDir = srcDir.findChild(aliasPrefix);
                    if (aliasDir != null) {
                        com.intellij.openapi.vfs.VirtualFile targetFile = findFileWithExtensions(aliasDir, remainingPath);
                        if (targetFile != null) {
                            String relativePath = com.intellij.openapi.vfs.VfsUtilCore.getRelativePath(targetFile, projectDir, '/');
                            if (relativePath != null) {
                                LOG.debug("Resolved custom alias '{}' to: {}", moduleSpecifier, relativePath);
                                return relativePath;
                            }
                        }
                    }
                }
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error resolving custom alias: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析绝对路径模块
     */
    private static String resolveAbsoluteModulePath(String moduleSpecifier, com.intellij.openapi.vfs.VirtualFile projectDir) {
        try {
            com.intellij.openapi.vfs.VirtualFile targetFile = findFileWithExtensions(projectDir, moduleSpecifier);
            if (targetFile != null) {
                String relativePath = com.intellij.openapi.vfs.VfsUtilCore.getRelativePath(targetFile, projectDir, '/');
                if (relativePath != null) {
                    LOG.debug("Resolved absolute path '{}' to: {}", moduleSpecifier, relativePath);
                    return relativePath;
                }
            }
            return null;
        } catch (Exception e) {
            LOG.debug("Error resolving absolute module path: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从文本解析TypeScript导入的目标文件路径
     */
    private static String resolveTypeScriptTargetFilePathFromText(String moduleSpecifier, boolean isExternal, String importText) {
        try {
            // 对于文本解析，我们无法使用PSI，只能基于模块说明符推断
            return inferFilePathFromModuleSpecifier(moduleSpecifier);

        } catch (Exception e) {
            LOG.debug("Error resolving TypeScript target file path from text: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析TypeScript require调用的目标文件路径
     */
    private static String resolveTypeScriptRequireTargetFilePath(JSCallExpression callExpression, String moduleSpecifier, boolean isExternal) {
        String methodMsg = "=== resolveTypeScriptRequireTargetFilePath() for: " + moduleSpecifier + " (isExternal: " + isExternal + ") ===";
        LOG.info(methodMsg);
        System.out.println("[CommonJS Debug] " + methodMsg);

        try {
            // 方法1: 尝试通过PSI解析模块引用获得实际目标文件
            LOG.info("[CommonJS Debug] 方法1: 开始 PSI 解析模块引用...");
            System.out.println("[CommonJS Debug] 方法1: 开始 PSI 解析模块引用...");

            PsiElement resolved = resolveRequireModule(callExpression, moduleSpecifier);
            if (resolved != null) {
                LOG.info("[CommonJS Debug] ✅ PSI 解析成功! 解析到: " + resolved.getClass().getSimpleName());
                System.out.println("[CommonJS Debug] ✅ PSI 解析成功! 解析到: " + resolved.getClass().getSimpleName());

                // 尝试获取文件路径
                String absolutePath = getFilePathFromPsiElement(resolved);
                if (absolutePath != null) {
                    LOG.info("[CommonJS Debug] ✅ 成功获取文件路径: " + absolutePath);
                    System.out.println("[CommonJS Debug] ✅ 成功获取文件路径: " + absolutePath);

                    // 对于外部模块，优先返回绝对路径（特别是 Node.js 内置模块和 node_modules 中的文件）
                    if (isExternal) {
                        LOG.info("[CommonJS Debug] 外部模块，使用绝对路径: " + absolutePath);
                        System.out.println("[CommonJS Debug] 外部模块，使用绝对路径: " + absolutePath);
                        return absolutePath;
                    } else {
                        // 对于内部模块，尝试获取项目相对路径
                        String relativePath = convertToProjectRelativePath(absolutePath, callExpression);
                        if (relativePath != null) {
                            LOG.info("[CommonJS Debug] 内部模块，转换为相对路径: " + relativePath);
                            System.out.println("[CommonJS Debug] 内部模块，转换为相对路径: " + relativePath);
                            return relativePath;
                        } else {
                            LOG.info("[CommonJS Debug] 内部模块，回退到绝对路径: " + absolutePath);
                            System.out.println("[CommonJS Debug] 内部模块，回退到绝对路径: " + absolutePath);
                            return absolutePath;
                        }
                    }
                } else {
                    LOG.info("[CommonJS Debug] ❌ 无法从 PSI 元素获取文件路径: " + resolved.getClass().getSimpleName());
                    System.out.println("[CommonJS Debug] ❌ 无法从 PSI 元素获取文件路径: " + resolved.getClass().getSimpleName());
                }
            } else {
                LOG.info("[CommonJS Debug] ❌ PSI 解析失败，resolved = null");
                System.out.println("[CommonJS Debug] ❌ PSI 解析失败，resolved = null");
            }

            // 方法2: 如果 PSI 解析失败，尝试使用增强的文件解析
            LOG.info("[CommonJS Debug] 方法2: 开始增强文件解析...");
            System.out.println("[CommonJS Debug] 方法2: 开始增强文件解析...");

            ImportUtils.ResourceResolutionResult resolutionResult =
                ImportUtils.resolveResourceWithPSI(callExpression, moduleSpecifier);

            if (resolutionResult.getAbsolutePath() != null) {
                LOG.info("[CommonJS Debug] ✅ 增强解析成功: " + resolutionResult.getAbsolutePath());
                System.out.println("[CommonJS Debug] ✅ 增强解析成功: " + resolutionResult.getAbsolutePath());

                if (resolutionResult.isInternal()) {
                    // 对于内部文件，尝试转换为相对路径
                    String absolutePath = resolutionResult.getAbsolutePath();
                    String relativePath = convertToProjectRelativePath(absolutePath, callExpression);
                    String result = relativePath != null ? relativePath : absolutePath;
                    LOG.info("[CommonJS Debug] 内部文件，返回路径: " + result);
                    System.out.println("[CommonJS Debug] 内部文件，返回路径: " + result);
                    return result;
                } else {
                    // 返回绝对路径
                    LOG.info("[CommonJS Debug] 外部文件，返回绝对路径: " + resolutionResult.getAbsolutePath());
                    System.out.println("[CommonJS Debug] 外部文件，返回绝对路径: " + resolutionResult.getAbsolutePath());
                    return resolutionResult.getAbsolutePath();
                }
            } else {
                LOG.info("[CommonJS Debug] ❌ 增强解析失败，未找到文件路径");
                System.out.println("[CommonJS Debug] ❌ 增强解析失败，未找到文件路径");
            }

            // 方法3: 如果所有 PSI 方法都失败，对于外部模块尝试文件系统查找
            if (isExternal) {
                LOG.info("[CommonJS Debug] 方法3: 开始文件系统查找（仅限外部模块）...");
                System.out.println("[CommonJS Debug] 方法3: 开始文件系统查找（仅限外部模块）...");

                String fileSystemPath = resolveExternalModuleByFileSystem(callExpression, moduleSpecifier);
                if (fileSystemPath != null) {
                    LOG.info("[CommonJS Debug] ✅ 文件系统查找成功: " + fileSystemPath);
                    System.out.println("[CommonJS Debug] ✅ 文件系统查找成功: " + fileSystemPath);

                    // 对于外部模块，直接返回绝对路径，不转换为相对路径
                    LOG.info("[CommonJS Debug] 外部模块，直接返回绝对路径: " + fileSystemPath);
                    System.out.println("[CommonJS Debug] 外部模块，直接返回绝对路径: " + fileSystemPath);
                    return fileSystemPath;
                } else {
                    LOG.info("[CommonJS Debug] ❌ 文件系统查找失败");
                    System.out.println("[CommonJS Debug] ❌ 文件系统查找失败");
                }
            } else {
                LOG.info("[CommonJS Debug] 跳过方法3（仅适用于外部模块）");
                System.out.println("[CommonJS Debug] 跳过方法3（仅适用于外部模块）");
            }

            // 方法4: 最后回退到路径推断（但对外部模块要小心）
            if (!isExternal) {
                LOG.info("[CommonJS Debug] 方法4: 开始路径推断（仅限内部模块）...");
                System.out.println("[CommonJS Debug] 方法4: 开始路径推断（仅限内部模块）...");

                // 只对内部模块使用路径推断
                String inferredPath = inferFilePathFromModuleSpecifier(moduleSpecifier);
                if (inferredPath != null) {
                    LOG.info("[CommonJS Debug] ✅ 路径推断成功: " + inferredPath);
                    System.out.println("[CommonJS Debug] ✅ 路径推断成功: " + inferredPath);
                    return inferredPath;
                } else {
                    LOG.info("[CommonJS Debug] ❌ 路径推断失败");
                    System.out.println("[CommonJS Debug] ❌ 路径推断失败");
                }
            } else {
                // 对于外部模块，如果所有方法都失败，返回 null 而不是错误的推断路径
                LOG.info("[CommonJS Debug] 跳过方法4（外部模块不使用路径推断）");
                System.out.println("[CommonJS Debug] 跳过方法4（外部模块不使用路径推断）");
                LOG.info("[CommonJS Debug] 外部模块 '" + moduleSpecifier + "' 无法解析，返回 null 以避免错误路径");
                System.out.println("[CommonJS Debug] 外部模块 '" + moduleSpecifier + "' 无法解析，返回 null 以避免错误路径");
            }

            LOG.info("[CommonJS Debug] ❌ 所有解析方法都失败，返回 null");
            System.out.println("[CommonJS Debug] ❌ 所有解析方法都失败，返回 null");
            return null;

        } catch (Exception e) {
            LOG.debug("Error resolving TypeScript require target file path for '{}': {}", moduleSpecifier, e.getMessage());
            return null;
        }
    }

    /**
     * 解析TypeScript模块
     */
    private static PsiElement resolveTypeScriptModule(TypeScriptImportStatement importStatement, String moduleSpecifier) {
        try {
            // 尝试使用TypeScript PSI的解析能力
            if (importStatement.getExternalModuleReference() != null) {
                try {
                    // 尝试调用resolve方法，如果不存在则会抛出异常
                    PsiElement resolved = (PsiElement) importStatement.getExternalModuleReference().getClass()
                            .getMethod("resolve").invoke(importStatement.getExternalModuleReference());
                    if (resolved != null) {
                        return resolved;
                    }
                } catch (Exception resolveException) {
                    LOG.debug("External module reference resolve method not available: {}", resolveException.getMessage());
                }
            }

            // 回退到通过模块查找
            return findModuleFile(importStatement, moduleSpecifier);
        } catch (Exception e) {
            LOG.debug("Error resolving TypeScript module: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析ES6模块
     */
    private static PsiElement resolveES6Module(ES6ImportDeclaration importDeclaration, String moduleSpecifier) {
        try {
            // 尝试使用ES6的模块解析
            return findModuleFile(importDeclaration, moduleSpecifier);
        } catch (Exception e) {
            LOG.debug("Error resolving ES6 module: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析require模块 - 充分利用PSI引用解析能力
     */
    private static PsiElement resolveRequireModule(JSCallExpression callExpression, String moduleSpecifier) {
        try {
            LOG.info("[CommonJS Debug] === resolveRequireModule() for: " + moduleSpecifier + " ===");
            System.out.println("[CommonJS Debug] === resolveRequireModule() for: " + moduleSpecifier + " ===");

            // 方法1: 尝试通过PSI引用解析（最准确的方法）
            LOG.info("[CommonJS Debug] 子方法1: 尝试 PSI 引用解析...");
            System.out.println("[CommonJS Debug] 子方法1: 尝试 PSI 引用解析...");
            PsiElement resolved = tryPsiReferenceResolution(callExpression, moduleSpecifier);
            if (resolved != null) {
                LOG.info("[CommonJS Debug] ✅ PSI 引用解析成功: " + resolved.getClass().getSimpleName());
                System.out.println("[CommonJS Debug] ✅ PSI 引用解析成功: " + resolved.getClass().getSimpleName());
                return resolved;
            } else {
                LOG.info("[CommonJS Debug] ❌ PSI 引用解析失败");
                System.out.println("[CommonJS Debug] ❌ PSI 引用解析失败");
            }

            // 方法2: 尝试通过require调用表达式的引用解析
            LOG.info("[CommonJS Debug] 子方法2: 尝试 require 调用表达式解析...");
            System.out.println("[CommonJS Debug] 子方法2: 尝试 require 调用表达式解析...");
            resolved = tryRequireCallResolution(callExpression);
            if (resolved != null) {
                LOG.info("[CommonJS Debug] ✅ require 调用表达式解析成功: " + resolved.getClass().getSimpleName());
                System.out.println("[CommonJS Debug] ✅ require 调用表达式解析成功: " + resolved.getClass().getSimpleName());
                return resolved;
            } else {
                LOG.info("[CommonJS Debug] ❌ require 调用表达式解析失败");
                System.out.println("[CommonJS Debug] ❌ require 调用表达式解析失败");
            }

            // 方法3: 回退到文件查找
            LOG.info("[CommonJS Debug] 子方法3: 尝试文件查找...");
            System.out.println("[CommonJS Debug] 子方法3: 尝试文件查找...");
            resolved = findModuleFile(callExpression, moduleSpecifier);
            if (resolved != null) {
                LOG.info("[CommonJS Debug] ✅ 文件查找成功: " + resolved.getClass().getSimpleName());
                System.out.println("[CommonJS Debug] ✅ 文件查找成功: " + resolved.getClass().getSimpleName());
                return resolved;
            } else {
                LOG.info("[CommonJS Debug] ❌ 文件查找失败");
                System.out.println("[CommonJS Debug] ❌ 文件查找失败");
            }

            LOG.info("[CommonJS Debug] ❌ resolveRequireModule 所有方法都失败: " + moduleSpecifier);
            System.out.println("[CommonJS Debug] ❌ resolveRequireModule 所有方法都失败: " + moduleSpecifier);
            return null;

        } catch (Exception e) {
            LOG.info("[CommonJS Debug] ❌ resolveRequireModule 异常: " + e.getMessage());
            System.out.println("[CommonJS Debug] ❌ resolveRequireModule 异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 查找模块文件
     */
    private static PsiElement findModuleFile(PsiElement context, String moduleSpecifier) {
        try {
            com.intellij.openapi.project.Project project = context.getProject();
            String projectBasePath = project.getBasePath();
            if (projectBasePath == null) {
                return null;
            }

            com.intellij.openapi.vfs.VirtualFile projectDir = com.intellij.openapi.vfs.LocalFileSystem.getInstance().findFileByPath(projectBasePath);
            if (projectDir == null) {
                return null;
            }

            // 处理相对路径
            if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
                PsiFile currentFile = context.getContainingFile();
                if (currentFile != null) {
                    com.intellij.openapi.vfs.VirtualFile currentDir = currentFile.getVirtualFile().getParent();
                    com.intellij.openapi.vfs.VirtualFile targetFile = resolveRelativePath(currentDir, moduleSpecifier);
                    if (targetFile != null) {
                        return com.intellij.psi.PsiManager.getInstance(project).findFile(targetFile);
                    }
                }
            } else if (moduleSpecifier.startsWith("@/")) {
                // 处理别名路径
                String relativePath = moduleSpecifier.substring(2);
                com.intellij.openapi.vfs.VirtualFile srcDir = projectDir.findChild("src");
                if (srcDir != null) {
                    com.intellij.openapi.vfs.VirtualFile targetFile = findTypeScriptFile(srcDir, relativePath);
                    if (targetFile != null) {
                        return com.intellij.psi.PsiManager.getInstance(project).findFile(targetFile);
                    }
                }
            } else {
                // 处理绝对路径
                com.intellij.openapi.vfs.VirtualFile targetFile = findTypeScriptFile(projectDir, moduleSpecifier);
                if (targetFile != null) {
                    return com.intellij.psi.PsiManager.getInstance(project).findFile(targetFile);
                }
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error finding module file: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 查找带扩展名的TypeScript文件
     */
    private static com.intellij.openapi.vfs.VirtualFile findFileWithExtensions(com.intellij.openapi.vfs.VirtualFile baseDir, String modulePath) {
        if (baseDir == null || modulePath == null) {
            return null;
        }

        // TypeScript/JavaScript 文件扩展名优先级
        String[] extensions = {".ts", ".tsx", ".js", ".jsx", ".vue", ".d.ts"};

        // 1. 直接查找带扩展名的文件
        for (String ext : extensions) {
            com.intellij.openapi.vfs.VirtualFile file = baseDir.findFileByRelativePath(modulePath + ext);
            if (file != null && file.exists()) {
                LOG.debug("Found file with extension: {}{}", modulePath, ext);
                return file;
            }
        }

        // 2. 查找目录下的index文件
        for (String ext : extensions) {
            com.intellij.openapi.vfs.VirtualFile indexFile = baseDir.findFileByRelativePath(modulePath + "/index" + ext);
            if (indexFile != null && indexFile.exists()) {
                LOG.debug("Found index file: {}/index{}", modulePath, ext);
                return indexFile;
            }
        }

        // 3. 如果模块路径已经有扩展名，直接查找
        if (modulePath.matches(".*\\.(ts|tsx|js|jsx|vue)$")) {
            com.intellij.openapi.vfs.VirtualFile file = baseDir.findFileByRelativePath(modulePath);
            if (file != null && file.exists()) {
                LOG.debug("Found file with existing extension: {}", modulePath);
                return file;
            }
        }

        return null;
    }

    /**
     * 查找TypeScript文件，尝试多种扩展名（保持向后兼容）
     */
    private static com.intellij.openapi.vfs.VirtualFile findTypeScriptFile(com.intellij.openapi.vfs.VirtualFile baseDir, String modulePath) {
        return findFileWithExtensions(baseDir, modulePath);
    }

    /**
     * 解析相对路径
     */
    private static com.intellij.openapi.vfs.VirtualFile resolveRelativePath(com.intellij.openapi.vfs.VirtualFile baseDir, String relativePath) {
        try {
            // 移除文件扩展名，因为TypeScript导入通常不包含扩展名
            String pathWithoutExt = relativePath.replaceAll("\\.(ts|tsx|js|jsx|vue)$", "");

            // 查找文件
            return findTypeScriptFile(baseDir, pathWithoutExt);
        } catch (Exception e) {
            LOG.debug("Error resolving relative path: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取文件的项目相对路径
     */
    private static String getProjectRelativeFilePath(PsiFile file) {
        try {
            com.intellij.openapi.project.Project project = file.getProject();
            com.intellij.openapi.vfs.VirtualFile virtualFile = file.getVirtualFile();

            if (virtualFile == null) {
                return null;
            }

            String projectBasePath = project.getBasePath();
            if (projectBasePath == null) {
                return null;
            }

            com.intellij.openapi.vfs.VirtualFile projectDir = com.intellij.openapi.vfs.LocalFileSystem.getInstance().findFileByPath(projectBasePath);
            if (projectDir == null) {
                return null;
            }

            // 使用 VfsUtilCore 获取相对路径
            String relativePath = com.intellij.openapi.vfs.VfsUtilCore.getRelativePath(virtualFile, projectDir, '/');

            if (relativePath != null) {
                LOG.debug("Resolved file path: {} -> {}", virtualFile.getPath(), relativePath);
                return relativePath;
            }

            // 如果无法计算相对路径，可能文件在项目外部
            LOG.debug("File is outside project directory: {}", virtualFile.getPath());
            return null;

        } catch (Exception e) {
            LOG.debug("Error getting project relative file path for '{}': {}", file.getName(), e.getMessage());
            return null;
        }
    }

    /**
     * 推断TypeScript文件路径（保持向后兼容）
     */
    private static String inferTypeScriptFilePath(PsiElement context, String moduleSpecifier) {
        return smartInferFilePath(context, moduleSpecifier);
    }

    /**
     * 获取文件的相对路径（保持向后兼容）
     */
    private static String getRelativeFilePath(PsiFile file) {
        return getProjectRelativeFilePath(file);
    }

    /**
     * 智能推断文件路径
     */
    private static String smartInferFilePath(PsiElement context, String moduleSpecifier) {
        try {
            PsiFile currentFile = context.getContainingFile();
            if (currentFile == null) {
                return inferFilePathFromModuleSpecifier(moduleSpecifier);
            }

            String currentFilePath = getProjectRelativeFilePath(currentFile);
            if (currentFilePath == null) {
                return inferFilePathFromModuleSpecifier(moduleSpecifier);
            }

            if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
                return inferRelativeFilePath(currentFilePath, moduleSpecifier);
            }

            return inferFilePathFromModuleSpecifier(moduleSpecifier);
        } catch (Exception e) {
            LOG.debug("Error in smart file path inference: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从模块说明符推断文件路径
     */
    private static String inferFilePathFromModuleSpecifier(String moduleSpecifier) {
        try {
            if (moduleSpecifier.startsWith("@/")) {
                String relativePath = moduleSpecifier.substring(2);
                return addAppropriateExtension(relativePath);
            }

            if (moduleSpecifier.startsWith("src/")) {
                return addAppropriateExtension(moduleSpecifier);
            }

            if (!moduleSpecifier.contains("/")) {
                return addAppropriateExtension("src/" + moduleSpecifier);
            }

            return addAppropriateExtension(moduleSpecifier);
        } catch (Exception e) {
            LOG.debug("Error inferring file path from module specifier '{}': {}", moduleSpecifier, e.getMessage());
            return null;
        }
    }

    /**
     * 推断相对路径
     */
    private static String inferRelativeFilePath(String currentFilePath, String relativeModuleSpecifier) {
        try {
            String currentDir = currentFilePath.substring(0, currentFilePath.lastIndexOf('/'));
            String[] currentParts = currentDir.split("/");
            String[] relativeParts = relativeModuleSpecifier.split("/");

            java.util.List<String> resultParts = new java.util.ArrayList<>(java.util.Arrays.asList(currentParts));

            for (String part : relativeParts) {
                if (part.equals("..")) {
                    if (!resultParts.isEmpty()) {
                        resultParts.remove(resultParts.size() - 1);
                    }
                } else if (!part.equals(".") && !part.isEmpty()) {
                    resultParts.add(part);
                }
            }

            String inferredPath = String.join("/", resultParts);
            return addAppropriateExtension(inferredPath);
        } catch (Exception e) {
            LOG.debug("Error inferring relative file path: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 添加合适的文件扩展名
     */
    private static String addAppropriateExtension(String filePath) {
        if (filePath.matches(".*\\.(ts|tsx|js|jsx|vue|d\\.ts)$")) {
            return filePath;
        }

        if (filePath.contains("component") || filePath.contains("Component")) {
            if (filePath.contains("vue") || filePath.endsWith("/index")) {
                return filePath + ".vue";
            } else {
                return filePath + ".tsx";
            }
        } else if (filePath.contains("type") || filePath.contains("interface") || filePath.contains("model")) {
            return filePath + ".ts";
        } else if (filePath.contains("test") || filePath.contains("spec")) {
            return filePath + ".ts";
        } else {
            return filePath + ".ts";
        }
    }

    /**
     * 查找源码目录
     */
    private static com.intellij.openapi.vfs.VirtualFile findSourceDirectory(com.intellij.openapi.vfs.VirtualFile projectDir) {
        String[] srcDirNames = {"src", "source", "app", "lib"};

        for (String dirName : srcDirNames) {
            com.intellij.openapi.vfs.VirtualFile srcDir = projectDir.findChild(dirName);
            if (srcDir != null && srcDir.isDirectory()) {
                return srcDir;
            }
        }

        return projectDir;
    }

    /**
     * 解析相对路径（支持扩展名处理）
     */
    private static com.intellij.openapi.vfs.VirtualFile resolveRelativePathWithExtensions(com.intellij.openapi.vfs.VirtualFile baseDir, String relativePath) {
        try {
            if (baseDir == null || relativePath == null) {
                return null;
            }

            String normalizedPath = relativePath.replace('\\', '/');
            String pathWithoutExt = normalizedPath.replaceAll("\\.(ts|tsx|js|jsx|vue|d\\.ts)$", "");

            if (pathWithoutExt.startsWith("./")) {
                pathWithoutExt = pathWithoutExt.substring(2);
            }

            com.intellij.openapi.vfs.VirtualFile resolvedDir = baseDir;
            String[] pathParts = pathWithoutExt.split("/");

            for (String part : pathParts) {
                if (part.equals("..")) {
                    resolvedDir = resolvedDir.getParent();
                    if (resolvedDir == null) {
                        return null;
                    }
                } else if (!part.equals(".") && !part.isEmpty()) {
                    if (part.equals(pathParts[pathParts.length - 1])) {
                        return findFileWithExtensions(resolvedDir, part);
                    } else {
                        resolvedDir = resolvedDir.findChild(part);
                        if (resolvedDir == null || !resolvedDir.isDirectory()) {
                            return null;
                        }
                    }
                }
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error resolving relative path '{}': {}", relativePath, e.getMessage());
            return null;
        }
    }

    /**
     * 尝试通过PSI引用解析require模块
     */
    private static PsiElement tryPsiReferenceResolution(JSCallExpression callExpression, String moduleSpecifier) {
        try {
            LOG.info("=== tryPsiReferenceResolution for: " + moduleSpecifier + " ===");

            // 获取require调用的第一个参数（模块说明符）
            JSExpression[] arguments = callExpression.getArguments();
            if (arguments.length == 0) {
                LOG.info("No arguments in require call");
                return null;
            }

            JSExpression firstArg = arguments[0];
            if (!(firstArg instanceof JSLiteralExpression)) {
                LOG.info("First argument is not a literal expression: " + firstArg.getClass().getSimpleName());
                return null;
            }

            JSLiteralExpression literal = (JSLiteralExpression) firstArg;
            if (!literal.isStringLiteral()) {
                LOG.info("Literal is not a string literal");
                return null;
            }

            // 尝试解析字符串字面量的引用
            com.intellij.psi.PsiReference[] references = literal.getReferences();
            LOG.info("Found " + references.length + " references for string literal: " + literal.getText());

            for (com.intellij.psi.PsiReference reference : references) {
                LOG.info("Processing reference: " + reference.getClass().getSimpleName());
                PsiElement resolved = reference.resolve();
                if (resolved != null) {
                    LOG.info("✅ String literal reference resolved to: " + resolved.getClass().getSimpleName());
                    if (resolved.getContainingFile() != null && resolved.getContainingFile().getVirtualFile() != null) {
                        String resolvedPath = resolved.getContainingFile().getVirtualFile().getPath();
                        LOG.info("✅ Resolved file path: " + resolvedPath);
                    }
                    return resolved;
                } else {
                    LOG.info("❌ Reference resolved to null");
                }
            }

            LOG.info("❌ No references resolved successfully");
            return null;

        } catch (Exception e) {
            LOG.info("❌ Error in PSI reference resolution: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 尝试通过require调用表达式本身的解析
     */
    private static PsiElement tryRequireCallResolution(JSCallExpression callExpression) {
        try {
            // 方法1: 尝试解析require调用表达式的引用
            com.intellij.psi.PsiReference[] references = callExpression.getReferences();
            for (com.intellij.psi.PsiReference reference : references) {
                PsiElement resolved = reference.resolve();
                if (resolved != null) {
                    LOG.debug("Require call reference resolved to: " + resolved.getClass().getSimpleName());
                    return resolved;
                }
            }

            // 方法2: 尝试解析方法表达式（require函数本身）
            JSExpression methodExpression = callExpression.getMethodExpression();
            if (methodExpression instanceof JSReferenceExpression) {
                JSReferenceExpression refExpr = (JSReferenceExpression) methodExpression;
                PsiElement resolved = refExpr.resolve();
                if (resolved != null) {
                    LOG.debug("Method expression resolved to: " + resolved.getClass().getSimpleName());
                    return resolved;
                }
            }

            return null;

        } catch (Exception e) {
            LOG.debug("Error in require call resolution: " + e.getMessage());
            return null;
        }
    }

    /**
     * 从 PSI 元素中获取文件路径
     */
    private static String getFilePathFromPsiElement(PsiElement element) {
        try {
            LOG.info("[CommonJS Debug] === getFilePathFromPsiElement() for: " + element.getClass().getSimpleName() + " ===");
            System.out.println("[CommonJS Debug] === getFilePathFromPsiElement() for: " + element.getClass().getSimpleName() + " ===");

            // 方法1: 如果是 PsiFile，直接获取路径
            if (element instanceof PsiFile) {
                PsiFile file = (PsiFile) element;
                if (file.getVirtualFile() != null) {
                    String path = file.getVirtualFile().getPath();
                    LOG.info("[CommonJS Debug] ✅ PsiFile 路径: " + path);
                    System.out.println("[CommonJS Debug] ✅ PsiFile 路径: " + path);
                    return path;
                }
            }

            // 方法2: 如果元素有包含文件，获取包含文件的路径
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null && containingFile.getVirtualFile() != null) {
                String path = containingFile.getVirtualFile().getPath();
                LOG.info("[CommonJS Debug] ✅ 包含文件路径: " + path);
                System.out.println("[CommonJS Debug] ✅ 包含文件路径: " + path);
                return path;
            }

            // 方法3: 尝试通过 getNavigationElement 获取
            PsiElement navigationElement = element.getNavigationElement();
            if (navigationElement != null && navigationElement != element) {
                LOG.info("[CommonJS Debug] 尝试通过 NavigationElement 获取路径...");
                System.out.println("[CommonJS Debug] 尝试通过 NavigationElement 获取路径...");
                return getFilePathFromPsiElement(navigationElement);
            }

            // 方法4: 如果是 TypeScript 模块，尝试特殊处理
            if (element.getClass().getSimpleName().contains("TypeScript") &&
                element.getClass().getSimpleName().contains("Module")) {
                LOG.info("[CommonJS Debug] 检测到 TypeScript 模块，尝试特殊处理...");
                System.out.println("[CommonJS Debug] 检测到 TypeScript 模块，尝试特殊处理...");

                // 尝试获取模块的源文件
                String moduleText = element.getText();
                if (moduleText != null && !moduleText.trim().isEmpty()) {
                    LOG.info("[CommonJS Debug] 模块文本: " + moduleText.substring(0, Math.min(100, moduleText.length())));
                    System.out.println("[CommonJS Debug] 模块文本: " + moduleText.substring(0, Math.min(100, moduleText.length())));
                }
            }

            LOG.info("[CommonJS Debug] ❌ 无法从 PSI 元素获取文件路径");
            System.out.println("[CommonJS Debug] ❌ 无法从 PSI 元素获取文件路径");
            return null;

        } catch (Exception e) {
            LOG.info("[CommonJS Debug] ❌ 获取文件路径时发生异常: " + e.getMessage());
            System.out.println("[CommonJS Debug] ❌ 获取文件路径时发生异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}