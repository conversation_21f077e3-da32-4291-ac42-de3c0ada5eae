package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.ecmascript6.psi.ES6ImportDeclaration;
import com.intellij.lang.javascript.psi.ecma6.TypeScriptImportStatement;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;

import java.util.Collection;

/**
 * 导入模块分类器 - 判断模块是内部还是外部
 * <AUTHOR>
 */
public final class ImportClassifier {

    private static final Logger LOG = Logger.getInstance(ImportClassifier.class);

    private ImportClassifier() {
        // 工具类不允许实例化
    }

    /**
     * 判断是否为外部模块
     */
    public static boolean isExternalModule(String moduleSpecifier) {
        if (moduleSpecifier == null || moduleSpecifier.isEmpty()) {
            return true;
        }

        // 1. 明显的相对路径导入（项目内部）- 包括所有静态资源文件
        if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
            String resourceType = ImportUtils.isStaticResourceImport("import '" + moduleSpecifier + "';") ?
                (ImportUtils.isCssOrStyleImport("import '" + moduleSpecifier + "';") ? "CSS/Style" : "Asset") : "Module";
            System.out.println("[DeepCode] " + resourceType + " '" + moduleSpecifier + "' is relative path, marking as INTERNAL");
            return false;
        }

        // 2. 项目别名路径（通常指向项目内部）
        if (moduleSpecifier.startsWith("@/")) {
            String resourceType = ImportUtils.isStaticResourceImport("import '" + moduleSpecifier + "';") ?
                (ImportUtils.isCssOrStyleImport("import '" + moduleSpecifier + "';") ? "CSS/Style" : "Asset") : "Module";
            System.out.println("[DeepCode] " + resourceType + " '" + moduleSpecifier + "' uses @/ alias, marking as INTERNAL");
            return false;
        }

        // 3. 自定义项目别名（如 @page/, @components/, @utils/ 等）
        if (moduleSpecifier.startsWith("@") && moduleSpecifier.contains("/") && !isNpmPackage(moduleSpecifier)) {
            String resourceType = ImportUtils.isStaticResourceImport("import '" + moduleSpecifier + "';") ?
                (ImportUtils.isCssOrStyleImport("import '" + moduleSpecifier + "';") ? "CSS/Style" : "Asset") : "Module";
            System.out.println("[DeepCode] " + resourceType + " '" + moduleSpecifier + "' uses custom project alias, marking as INTERNAL");
            return false;
        }

        // 3. Node.js 内置模块（外部）
        if (isNodeBuiltinModule(moduleSpecifier)) {
            System.out.println("[DeepCode] Module '" + moduleSpecifier + "' is Node.js builtin, marking as EXTERNAL");
            return true;
        }

        // 4. 其他所有情况都应该通过 PSI 的 findReferencedElements() 来判断
        // 这里只是回退方案，当 PSI 无法解析时保守地视为外部模块
        LOG.debug("Cannot determine module type for: " + moduleSpecifier + " without PSI analysis, defaulting to external");
        System.out.println("[DeepCode] Module '" + moduleSpecifier + "' cannot be determined, defaulting to EXTERNAL");
        return true;
    }

    /**
     * 判断是否为 npm 包（而非项目别名）
     */
    private static boolean isNpmPackage(String moduleSpecifier) {
        // 常见的 npm 包模式
        if (moduleSpecifier.startsWith("@") && moduleSpecifier.contains("/")) {
            String[] parts = moduleSpecifier.split("/");
            if (parts.length >= 2) {
                String scope = parts[0]; // @scope
                String packageName = parts[1]; // package-name

                // 常见的 npm scope 模式
                if (scope.matches("@[a-z0-9-]+") && packageName.matches("[a-z0-9-]+")) {
                    // 检查是否为常见的项目别名
                    String[] commonProjectAliases = {
                        "@page", "@components", "@utils", "@services", "@assets",
                        "@styles", "@views", "@layouts", "@hooks", "@store", "@api",
                        "@config", "@constants", "@types", "@interfaces", "@models"
                    };

                    for (String alias : commonProjectAliases) {
                        if (scope.equals(alias)) {
                            return false; // 是项目别名，不是 npm 包
                        }
                    }

                    // 如果不在常见项目别名列表中，可能是 npm 包
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 利用 PSI 能力判断导入是否为外部模块（增强版本）
     */
    public static boolean isExternalModuleWithPSI(TypeScriptImportStatement importStatement, String moduleSpecifier) {
        String msg = "=== PSI Module Analysis for: " + moduleSpecifier + " ===";
        LOG.info(msg);
        System.out.println("[DeepCode] " + msg);

        try {
            // 首先使用改进的PSI文件解析方法
            ImportUtils.ResourceResolutionResult resolutionResult =
                ImportUtils.resolveResourceWithPSI(importStatement, moduleSpecifier);

            if (resolutionResult.getAbsolutePath() != null) {
                String psiMsg = "PSI resolved file path: " + resolutionResult.getAbsolutePath() +
                              " (internal: " + resolutionResult.isInternal() + ")";
                LOG.info(psiMsg);
                System.out.println("[DeepCode] " + psiMsg);

                String conclusionMsg = "CONCLUSION: TypeScript Module " + moduleSpecifier + " is " +
                                     (resolutionResult.isInternal() ? "INTERNAL" : "EXTERNAL") + " (PSI file resolution)";
                LOG.info(conclusionMsg);
                System.out.println("[DeepCode] " + conclusionMsg);

                return !resolutionResult.isInternal();
            }

            // 如果PSI文件解析失败，回退到原有的引用元素分析
            Collection<? extends PsiElement> referencedElements = importStatement.findReferencedElements();
            String elementsMsg = "PSI found " + referencedElements.size() + " referenced elements for module: " + moduleSpecifier;
            LOG.info(elementsMsg);
            System.out.println("[DeepCode] " + elementsMsg);

            // 如果找到了引用的元素，检查它们的文件位置
            if (!referencedElements.isEmpty()) {
                for (PsiElement element : referencedElements) {
                    LOG.info("Checking referenced element: " + element.getClass().getSimpleName());

                    // 获取元素所在的文件路径
                    PsiFile containingFile = element.getContainingFile();
                    if (containingFile != null && containingFile.getVirtualFile() != null) {
                        String filePath = containingFile.getVirtualFile().getPath();
                        String filePathMsg = "Referenced element file path: " + filePath;
                        LOG.info(filePathMsg);
                        System.out.println("[DeepCode] " + filePathMsg);
                    }

                    // 检查引用的文件是否在项目内部
                    boolean isInternal = isElementInProject(element);
                    String internalMsg = "Element " + element.getClass().getSimpleName() + " is internal: " + isInternal;
                    LOG.info(internalMsg);
                    System.out.println("[DeepCode] " + internalMsg);

                    if (isInternal) {
                        String conclusionMsg = "CONCLUSION: Module " + moduleSpecifier + " is INTERNAL (found project element)";
                        LOG.info(conclusionMsg);
                        System.out.println("[DeepCode] " + conclusionMsg);
                        return false; // 项目内部
                    }
                }

                // 如果找到引用元素但都不在项目内，则是外部模块
                LOG.info("CONCLUSION: Module " + moduleSpecifier + " is EXTERNAL (all elements are external)");
                return true;
            }

            // 如果没有找到引用元素，尝试其他方法
            LOG.info("PSI found no referenced elements, trying alternative analysis");

            // 尝试通过模块解析器获取文件位置
            boolean alternativeResult = analyzeModuleWithAlternativeMethod(importStatement, moduleSpecifier);
            if (alternativeResult != true) { // 不是明确的外部模块
                return alternativeResult;
            }

            // 如果所有方法都失败，回退到基于路径的判断
            LOG.info("All PSI methods failed, falling back to path-based analysis");
            boolean pathBasedResult = isExternalModule(moduleSpecifier);
            LOG.info("CONCLUSION: Module " + moduleSpecifier + " is " + (pathBasedResult ? "EXTERNAL" : "INTERNAL") + " (path-based)");
            return pathBasedResult;

        } catch (Exception e) {
            LOG.warn("Failed to use PSI for module resolution, falling back to path-based analysis", e);
            boolean pathBasedResult = isExternalModule(moduleSpecifier);
            LOG.info("CONCLUSION: Module " + moduleSpecifier + " is " + (pathBasedResult ? "EXTERNAL" : "INTERNAL") + " (fallback)");
            return pathBasedResult;
        }
    }

    /**
     * 使用 PSI 判断是否为外部模块（ES6 版本）
     */
    public static boolean isExternalModuleWithES6PSI(ES6ImportDeclaration importDeclaration, String moduleSpecifier) {
        String msg = "=== ES6 PSI Module Analysis for: " + moduleSpecifier + " ===";
        LOG.info(msg);
        System.out.println("[DeepCode] " + msg);

        try {
            // 首先使用改进的PSI文件解析方法
            ImportUtils.ResourceResolutionResult resolutionResult =
                ImportUtils.resolveResourceWithPSI(importDeclaration, moduleSpecifier);

            if (resolutionResult.getAbsolutePath() != null) {
                String psiMsg = "PSI resolved file path: " + resolutionResult.getAbsolutePath() +
                              " (internal: " + resolutionResult.isInternal() + ")";
                LOG.info(psiMsg);
                System.out.println("[DeepCode] " + psiMsg);

                String conclusionMsg = "CONCLUSION: ES6 Module " + moduleSpecifier + " is " +
                                     (resolutionResult.isInternal() ? "INTERNAL" : "EXTERNAL") + " (PSI file resolution)";
                LOG.info(conclusionMsg);
                System.out.println("[DeepCode] " + conclusionMsg);

                return !resolutionResult.isInternal();
            }

            String noMethodMsg = "PSI file resolution failed, trying path-based analysis";
            LOG.info(noMethodMsg);
            System.out.println("[DeepCode] " + noMethodMsg);

            // 尝试获取模块来源信息
            PsiFile currentFile = importDeclaration.getContainingFile();
            if (currentFile != null && currentFile.getVirtualFile() != null) {
                String currentFilePath = currentFile.getVirtualFile().getPath();
                String filePathMsg = "Current file path: " + currentFilePath;
                LOG.info(filePathMsg);
                System.out.println("[DeepCode] " + filePathMsg);

                // 获取项目根目录
                VirtualFile projectRoot = ImportUtils.getProjectRoot(importDeclaration);
                if (projectRoot != null) {
                    String projectRootPath = projectRoot.getPath();
                    String rootMsg = "Project root path: " + projectRootPath;
                    LOG.info(rootMsg);
                    System.out.println("[DeepCode] " + rootMsg);

                    // 尝试基于模块路径和当前文件位置进行推测分析
                    boolean result = analyzeES6ModuleByPath(importDeclaration, moduleSpecifier, currentFilePath, projectRootPath);
                    String conclusionMsg = "CONCLUSION: ES6 Module " + moduleSpecifier + " is " + (result ? "EXTERNAL" : "INTERNAL") + " (path-based PSI analysis)";
                    LOG.info(conclusionMsg);
                    System.out.println("[DeepCode] " + conclusionMsg);
                    return result;
                }
            }

            // 如果无法通过 PSI 分析，回退到基础分析
            String fallbackMsg = "ES6 PSI analysis failed, falling back to basic path analysis";
            LOG.info(fallbackMsg);
            System.out.println("[DeepCode] " + fallbackMsg);

            boolean pathBasedResult = isExternalModule(moduleSpecifier);
            String conclusionMsg = "CONCLUSION: ES6 Module " + moduleSpecifier + " is " + (pathBasedResult ? "EXTERNAL" : "INTERNAL") + " (fallback)";
            LOG.info(conclusionMsg);
            System.out.println("[DeepCode] " + conclusionMsg);
            return pathBasedResult;

        } catch (Exception e) {
            LOG.warn("Failed to use ES6 PSI for module resolution, falling back to path-based analysis", e);
            boolean pathBasedResult = isExternalModule(moduleSpecifier);
            String conclusionMsg = "CONCLUSION: ES6 Module " + moduleSpecifier + " is " + (pathBasedResult ? "EXTERNAL" : "INTERNAL") + " (exception fallback)";
            LOG.info(conclusionMsg);
            System.out.println("[DeepCode] " + conclusionMsg);
            return pathBasedResult;
        }
    }

    /**
     * 判断是否为 Node.js 内置模块
     */
    private static boolean isNodeBuiltinModule(String moduleSpecifier) {
        // Node.js 内置模块列表（这些是 Node.js 标准库，不是第三方包）
        String[] builtinModules = {
            "assert", "buffer", "child_process", "cluster", "crypto", "dgram", "dns",
            "events", "fs", "http", "https", "net", "os", "path", "querystring",
            "readline", "stream", "string_decoder", "timers", "tls", "tty", "url",
            "util", "vm", "zlib", "constants", "domain", "module", "perf_hooks",
            "process", "punycode", "repl", "sys", "v8", "worker_threads"
        };

        // 也可能以 node: 前缀形式出现（Node.js 12+ 支持）
        if (moduleSpecifier.startsWith("node:")) {
            String moduleName = moduleSpecifier.substring(5);
            for (String builtin : builtinModules) {
                if (builtin.equals(moduleName)) {
                    return true;
                }
            }
        }

        // 直接检查模块名
        for (String builtin : builtinModules) {
            if (builtin.equals(moduleSpecifier)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 使用替代方法分析模块
     */
    private static boolean analyzeModuleWithAlternativeMethod(TypeScriptImportStatement importStatement, String moduleSpecifier) {
        try {
            // 获取导入语句所在的文件
            PsiFile currentFile = importStatement.getContainingFile();
            if (currentFile == null || currentFile.getVirtualFile() == null) {
                return true; // 无法确定，默认外部
            }

            String currentFilePath = currentFile.getVirtualFile().getPath();
            LOG.info("Current file path: " + currentFilePath);

            // 获取项目根目录
            VirtualFile projectRoot = ImportUtils.getProjectRoot(importStatement);
            if (projectRoot == null) {
                return true; // 无法确定项目根目录，默认外部
            }

            String projectRootPath = projectRoot.getPath();
            LOG.info("Project root path: " + projectRootPath);

            return true; // 默认外部

        } catch (Exception e) {
            LOG.debug("Alternative analysis failed: " + e.getMessage());
            return true;
        }
    }

    /**
     * 基于路径分析 ES6 模块
     */
    private static boolean analyzeES6ModuleByPath(ES6ImportDeclaration importDeclaration, String moduleSpecifier, String currentFilePath, String projectRootPath) {
        try {
            String analysisMsg = "Analyzing ES6 module by path: " + moduleSpecifier;
            LOG.info(analysisMsg);
            System.out.println("[DeepCode] " + analysisMsg);

            // 如果是相对路径，肯定是项目内部（包括所有静态资源文件）
            if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
                String pathMsg = "Relative import detected: " + moduleSpecifier + " (including all static resources)";
                LOG.info(pathMsg);
                System.out.println("[DeepCode] " + pathMsg);

                // 特别处理静态资源文件
                if (ImportUtils.isStaticResourceImport("import '" + moduleSpecifier + "';")) {
                    String resourceType = ImportUtils.isCssOrStyleImport("import '" + moduleSpecifier + "';") ? "CSS/Style" : "Asset";
                    String resourceMsg = resourceType + " file import detected: " + moduleSpecifier;
                    LOG.info(resourceMsg);
                    System.out.println("[DeepCode] " + resourceMsg);
                }

                // 相对路径导入，肯定是项目内部
                return false;
            }

            // 如果是项目别名路径，通常也是项目内部
            if (moduleSpecifier.startsWith("@/")) {
                String aliasMsg = "Project alias import detected: " + moduleSpecifier;
                LOG.info(aliasMsg);
                System.out.println("[DeepCode] " + aliasMsg);

                // 特别处理静态资源文件
                if (ImportUtils.isStaticResourceImport("import '" + moduleSpecifier + "';")) {
                    String resourceType = ImportUtils.isCssOrStyleImport("import '" + moduleSpecifier + "';") ? "CSS/Style" : "Asset";
                    String resourceMsg = resourceType + " file with alias detected: " + moduleSpecifier;
                    LOG.info(resourceMsg);
                    System.out.println("[DeepCode] " + resourceMsg);
                }

                return false;
            }

            // 对于绝对路径或别名，检查是否可能指向项目内部
            return true; // 默认外部

        } catch (Exception e) {
            LOG.debug("Failed to analyze ES6 module by path: " + e.getMessage());
            return true; // 默认外部
        }
    }

    /**
     * 检查 PSI 元素是否在项目内部
     */
    private static boolean isElementInProject(PsiElement element) {
        try {
            if (element == null) return false;

            // 获取元素所在的文件
            PsiFile containingFile = element.getContainingFile();
            if (containingFile == null) return false;

            // 获取文件的虚拟文件
            VirtualFile virtualFile = containingFile.getVirtualFile();
            if (virtualFile == null) return false;

            String filePath = virtualFile.getPath();
            LOG.info("Checking file path: " + filePath);

            // 1. 获取项目根目录
            VirtualFile projectRoot = ImportUtils.getProjectRoot(element);
            if (projectRoot == null) {
                LOG.warn("Cannot determine project root, assuming external");
                return false;
            }

            String projectRootPath = projectRoot.getPath();
            LOG.info("Project root path: " + projectRootPath);

            // 2. 检查文件是否在项目根目录下
            if (!filePath.startsWith(projectRootPath)) {
                LOG.info("File is outside project root: external");
                return false;
            }

            // 3. 排除项目内的 node_modules（外部依赖）
            if (filePath.contains("/node_modules/")) {
                LOG.info("File is in node_modules: external");
                return false;
            }

            // 4. 排除其他明确的外部标识
            if (filePath.contains("/.gradle/") || filePath.contains("/.m2/") ||
                filePath.contains("/Library/") || filePath.contains("/usr/lib/")) {
                LOG.info("File is in system library: external");
                return false;
            }

            // 5. 如果文件在项目根目录下且不在 node_modules 中，则为项目内部
            LOG.info("File is under project root and not in node_modules: internal");
            return true;

        } catch (Exception e) {
            LOG.warn("Failed to check if element is in project: " + e.getMessage());
            return false;
        }
    }
}

