package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.ecmascript6.psi.ES6ImportDeclaration;
import com.intellij.lang.javascript.psi.ecma6.TypeScriptImportStatement;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;

import java.util.Collection;

/**
 * 导入模块分类器 - 判断模块是内部还是外部
 * <AUTHOR>
 */
public final class ImportClassifier {

    private static final Logger LOG = Logger.getInstance(ImportClassifier.class);

    private ImportClassifier() {
        // 工具类不允许实例化
    }

    /**
     * 判断是否为外部模块（简化版本，主要用于回退场景）
     * 注意：这个方法只处理明显的情况，复杂的别名解析应该使用 PSI 版本
     */
    public static boolean isExternalModule(String moduleSpecifier) {
        if (moduleSpecifier == null || moduleSpecifier.isEmpty()) {
            return true;
        }

        // 1. 明显的相对路径导入（项目内部）
        if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
            String resourceType = ImportUtils.isStaticResourceImport("import '" + moduleSpecifier + "';") ?
                (ImportUtils.isCssOrStyleImport("import '" + moduleSpecifier + "';") ? "CSS/Style" : "Asset") : "Module";
            System.out.println("[DeepCode] " + resourceType + " '" + moduleSpecifier + "' is relative path, marking as INTERNAL");
            return false;
        }

        // 2. Node.js 内置模块（外部）
        if (isNodeBuiltinModule(moduleSpecifier)) {
            System.out.println("[DeepCode] Module '" + moduleSpecifier + "' is Node.js builtin, marking as EXTERNAL");
            return true;
        }

        // 3. 其他所有情况都应该通过 PSI 解析来判断
        // 这里只是回退方案，当 PSI 无法解析时保守地视为外部模块
        LOG.debug("Cannot determine module type for: " + moduleSpecifier + " without PSI analysis, defaulting to external");
        System.out.println("[DeepCode] Module '" + moduleSpecifier + "' cannot be determined without PSI, defaulting to EXTERNAL");
        return true;
    }



    /**
     * 利用 PSI 能力判断导入是否为外部模块（完全基于 PSI 解析）
     */
    public static boolean isExternalModuleWithPSI(TypeScriptImportStatement importStatement, String moduleSpecifier) {
        String msg = "=== PSI Module Analysis for: " + moduleSpecifier + " ===";
        LOG.info(msg);
        System.out.println("[DeepCode] " + msg);

        // 添加导入语句的详细信息
        try {
            String importText = importStatement.toString();
            String importInfo = "Import statement: " + importText;
            LOG.info(importInfo);
            System.out.println("[DeepCode] " + importInfo);
        } catch (Exception e) {
            String errorMsg = "Error getting import statement text: " + e.getMessage();
            LOG.warn(errorMsg);
            System.out.println("[DeepCode] " + errorMsg);
        }

        // 添加当前文件信息
        try {
            // 通过 PSI 元素获取包含文件
            PsiElement element = (PsiElement) importStatement;
            PsiFile currentFile = element.getContainingFile();
            if (currentFile != null && currentFile.getVirtualFile() != null) {
                String currentFilePath = currentFile.getVirtualFile().getPath();
                String currentFileInfo = "Current file: " + currentFilePath;
                LOG.info(currentFileInfo);
                System.out.println("[DeepCode] " + currentFileInfo);
            }
        } catch (Exception e) {
            String errorMsg = "Error getting current file info: " + e.getMessage();
            LOG.warn(errorMsg);
            System.out.println("[DeepCode] " + errorMsg);
        }

        try {
            // 方法1: 使用 PSI 的引用解析能力来获取实际的目标文件
            String step1Msg = "Step 1: Trying PSI reference resolution...";
            LOG.info(step1Msg);
            System.out.println("[DeepCode] " + step1Msg);

            Collection<? extends PsiElement> referencedElements = importStatement.findReferencedElements();
            String elementsMsg = "PSI found " + referencedElements.size() + " referenced elements for module: " + moduleSpecifier;
            LOG.info(elementsMsg);
            System.out.println("[DeepCode] " + elementsMsg);

            // 如果找到了引用的元素，分析它们的文件位置
            if (!referencedElements.isEmpty()) {
                String foundElementsMsg = "Found " + referencedElements.size() + " referenced elements, analyzing each...";
                LOG.info(foundElementsMsg);
                System.out.println("[DeepCode] " + foundElementsMsg);

                int elementIndex = 0;
                for (PsiElement element : referencedElements) {
                    elementIndex++;
                    String elementMsg = "Element " + elementIndex + "/" + referencedElements.size() + ": " + element.getClass().getSimpleName();
                    LOG.info(elementMsg);
                    System.out.println("[DeepCode] " + elementMsg);

                    try {
                        String elementString = element.toString();
                        String elementInfo = "Element details: " + elementString;
                        LOG.info(elementInfo);
                        System.out.println("[DeepCode] " + elementInfo);
                    } catch (Exception e) {
                        String elementError = "Error getting element details: " + e.getMessage();
                        LOG.warn(elementError);
                        System.out.println("[DeepCode] " + elementError);
                    }

                    // 获取元素所在的文件路径
                    PsiFile containingFile = element.getContainingFile();
                    if (containingFile != null) {
                        String containingFileMsg = "Element has containing file: " + containingFile.getName();
                        LOG.info(containingFileMsg);
                        System.out.println("[DeepCode] " + containingFileMsg);

                        VirtualFile virtualFile = containingFile.getVirtualFile();
                        if (virtualFile != null) {
                            String filePath = virtualFile.getPath();
                            String filePathMsg = "Referenced element file path: " + filePath;
                            LOG.info(filePathMsg);
                            System.out.println("[DeepCode] " + filePathMsg);

                            // 判断文件是否在项目内部
                            String checkingMsg = "Checking if file is in project...";
                            LOG.info(checkingMsg);
                            System.out.println("[DeepCode] " + checkingMsg);

                            boolean isInternal = isFileInProject(importStatement, virtualFile);
                            String internalMsg = "File " + filePath + " is " + (isInternal ? "INTERNAL" : "EXTERNAL");
                            LOG.info(internalMsg);
                            System.out.println("[DeepCode] " + internalMsg);

                            String conclusionMsg = "CONCLUSION: TypeScript Module " + moduleSpecifier + " is " +
                                                 (isInternal ? "INTERNAL" : "EXTERNAL") + " (PSI element analysis)";
                            LOG.info(conclusionMsg);
                            System.out.println("[DeepCode] " + conclusionMsg);

                            return !isInternal;
                        } else {
                            String noVirtualFileMsg = "Element containing file has no virtual file";
                            LOG.warn(noVirtualFileMsg);
                            System.out.println("[DeepCode] " + noVirtualFileMsg);
                        }
                    } else {
                        String noContainingFileMsg = "Element has no containing file";
                        LOG.warn(noContainingFileMsg);
                        System.out.println("[DeepCode] " + noContainingFileMsg);
                    }
                }

                String noValidElementsMsg = "No valid file paths found in referenced elements";
                LOG.warn(noValidElementsMsg);
                System.out.println("[DeepCode] " + noValidElementsMsg);
            } else {
                String noElementsMsg = "No referenced elements found via PSI";
                LOG.info(noElementsMsg);
                System.out.println("[DeepCode] " + noElementsMsg);
            }

            // 方法2: 如果 PSI 引用解析失败，尝试使用增强的文件解析
            ImportUtils.ResourceResolutionResult resolutionResult =
                ImportUtils.resolveResourceWithPSI(importStatement, moduleSpecifier);

            if (resolutionResult.getAbsolutePath() != null) {
                String psiMsg = "Enhanced PSI resolved file path: " + resolutionResult.getAbsolutePath() +
                              " (internal: " + resolutionResult.isInternal() + ")";
                LOG.info(psiMsg);
                System.out.println("[DeepCode] " + psiMsg);

                String conclusionMsg = "CONCLUSION: TypeScript Module " + moduleSpecifier + " is " +
                                     (resolutionResult.isInternal() ? "INTERNAL" : "EXTERNAL") + " (enhanced PSI resolution)";
                LOG.info(conclusionMsg);
                System.out.println("[DeepCode] " + conclusionMsg);

                return !resolutionResult.isInternal();
            }

            // 如果找到了引用的元素，检查它们的文件位置
            if (!referencedElements.isEmpty()) {
                for (PsiElement element : referencedElements) {
                    LOG.info("Checking referenced element: " + element.getClass().getSimpleName());

                    // 获取元素所在的文件路径
                    PsiFile containingFile = element.getContainingFile();
                    if (containingFile != null && containingFile.getVirtualFile() != null) {
                        String filePath = containingFile.getVirtualFile().getPath();
                        String filePathMsg = "Referenced element file path: " + filePath;
                        LOG.info(filePathMsg);
                        System.out.println("[DeepCode] " + filePathMsg);
                    }

                    // 检查引用的文件是否在项目内部
                    boolean isInternal = isElementInProject(element);
                    String internalMsg = "Element " + element.getClass().getSimpleName() + " is internal: " + isInternal;
                    LOG.info(internalMsg);
                    System.out.println("[DeepCode] " + internalMsg);

                    if (isInternal) {
                        String conclusionMsg = "CONCLUSION: Module " + moduleSpecifier + " is INTERNAL (found project element)";
                        LOG.info(conclusionMsg);
                        System.out.println("[DeepCode] " + conclusionMsg);
                        return false; // 项目内部
                    }
                }

                // 如果找到引用元素但都不在项目内，则是外部模块
                LOG.info("CONCLUSION: Module " + moduleSpecifier + " is EXTERNAL (all elements are external)");
                return true;
            }

            // 如果没有找到引用元素，尝试其他方法
            LOG.info("PSI found no referenced elements, trying alternative analysis");

            // 尝试通过模块解析器获取文件位置
            boolean alternativeResult = analyzeModuleWithAlternativeMethod(importStatement, moduleSpecifier);
            if (alternativeResult != true) { // 不是明确的外部模块
                return alternativeResult;
            }

            // 如果所有方法都失败，回退到基于路径的判断
            LOG.info("All PSI methods failed, falling back to path-based analysis");
            boolean pathBasedResult = isExternalModule(moduleSpecifier);
            LOG.info("CONCLUSION: Module " + moduleSpecifier + " is " + (pathBasedResult ? "EXTERNAL" : "INTERNAL") + " (path-based)");
            return pathBasedResult;

        } catch (Exception e) {
            LOG.warn("Failed to use PSI for module resolution, falling back to path-based analysis", e);
            boolean pathBasedResult = isExternalModule(moduleSpecifier);
            LOG.info("CONCLUSION: Module " + moduleSpecifier + " is " + (pathBasedResult ? "EXTERNAL" : "INTERNAL") + " (fallback)");
            return pathBasedResult;
        }
    }

    /**
     * 使用 PSI 判断是否为外部模块（ES6 版本）
     */
    public static boolean isExternalModuleWithES6PSI(ES6ImportDeclaration importDeclaration, String moduleSpecifier) {
        String msg = "=== ES6 PSI Module Analysis for: " + moduleSpecifier + " ===";
        LOG.info(msg);
        System.out.println("[DeepCode] " + msg);

        try {
            // 首先使用改进的PSI文件解析方法
            ImportUtils.ResourceResolutionResult resolutionResult =
                ImportUtils.resolveResourceWithPSI(importDeclaration, moduleSpecifier);

            if (resolutionResult.getAbsolutePath() != null) {
                String psiMsg = "PSI resolved file path: " + resolutionResult.getAbsolutePath() +
                              " (internal: " + resolutionResult.isInternal() + ")";
                LOG.info(psiMsg);
                System.out.println("[DeepCode] " + psiMsg);

                String conclusionMsg = "CONCLUSION: ES6 Module " + moduleSpecifier + " is " +
                                     (resolutionResult.isInternal() ? "INTERNAL" : "EXTERNAL") + " (PSI file resolution)";
                LOG.info(conclusionMsg);
                System.out.println("[DeepCode] " + conclusionMsg);

                return !resolutionResult.isInternal();
            }

            String noMethodMsg = "PSI file resolution failed, trying path-based analysis";
            LOG.info(noMethodMsg);
            System.out.println("[DeepCode] " + noMethodMsg);

            // 尝试获取模块来源信息
            PsiFile currentFile = importDeclaration.getContainingFile();
            if (currentFile != null && currentFile.getVirtualFile() != null) {
                String currentFilePath = currentFile.getVirtualFile().getPath();
                String filePathMsg = "Current file path: " + currentFilePath;
                LOG.info(filePathMsg);
                System.out.println("[DeepCode] " + filePathMsg);

                // 获取项目根目录
                VirtualFile projectRoot = ImportUtils.getProjectRoot(importDeclaration);
                if (projectRoot != null) {
                    String projectRootPath = projectRoot.getPath();
                    String rootMsg = "Project root path: " + projectRootPath;
                    LOG.info(rootMsg);
                    System.out.println("[DeepCode] " + rootMsg);

                    // 尝试基于模块路径和当前文件位置进行推测分析
                    boolean result = analyzeES6ModuleByPath(importDeclaration, moduleSpecifier, currentFilePath, projectRootPath);
                    String conclusionMsg = "CONCLUSION: ES6 Module " + moduleSpecifier + " is " + (result ? "EXTERNAL" : "INTERNAL") + " (path-based PSI analysis)";
                    LOG.info(conclusionMsg);
                    System.out.println("[DeepCode] " + conclusionMsg);
                    return result;
                }
            }

            // 如果无法通过 PSI 分析，回退到基础分析
            String fallbackMsg = "ES6 PSI analysis failed, falling back to basic path analysis";
            LOG.info(fallbackMsg);
            System.out.println("[DeepCode] " + fallbackMsg);

            boolean pathBasedResult = isExternalModule(moduleSpecifier);
            String conclusionMsg = "CONCLUSION: ES6 Module " + moduleSpecifier + " is " + (pathBasedResult ? "EXTERNAL" : "INTERNAL") + " (fallback)";
            LOG.info(conclusionMsg);
            System.out.println("[DeepCode] " + conclusionMsg);
            return pathBasedResult;

        } catch (Exception e) {
            LOG.warn("Failed to use ES6 PSI for module resolution, falling back to path-based analysis", e);
            boolean pathBasedResult = isExternalModule(moduleSpecifier);
            String conclusionMsg = "CONCLUSION: ES6 Module " + moduleSpecifier + " is " + (pathBasedResult ? "EXTERNAL" : "INTERNAL") + " (exception fallback)";
            LOG.info(conclusionMsg);
            System.out.println("[DeepCode] " + conclusionMsg);
            return pathBasedResult;
        }
    }

    /**
     * 判断是否为 Node.js 内置模块
     */
    private static boolean isNodeBuiltinModule(String moduleSpecifier) {
        // Node.js 内置模块列表（这些是 Node.js 标准库，不是第三方包）
        String[] builtinModules = {
            "assert", "buffer", "child_process", "cluster", "crypto", "dgram", "dns",
            "events", "fs", "http", "https", "net", "os", "path", "querystring",
            "readline", "stream", "string_decoder", "timers", "tls", "tty", "url",
            "util", "vm", "zlib", "constants", "domain", "module", "perf_hooks",
            "process", "punycode", "repl", "sys", "v8", "worker_threads"
        };

        // 也可能以 node: 前缀形式出现（Node.js 12+ 支持）
        if (moduleSpecifier.startsWith("node:")) {
            String moduleName = moduleSpecifier.substring(5);
            for (String builtin : builtinModules) {
                if (builtin.equals(moduleName)) {
                    return true;
                }
            }
        }

        // 直接检查模块名
        for (String builtin : builtinModules) {
            if (builtin.equals(moduleSpecifier)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 使用替代方法分析模块
     */
    private static boolean analyzeModuleWithAlternativeMethod(TypeScriptImportStatement importStatement, String moduleSpecifier) {
        try {
            // 获取导入语句所在的文件
            PsiFile currentFile = importStatement.getContainingFile();
            if (currentFile == null || currentFile.getVirtualFile() == null) {
                return true; // 无法确定，默认外部
            }

            String currentFilePath = currentFile.getVirtualFile().getPath();
            LOG.info("Current file path: " + currentFilePath);

            // 获取项目根目录
            VirtualFile projectRoot = ImportUtils.getProjectRoot(importStatement);
            if (projectRoot == null) {
                return true; // 无法确定项目根目录，默认外部
            }

            String projectRootPath = projectRoot.getPath();
            LOG.info("Project root path: " + projectRootPath);

            return true; // 默认外部

        } catch (Exception e) {
            LOG.debug("Alternative analysis failed: " + e.getMessage());
            return true;
        }
    }

    /**
     * 基于路径分析 ES6 模块
     */
    private static boolean analyzeES6ModuleByPath(ES6ImportDeclaration importDeclaration, String moduleSpecifier, String currentFilePath, String projectRootPath) {
        try {
            String analysisMsg = "Analyzing ES6 module by path: " + moduleSpecifier;
            LOG.info(analysisMsg);
            System.out.println("[DeepCode] " + analysisMsg);

            // 如果是相对路径，肯定是项目内部（包括所有静态资源文件）
            if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
                String pathMsg = "Relative import detected: " + moduleSpecifier + " (including all static resources)";
                LOG.info(pathMsg);
                System.out.println("[DeepCode] " + pathMsg);

                // 特别处理静态资源文件
                if (ImportUtils.isStaticResourceImport("import '" + moduleSpecifier + "';")) {
                    String resourceType = ImportUtils.isCssOrStyleImport("import '" + moduleSpecifier + "';") ? "CSS/Style" : "Asset";
                    String resourceMsg = resourceType + " file import detected: " + moduleSpecifier;
                    LOG.info(resourceMsg);
                    System.out.println("[DeepCode] " + resourceMsg);
                }

                // 相对路径导入，肯定是项目内部
                return false;
            }

            // 如果是项目别名路径，通常也是项目内部
            if (moduleSpecifier.startsWith("@/")) {
                String aliasMsg = "Project alias import detected: " + moduleSpecifier;
                LOG.info(aliasMsg);
                System.out.println("[DeepCode] " + aliasMsg);

                // 特别处理静态资源文件
                if (ImportUtils.isStaticResourceImport("import '" + moduleSpecifier + "';")) {
                    String resourceType = ImportUtils.isCssOrStyleImport("import '" + moduleSpecifier + "';") ? "CSS/Style" : "Asset";
                    String resourceMsg = resourceType + " file with alias detected: " + moduleSpecifier;
                    LOG.info(resourceMsg);
                    System.out.println("[DeepCode] " + resourceMsg);
                }

                return false;
            }

            // 对于绝对路径或别名，检查是否可能指向项目内部
            return true; // 默认外部

        } catch (Exception e) {
            LOG.debug("Failed to analyze ES6 module by path: " + e.getMessage());
            return true; // 默认外部
        }
    }

    /**
     * 检查文件是否在项目内部
     */
    private static boolean isFileInProject(PsiElement contextElement, VirtualFile virtualFile) {
        String methodMsg = "=== isFileInProject() called ===";
        LOG.info(methodMsg);
        System.out.println("[DeepCode] " + methodMsg);

        try {
            if (virtualFile == null) {
                String nullFileMsg = "VirtualFile is null, returning false";
                LOG.warn(nullFileMsg);
                System.out.println("[DeepCode] " + nullFileMsg);
                return false;
            }

            String filePath = virtualFile.getPath();
            String filePathMsg = "Checking file path: " + filePath;
            LOG.info(filePathMsg);
            System.out.println("[DeepCode] " + filePathMsg);

            // 1. 获取项目根目录
            String gettingRootMsg = "Getting project root...";
            LOG.info(gettingRootMsg);
            System.out.println("[DeepCode] " + gettingRootMsg);

            VirtualFile projectRoot = ImportUtils.getProjectRoot(contextElement);
            if (projectRoot == null) {
                String noRootMsg = "Cannot determine project root, assuming external";
                LOG.warn(noRootMsg);
                System.out.println("[DeepCode] " + noRootMsg);
                return false;
            }

            String projectRootPath = projectRoot.getPath();
            String rootPathMsg = "Project root path: " + projectRootPath;
            LOG.info(rootPathMsg);
            System.out.println("[DeepCode] " + rootPathMsg);

            // 2. 检查文件是否在项目根目录下
            String checkingRootMsg = "Checking if file is under project root...";
            LOG.info(checkingRootMsg);
            System.out.println("[DeepCode] " + checkingRootMsg);

            if (!filePath.startsWith(projectRootPath)) {
                String outsideRootMsg = "File is outside project root: external";
                LOG.info(outsideRootMsg);
                System.out.println("[DeepCode] " + outsideRootMsg);
                return false;
            }

            String underRootMsg = "File is under project root";
            LOG.info(underRootMsg);
            System.out.println("[DeepCode] " + underRootMsg);

            // 3. 排除项目内的 node_modules（外部依赖）
            String checkingNodeModulesMsg = "Checking if file is in node_modules...";
            LOG.info(checkingNodeModulesMsg);
            System.out.println("[DeepCode] " + checkingNodeModulesMsg);

            if (filePath.contains("/node_modules/")) {
                String nodeModulesMsg = "File is in node_modules: external";
                LOG.info(nodeModulesMsg);
                System.out.println("[DeepCode] " + nodeModulesMsg);
                return false;
            }

            String finalMsg = "File is in project: internal";
            LOG.info(finalMsg);
            System.out.println("[DeepCode] " + finalMsg);
            return true;

        } catch (Exception e) {
            String errorMsg = "Error checking if file is in project: " + e.getMessage();
            LOG.warn(errorMsg, e);
            System.out.println("[DeepCode] " + errorMsg);
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查 PSI 元素是否在项目内部
     */
    private static boolean isElementInProject(PsiElement element) {
        try {
            if (element == null) return false;

            // 获取元素所在的文件
            PsiFile containingFile = element.getContainingFile();
            if (containingFile == null) return false;

            // 获取文件的虚拟文件
            VirtualFile virtualFile = containingFile.getVirtualFile();
            if (virtualFile == null) return false;

            return isFileInProject(element, virtualFile);

        } catch (Exception e) {
            LOG.warn("Error checking if element is in project: " + e.getMessage());
            return false;
        }
    }
}

