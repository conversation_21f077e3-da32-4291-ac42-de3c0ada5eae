package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.lang.ecmascript6.psi.ES6ImportDeclaration;
import com.intellij.lang.javascript.psi.JSCallExpression;
import com.intellij.lang.javascript.psi.JSLiteralExpression;
import com.intellij.lang.javascript.psi.ecma6.TypeScriptImportStatement;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiReference;
import com.intellij.psi.util.PsiTreeUtil;

import java.util.Collection;

/**
 * PSI 解析器
 * 
 * 职责：
 * - 统一处理 PSI 解析逻辑
 * - 支持不同类型的 PSI 元素
 * - 提供引用解析和元素分析
 * 
 * <AUTHOR>
 */
public class PSIResolver {
    
    private static final Logger LOG = Logger.getInstance(PSIResolver.class);
    
    private final ProjectPathConverter pathConverter;
    
    public PSIResolver() {
        this.pathConverter = new ProjectPathConverter();
    }
    
    /**
     * 解析目标文件路径
     * 
     * @param importElement PSI 导入元素
     * @param moduleSpecifier 模块说明符
     * @return 解析的目标文件路径，失败时返回 null
     */
    public String resolveTargetFilePath(PsiElement importElement, String moduleSpecifier) {
        try {
            LOG.debug("PSI resolving target file path for: " + moduleSpecifier);
            
            if (importElement == null || moduleSpecifier == null) {
                return null;
            }
            
            // 方法1: 尝试通过 findReferencedElements 解析
            String referencedPath = resolveViaReferencedElements(importElement, moduleSpecifier);
            if (referencedPath != null) {
                LOG.debug("PSI resolution via referenced elements successful: " + referencedPath);
                return referencedPath;
            }
            
            // 方法2: 尝试通过字符串字面量引用解析
            String literalPath = resolveViaStringLiteralReferences(importElement, moduleSpecifier);
            if (literalPath != null) {
                LOG.debug("PSI resolution via string literal references successful: " + literalPath);
                return literalPath;
            }
            
            // 方法3: 尝试通过直接引用解析
            String directPath = resolveViaDirectReferences(importElement, moduleSpecifier);
            if (directPath != null) {
                LOG.debug("PSI resolution via direct references successful: " + directPath);
                return directPath;
            }
            
            LOG.debug("PSI resolution failed for: " + moduleSpecifier);
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error in PSI resolution: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 通过 findReferencedElements 解析
     */
    private String resolveViaReferencedElements(PsiElement importElement, String moduleSpecifier) {
        try {
            Collection<? extends PsiElement> referencedElements = null;
            
            if (importElement instanceof TypeScriptImportStatement) {
                TypeScriptImportStatement tsImport = (TypeScriptImportStatement) importElement;
                referencedElements = tsImport.findReferencedElements();
                LOG.debug("TypeScript import found " + referencedElements.size() + " referenced elements");
                
            } else if (importElement instanceof ES6ImportDeclaration) {
                // ES6ImportDeclaration 处理
                referencedElements = resolveES6ReferencedElements((ES6ImportDeclaration) importElement);
                
            } else if (importElement instanceof JSCallExpression) {
                // CommonJS require 处理
                referencedElements = resolveRequireReferencedElements((JSCallExpression) importElement, moduleSpecifier);
            }
            
            // 分析引用的元素，找到文件
            if (referencedElements != null && !referencedElements.isEmpty()) {
                for (PsiElement element : referencedElements) {
                    String filePath = extractFilePathFromElement(element);
                    if (filePath != null) {
                        return filePath;
                    }
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error resolving via referenced elements: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 通过字符串字面量引用解析
     */
    private String resolveViaStringLiteralReferences(PsiElement importElement, String moduleSpecifier) {
        try {
            // 查找字符串字面量
            Collection<JSLiteralExpression> literals = 
                PsiTreeUtil.findChildrenOfType(importElement, JSLiteralExpression.class);
            
            for (JSLiteralExpression literal : literals) {
                if (literal.isStringLiteral()) {
                    String value = literal.getStringValue();
                    if (moduleSpecifier.equals(value)) {
                        // 尝试解析字符串字面量的引用
                        PsiReference[] references = literal.getReferences();
                        for (PsiReference reference : references) {
                            PsiElement resolved = reference.resolve();
                            if (resolved != null) {
                                String filePath = extractFilePathFromElement(resolved);
                                if (filePath != null) {
                                    return filePath;
                                }
                            }
                        }
                    }
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error resolving via string literal references: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 通过直接引用解析
     */
    private String resolveViaDirectReferences(PsiElement importElement, String moduleSpecifier) {
        try {
            // 获取元素的所有引用
            PsiReference[] references = importElement.getReferences();
            
            for (PsiReference reference : references) {
                PsiElement resolved = reference.resolve();
                if (resolved != null) {
                    String filePath = extractFilePathFromElement(resolved);
                    if (filePath != null) {
                        return filePath;
                    }
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error resolving via direct references: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析 ES6 引用元素
     */
    private Collection<? extends PsiElement> resolveES6ReferencedElements(ES6ImportDeclaration es6Import) {
        try {
            // 尝试使用反射调用 findReferencedElements 方法
            try {
                java.lang.reflect.Method method = es6Import.getClass().getMethod("findReferencedElements");
                Object result = method.invoke(es6Import);
                if (result instanceof Collection) {
                    @SuppressWarnings("unchecked")
                    Collection<? extends PsiElement> elements = (Collection<? extends PsiElement>) result;
                    LOG.debug("ES6 import found " + elements.size() + " referenced elements via reflection");
                    return elements;
                }
            } catch (Exception reflectionException) {
                LOG.debug("ES6 findReferencedElements method not available: " + reflectionException.getMessage());
            }
            
            // 其他 ES6 解析方法...
            return java.util.Collections.emptyList();
            
        } catch (Exception e) {
            LOG.debug("Error resolving ES6 referenced elements: " + e.getMessage());
            return java.util.Collections.emptyList();
        }
    }
    
    /**
     * 解析 require 引用元素
     */
    private Collection<? extends PsiElement> resolveRequireReferencedElements(JSCallExpression callExpression, String moduleSpecifier) {
        try {
            // 这里可以实现 require 特定的解析逻辑
            // 目前返回空集合
            return java.util.Collections.emptyList();
            
        } catch (Exception e) {
            LOG.debug("Error resolving require referenced elements: " + e.getMessage());
            return java.util.Collections.emptyList();
        }
    }
    
    /**
     * 从 PSI 元素提取文件路径
     */
    private String extractFilePathFromElement(PsiElement element) {
        try {
            if (element == null) {
                return null;
            }
            
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null && containingFile.getVirtualFile() != null) {
                String absolutePath = containingFile.getVirtualFile().getPath();
                
                // 尝试转换为项目相对路径
                String relativePath = pathConverter.convert(absolutePath, element);
                if (relativePath != null) {
                    LOG.debug("Converted to project relative path: " + relativePath);
                    return relativePath;
                } else {
                    // 如果无法转换为相对路径，返回绝对路径
                    LOG.debug("Using absolute path: " + absolutePath);
                    return absolutePath;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error extracting file path from element: " + e.getMessage());
            return null;
        }
    }
}
