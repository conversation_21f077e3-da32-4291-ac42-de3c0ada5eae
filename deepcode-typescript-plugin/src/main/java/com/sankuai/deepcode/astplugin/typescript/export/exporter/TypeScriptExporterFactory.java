package com.sankuai.deepcode.astplugin.typescript.export.exporter;

import com.sankuai.deepcode.astplugin.export.ExportOption;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * TypeScript 导出器工厂
 * 负责根据导出选项创建相应的导出器实例
 *
 * <AUTHOR>
 */
public class TypeScriptExporterFactory {

    private static final Map<ExportOption, TypeScriptDataExporter> EXPORTER_MAP = new LinkedHashMap<>();

    static {
        // 初始化导出器映射
        EXPORTER_MAP.put(ExportOption.STATISTICS_CSV, new TypeScriptCsvExporter());
        EXPORTER_MAP.put(ExportOption.CALL_DETAILS, new TypeScriptCallRelationExporter());
        EXPORTER_MAP.put(ExportOption.EXPORT_IMPORTS, new TypeScriptImportsExporter());
    }

    /**
     * 根据导出选项获取相应的导出器
     *
     * @param exportOption 导出选项
     * @return 对应的导出器实例
     * @throws UnsupportedOperationException 如果不支持该导出选项
     */
    public static TypeScriptDataExporter getExporter(ExportOption exportOption) {
        TypeScriptDataExporter exporter = EXPORTER_MAP.get(exportOption);
        if (exporter == null) {
            throw new UnsupportedOperationException("不支持的导出选项: " + exportOption);
        }
        return exporter;
    }

    /**
     * 获取所有支持的导出选项
     *
     * @return 支持的导出选项数组
     */
    public static ExportOption[] getSupportedExportOptions() {
        return EXPORTER_MAP.keySet().toArray(new ExportOption[0]);
    }

    /**
     * 检查是否支持指定的导出选项
     *
     * @param exportOption 导出选项
     * @return 如果支持返回true，否则返回false
     */
    public static boolean isSupported(ExportOption exportOption) {
        return EXPORTER_MAP.containsKey(exportOption);
    }

    /**
     * 注册自定义导出器
     *
     * @param exportOption 导出选项
     * @param exporter 导出器实例
     */
    public static void registerExporter(ExportOption exportOption, TypeScriptDataExporter exporter) {
        EXPORTER_MAP.put(exportOption, exporter);
    }

    /**
     * 移除导出器
     *
     * @param exportOption 导出选项
     * @return 被移除的导出器实例，如果不存在返回null
     */
    public static TypeScriptDataExporter removeExporter(ExportOption exportOption) {
        return EXPORTER_MAP.remove(exportOption);
    }
}

