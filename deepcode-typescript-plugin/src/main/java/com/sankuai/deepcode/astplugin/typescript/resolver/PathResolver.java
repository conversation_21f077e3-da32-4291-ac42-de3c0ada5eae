package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.psi.PsiElement;

/**
 * 路径解析器
 * 
 * 职责：
 * - 统一处理所有路径解析逻辑
 * - 相对路径转绝对路径
 * - 项目路径转换
 * - 扩展名推断
 * 
 * <AUTHOR>
 */
public class PathResolver {
    
    private final RelativePathResolver relativePathResolver;
    private final ProjectPathConverter projectPathConverter;
    private final FileExtensionInferrer extensionInferrer;
    
    public PathResolver() {
        this.relativePathResolver = new RelativePathResolver();
        this.projectPathConverter = new ProjectPathConverter();
        this.extensionInferrer = new FileExtensionInferrer();
    }
    
    /**
     * 解析相对路径为绝对路径
     * 
     * @param currentFilePath 当前文件的绝对路径
     * @param relativeModuleSpecifier 相对模块说明符
     * @return 解析后的绝对路径
     */
    public String resolveRelativeToAbsolute(String currentFilePath, String relativeModuleSpecifier) {
        return relativePathResolver.resolve(currentFilePath, relativeModuleSpecifier);
    }
    
    /**
     * 转换为项目相对路径
     * 
     * @param absolutePath 绝对路径
     * @param context PSI 上下文元素
     * @return 项目相对路径，失败时返回 null
     */
    public String convertToProjectRelative(String absolutePath, PsiElement context) {
        return projectPathConverter.convert(absolutePath, context);
    }
    
    /**
     * 智能推断文件扩展名
     * 
     * @param filePath 文件路径（无扩展名）
     * @return 添加扩展名后的文件路径
     */
    public String inferExtension(String filePath) {
        return extensionInferrer.infer(filePath);
    }
    
    /**
     * 查找实际存在的文件（考虑扩展名）
     * 
     * @param basePath 基础路径
     * @return 实际存在的文件路径，未找到时返回 null
     */
    public String findActualFile(String basePath) {
        return extensionInferrer.findExistingFile(basePath);
    }
    
    /**
     * 检查是否为相对路径
     * 
     * @param moduleSpecifier 模块说明符
     * @return 是否为相对路径
     */
    public boolean isRelativePath(String moduleSpecifier) {
        return moduleSpecifier != null && 
               (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../"));
    }
    
    /**
     * 强制解析路径（兜底策略）
     * 
     * @param context PSI 上下文元素
     * @param moduleSpecifier 模块说明符
     * @return 强制解析的路径
     */
    public String forceResolve(PsiElement context, String moduleSpecifier) {
        if (isRelativePath(moduleSpecifier)) {
            return relativePathResolver.forceResolve(context, moduleSpecifier);
        } else {
            return extensionInferrer.forceInfer(moduleSpecifier);
        }
    }
}
