package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.openapi.diagnostic.Logger;

import java.io.File;

/**
 * 文件扩展名推断器
 * 
 * 职责：
 * - 智能推断文件扩展名
 * - 查找实际存在的文件
 * - 基于路径特征推断文件类型
 * 
 * <AUTHOR>
 */
public class FileExtensionInferrer {
    
    private static final Logger LOG = Logger.getInstance(FileExtensionInferrer.class);
    
    // 支持的扩展名，按优先级排序
    private static final String[] EXTENSIONS = {".ts", ".tsx", ".js", ".jsx", ".vue", ".d.ts"};
    
    /**
     * 智能推断文件扩展名
     * 
     * @param filePath 文件路径（无扩展名）
     * @return 添加扩展名后的文件路径
     */
    public String infer(String filePath) {
        try {
            if (filePath == null) {
                return null;
            }
            
            LOG.debug("Inferring extension for: " + filePath);
            
            // 如果已经有扩展名，直接返回
            if (hasExtension(filePath)) {
                LOG.debug("File already has extension: " + filePath);
                return filePath;
            }
            
            // 基于路径特征推断扩展名
            String extension = inferExtensionByPathFeatures(filePath);
            String result = filePath + extension;
            
            LOG.debug("Inferred extension: " + filePath + " -> " + result);
            return result;
            
        } catch (Exception e) {
            LOG.debug("Error inferring extension: " + e.getMessage());
            return filePath + ".ts"; // 默认扩展名
        }
    }
    
    /**
     * 查找实际存在的文件（考虑扩展名）
     * 
     * @param basePath 基础路径
     * @return 实际存在的文件路径，未找到时返回 null
     */
    public String findExistingFile(String basePath) {
        try {
            if (basePath == null) {
                return null;
            }
            
            LOG.debug("Finding existing file for: " + basePath);
            
            // 首先检查是否已经有扩展名且文件存在
            if (hasExtension(basePath)) {
                File file = new File(basePath);
                if (file.exists()) {
                    LOG.debug("Found existing file with extension: " + basePath);
                    return basePath;
                }
            }
            
            // 尝试添加不同的扩展名
            for (String extension : EXTENSIONS) {
                String pathWithExtension = basePath + extension;
                File file = new File(pathWithExtension);
                if (file.exists()) {
                    LOG.debug("Found existing file: " + pathWithExtension);
                    return pathWithExtension;
                }
            }
            
            // 检查是否是目录，查找 index 文件
            File dir = new File(basePath);
            if (dir.exists() && dir.isDirectory()) {
                for (String extension : EXTENSIONS) {
                    String indexPath = basePath + "/index" + extension;
                    File indexFile = new File(indexPath);
                    if (indexFile.exists()) {
                        LOG.debug("Found index file: " + indexPath);
                        return indexPath;
                    }
                }
            }
            
            LOG.debug("No existing file found for: " + basePath);
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error finding existing file: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 强制推断扩展名（兜底策略）
     * 
     * @param filePath 文件路径
     * @return 添加扩展名后的文件路径，永不返回 null
     */
    public String forceInfer(String filePath) {
        try {
            if (filePath == null) {
                return "unknown.ts";
            }
            
            // 首先尝试正常推断
            String inferred = infer(filePath);
            if (inferred != null) {
                return inferred;
            }
            
            // 兜底策略：基于模块说明符特征推断
            return forceInferByModuleSpecifier(filePath);
            
        } catch (Exception e) {
            LOG.debug("Error in force infer: " + e.getMessage());
            return filePath + ".ts"; // 最终兜底
        }
    }
    
    /**
     * 检查文件是否已有扩展名
     * 
     * @param filePath 文件路径
     * @return 是否已有扩展名
     */
    private boolean hasExtension(String filePath) {
        if (filePath == null) {
            return false;
        }
        
        for (String extension : EXTENSIONS) {
            if (filePath.endsWith(extension)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 基于路径特征推断扩展名
     * 
     * @param filePath 文件路径
     * @return 推断的扩展名
     */
    private String inferExtensionByPathFeatures(String filePath) {
        String lowerPath = filePath.toLowerCase();
        
        // 组件相关
        if (lowerPath.contains("component") || lowerPath.contains("Component")) {
            if (lowerPath.contains("vue") || lowerPath.endsWith("/index")) {
                return ".vue";
            } else {
                return ".tsx";
            }
        }
        
        // 页面相关
        if (lowerPath.contains("page") || lowerPath.contains("Page")) {
            return ".tsx";
        }
        
        // 类型定义相关
        if (lowerPath.contains("type") || lowerPath.contains("interface") || 
            lowerPath.contains("model") || lowerPath.contains("dto")) {
            return ".ts";
        }
        
        // 测试文件
        if (lowerPath.contains("test") || lowerPath.contains("spec")) {
            return ".ts";
        }
        
        // 配置文件
        if (lowerPath.contains("config") || lowerPath.contains("Config")) {
            return ".ts";
        }
        
        // 工具类
        if (lowerPath.contains("util") || lowerPath.contains("helper") || 
            lowerPath.contains("service")) {
            return ".ts";
        }
        
        // 静态资源
        if (lowerPath.contains("static") || lowerPath.contains("lib") || 
            lowerPath.contains("vendor") || lowerPath.contains("assets")) {
            return ".js";
        }
        
        // 默认为 TypeScript 文件
        return ".ts";
    }
    
    /**
     * 基于模块说明符强制推断（最终兜底）
     * 
     * @param moduleSpecifier 模块说明符
     * @return 推断的文件路径
     */
    private String forceInferByModuleSpecifier(String moduleSpecifier) {
        if (moduleSpecifier == null) {
            return "unknown.ts";
        }
        
        // 对于别名路径
        if (moduleSpecifier.startsWith("@/")) {
            String relativePath = moduleSpecifier.substring(2);
            return "src/" + relativePath + inferExtensionByPathFeatures(relativePath);
        }
        
        // 对于 src/ 开头的路径
        if (moduleSpecifier.startsWith("src/")) {
            return moduleSpecifier + inferExtensionByPathFeatures(moduleSpecifier);
        }
        
        // 对于简单模块名
        if (!moduleSpecifier.contains("/")) {
            return "src/" + moduleSpecifier + ".ts";
        }
        
        // 其他情况
        return moduleSpecifier + inferExtensionByPathFeatures(moduleSpecifier);
    }
}
