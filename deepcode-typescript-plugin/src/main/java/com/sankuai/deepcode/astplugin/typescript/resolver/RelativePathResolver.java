package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 相对路径解析器
 * 
 * 职责：
 * - 解析相对路径为绝对路径
 * - 处理 ../ 和 ./ 路径
 * - 提供强制解析能力
 * 
 * <AUTHOR>
 */
public class RelativePathResolver {
    
    private static final Logger LOG = Logger.getInstance(RelativePathResolver.class);
    
    /**
     * 解析相对路径为绝对路径
     * 
     * @param currentFilePath 当前文件的绝对路径
     * @param relativeModuleSpecifier 相对模块说明符
     * @return 解析后的绝对路径
     */
    public String resolve(String currentFilePath, String relativeModuleSpecifier) {
        try {
            LOG.debug("Resolving relative path: " + relativeModuleSpecifier + " from: " + currentFilePath);
            
            if (currentFilePath == null || relativeModuleSpecifier == null) {
                return null;
            }
            
            // 获取当前文件的目录
            String currentDir = currentFilePath.substring(0, currentFilePath.lastIndexOf('/'));
            
            // 解析相对路径
            return resolveRelativePath(currentDir, relativeModuleSpecifier);
            
        } catch (Exception e) {
            LOG.debug("Error resolving relative path: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 强制解析相对路径（兜底策略）
     * 
     * @param context PSI 上下文元素
     * @param relativeModuleSpecifier 相对模块说明符
     * @return 强制解析的绝对路径
     */
    public String forceResolve(PsiElement context, String relativeModuleSpecifier) {
        try {
            LOG.debug("Force resolving relative path: " + relativeModuleSpecifier);
            
            PsiFile currentFile = context.getContainingFile();
            if (currentFile == null || currentFile.getVirtualFile() == null) {
                LOG.debug("Cannot get current file, using fallback");
                return relativeModuleSpecifier; // 兜底返回原始路径
            }
            
            String currentFilePath = currentFile.getVirtualFile().getPath();
            String resolved = resolve(currentFilePath, relativeModuleSpecifier);
            
            if (resolved != null) {
                LOG.debug("Force resolve successful: " + resolved);
                return resolved;
            } else {
                // 即使解析失败，也要返回一个合理的路径
                String currentDir = currentFilePath.substring(0, currentFilePath.lastIndexOf('/'));
                String fallbackPath = currentDir + "/" + relativeModuleSpecifier;
                LOG.debug("Force resolve fallback: " + fallbackPath);
                return fallbackPath;
            }
            
        } catch (Exception e) {
            LOG.debug("Error in force resolve: " + e.getMessage());
            // 最终兜底：返回相对路径本身
            return relativeModuleSpecifier;
        }
    }
    
    /**
     * 解析相对路径的核心逻辑
     * 
     * @param currentDir 当前目录
     * @param relativePath 相对路径
     * @return 解析后的绝对路径
     */
    private String resolveRelativePath(String currentDir, String relativePath) {
        try {
            String[] currentParts = currentDir.split("/");
            String[] relativeParts = relativePath.split("/");
            
            List<String> resultParts = new ArrayList<>(Arrays.asList(currentParts));
            
            for (String part : relativeParts) {
                if (part.equals("..")) {
                    // 上级目录
                    if (!resultParts.isEmpty()) {
                        resultParts.remove(resultParts.size() - 1);
                    }
                } else if (!part.equals(".") && !part.isEmpty()) {
                    // 普通目录或文件名
                    resultParts.add(part);
                }
                // 忽略 "." 和空字符串
            }
            
            return String.join("/", resultParts);
            
        } catch (Exception e) {
            LOG.debug("Error in resolveRelativePath: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查是否为相对路径
     * 
     * @param moduleSpecifier 模块说明符
     * @return 是否为相对路径
     */
    public boolean isRelativePath(String moduleSpecifier) {
        return moduleSpecifier != null && 
               (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../"));
    }
    
    /**
     * 规范化路径（移除多余的 ./ 和 ../）
     * 
     * @param path 原始路径
     * @return 规范化后的路径
     */
    public String normalizePath(String path) {
        if (path == null) {
            return null;
        }
        
        try {
            String[] parts = path.split("/");
            List<String> normalizedParts = new ArrayList<>();
            
            for (String part : parts) {
                if (part.equals("..")) {
                    if (!normalizedParts.isEmpty()) {
                        normalizedParts.remove(normalizedParts.size() - 1);
                    }
                } else if (!part.equals(".") && !part.isEmpty()) {
                    normalizedParts.add(part);
                }
            }
            
            return String.join("/", normalizedParts);
            
        } catch (Exception e) {
            LOG.debug("Error normalizing path: " + e.getMessage());
            return path; // 返回原始路径作为兜底
        }
    }
}
