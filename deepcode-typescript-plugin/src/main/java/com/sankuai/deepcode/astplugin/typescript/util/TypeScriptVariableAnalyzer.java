package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.lang.javascript.psi.JSVarStatement;
import com.intellij.lang.javascript.psi.JSVariable;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;

import java.util.Collection;

/**
 * TypeScript/JavaScript 变量分析器
 * 负责分析模块级变量声明
 *
 * <AUTHOR>
 */
public final class TypeScriptVariableAnalyzer {

    private static final Logger LOG = Logger.getInstance(TypeScriptVariableAnalyzer.class);

    private TypeScriptVariableAnalyzer() {
        // 工具类不允许实例化
    }

    /**
     * 分析模块级变量
     */
    public static void analyzeModuleVariables(JSFile jsFile, AnalysisResult result) {
        try {
            Collection<JSVarStatement> varStatements = PsiTreeUtil.findChildrenOfType(jsFile, JSVarStatement.class);
            
            for (JSVarStatement varStatement : varStatements) {
                analyzeVariableStatement(varStatement, result);
            }
            
        } catch (Exception e) {
            LOG.warn("Failed to analyze module variables in file: " + jsFile.getName(), e);
            result.addError("Variable analysis failed: " + e.getMessage());
        }
    }

    /**
     * 分析变量声明语句
     */
    private static void analyzeVariableStatement(JSVarStatement varStatement, AnalysisResult result) {
        try {
            JSVariable[] variables = varStatement.getVariables();
            
            for (JSVariable variable : variables) {
                analyzeVariable(variable, result);
            }
            
        } catch (Exception e) {
            LOG.warn("Failed to analyze variable statement: " + varStatement.getText(), e);
        }
    }

    /**
     * 分析单个变量
     */
    private static void analyzeVariable(JSVariable variable, AnalysisResult result) {
        try {
            String variableName = variable.getName();
            if (variableName == null) {
                return;
            }

            // 简单记录变量信息，不创建节点
            LOG.debug("Found variable: " + variableName);

            // 这里可以扩展来处理具体的变量分析逻辑

        } catch (Exception e) {
            LOG.warn("Failed to analyze variable: " + variable.getName(), e);
        }
    }
}

