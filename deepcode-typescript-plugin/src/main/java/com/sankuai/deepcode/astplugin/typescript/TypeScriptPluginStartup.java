package com.sankuai.deepcode.astplugin.typescript;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import com.sankuai.deepcode.astplugin.export.ExportRegistry;
import com.sankuai.deepcode.astplugin.typescript.export.TypeScriptExportService;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 插件启动组件 - 确保TypeScript分析器在插件启动时正确注册
 *
 * <AUTHOR>
 */
public class TypeScriptPluginStartup implements ProjectActivity {

    private static final Logger LOG = Logger.getInstance(TypeScriptPluginStartup.class);

    @Nullable
    @Override
    public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        // 在后台线程中初始化分析器服务
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                LOG.info("🚀 TypeScript Plugin startup - initializing analyzer service");

                // 抑制特定的实验性功能警告
                suppressExperimentalWarnings();

                // 获取或创建TypeScriptAnalyzerService实例，这会触发分析器注册
                TypeScriptAnalyzerService typeScriptService = ApplicationManager.getApplication()
                        .getService(TypeScriptAnalyzerService.class);

                // 确保分析器已注册
                if (typeScriptService != null) {
                    typeScriptService.ensureAnalyzerRegistered();
                    LOG.info("✅ TypeScript analyzer registered successfully");
                } else {
                    LOG.error("❌ Failed to get TypeScriptAnalyzerService instance");
                }

                // 注册TypeScript导出服务
                try {
                    ExportRegistry exportRegistry = ExportRegistry.getInstance();
                    TypeScriptExportService typeScriptExportService = new TypeScriptExportService();
                    exportRegistry.registerExportService(typeScriptExportService);
                    LOG.info("✅ TypeScript export service registered successfully");
                } catch (Exception e) {
                    LOG.error("❌ Failed to register TypeScript export service", e);
                }

                LOG.info("✅ TypeScript Plugin startup completed successfully");

            } catch (Exception e) {
                LOG.error("💥 Error during TypeScript plugin startup", e);
            }
        });
        return Unit.INSTANCE;
    }

    /**
     * 抑制TypeScript编译器相关的实验性功能警告
     */
    private void suppressExperimentalWarnings() {
        try {
            // 设置系统属性以禁用TypeScript编译器评估功能的警告
            System.setProperty("typescript.compiler.evaluation.enabled", "false");

            // 如果需要，可以添加更多的警告抑制设置
            LOG.debug("Experimental TypeScript warnings suppressed");
        } catch (Exception e) {
            LOG.debug("Failed to suppress experimental warnings: " + e.getMessage());
        }
    }
}

