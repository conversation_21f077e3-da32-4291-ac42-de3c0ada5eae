package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.lang.javascript.psi.JSFunction;
import com.intellij.lang.javascript.psi.ecmal4.JSClass;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;

import java.util.Collection;

/**
 * TypeScript/JavaScript 结构分析器
 * 负责分析和创建函数、类等节点结构
 *
 * <AUTHOR>
 */
public final class TypeScriptStructureAnalyzer {

    private static final Logger LOG = Logger.getInstance(TypeScriptStructureAnalyzer.class);

    private TypeScriptStructureAnalyzer() {
        // 工具类不允许实例化
    }

    /**
     * 分析文件中的所有函数和类
     */
    public static void analyzeFileStructure(JSFile jsFile, AnalysisResult result) {
        try {
            // 分析所有函数
            Collection<JSFunction> functions = PsiTreeUtil.findChildrenOfType(jsFile, JSFunction.class);
            for (JSFunction function : functions) {
                analyzeFunction(function, result);
            }

            // 分析所有类
            Collection<JSClass> classes = PsiTreeUtil.findChildrenOfType(jsFile, JSClass.class);
            for (JSClass jsClass : classes) {
                analyzeClass(jsClass, result);
            }

        } catch (Exception e) {
            LOG.warn("Failed to analyze file structure: " + jsFile.getName(), e);
            result.addError("Structure analysis failed: " + e.getMessage());
        }
    }

    /**
     * 分析函数并创建节点
     */
    public static void analyzeFunction(JSFunction jsFunction, AnalysisResult result) {
        try {
            String functionName = jsFunction.getName() != null ? jsFunction.getName() : "anonymous";
            String packageName = TypeScriptPsiUtils.getPackageName(jsFunction);
            String className = getContainingClassName(jsFunction);
            
            String fullId = packageName + "." + (className != null ? className + "." : "") + functionName;
            String signature = TypeScriptPsiUtils.buildFunctionSignature(jsFunction);
            
            // 检查是否已存在该节点
            AnalysisNode existingNode = result.getNodes().get(fullId);
            if (existingNode != null) {
                return;
            }
            
            AnalysisNode functionNode = new AnalysisNode(
                    fullId,
                    className != null ? AnalysisNode.NodeType.METHOD : AnalysisNode.NodeType.FUNCTION,
                    functionName,
                    className,
                    packageName,
                    TypeScriptPsiUtils.getLineNumber(jsFunction),
                    signature,
                    packageName,
                    TypeScriptPsiUtils.getFilePath(jsFunction),
                    TypeScriptPsiUtils.detectLanguage((JSFile) jsFunction.getContainingFile()).getCode()
            );
            
            result.addNode(functionNode);
            LOG.debug("Added function node: " + fullId);
            
        } catch (Exception e) {
            LOG.warn("Failed to analyze function: " + jsFunction.getName(), e);
        }
    }

    /**
     * 分析类并创建节点
     */
    public static void analyzeClass(JSClass jsClass, AnalysisResult result) {
        try {
            String className = jsClass.getName();
            if (className == null) {
                return;
            }
            String packageName = TypeScriptPsiUtils.getPackageName(jsClass);
            String fullId = packageName + "." + className;
            
            // 检查是否已存在该节点
            AnalysisNode existingNode = result.getNodes().get(fullId);
            if (existingNode != null) {
                return;
            }
            AnalysisNode classNode = new AnalysisNode(
                    fullId,
                    AnalysisNode.NodeType.CLASS,
                    className,
                    className,
                    packageName,
                    TypeScriptPsiUtils.getLineNumber(jsClass),
                    className + "()",
                    packageName,
                    TypeScriptPsiUtils.getFilePath(jsClass),
                    TypeScriptPsiUtils.detectLanguage((JSFile) jsClass.getContainingFile()).getCode()
            );
            result.addNode(classNode);
            LOG.debug("Added class node: " + fullId);
        } catch (Exception e) {
            LOG.warn("Failed to analyze class: " + jsClass.getName(), e);
        }
    }

    /**
     * 查找或创建函数节点
     */
    public static AnalysisNode findOrCreateFunctionNode(String functionSignature, JSFunction jsFunction, AnalysisResult result) {
        try {
            // 首先检查是否已存在该节点
            AnalysisNode existingNode = result.getNodes().get(functionSignature);
            if (existingNode != null) {
                return existingNode;
            }
            
            // 创建新的函数节点
            String functionName = jsFunction.getName() != null ? jsFunction.getName() : "anonymous";
            String packageName = TypeScriptPsiUtils.getPackageName(jsFunction);
            String className = getContainingClassName(jsFunction);
            
            String fullId = packageName + "." + (className != null ? className + "." : "") + functionName;
            String signature = TypeScriptPsiUtils.buildFunctionSignature(jsFunction);
            AnalysisNode functionNode = new AnalysisNode(
                    fullId,
                    className != null ? AnalysisNode.NodeType.METHOD : AnalysisNode.NodeType.FUNCTION,
                    functionName,
                    className,
                    packageName,
                    TypeScriptPsiUtils.getLineNumber(jsFunction),
                    signature,
                    packageName,
                    TypeScriptPsiUtils.getFilePath(jsFunction),
                    TypeScriptPsiUtils.detectLanguage((JSFile) jsFunction.getContainingFile()).getCode()
            );
            result.addNode(functionNode);
            LOG.debug("Created function node: " + fullId);
            
            return functionNode;
        } catch (Exception e) {
            LOG.warn("Failed to find or create function node: " + functionSignature, e);
            return null;
        }
    }

    /**
     * 查找或创建类节点
     */
    public static AnalysisNode findOrCreateClassNode(String classFullId, JSClass jsClass, AnalysisResult result) {
        try {
            // 首先检查是否已存在该节点
            AnalysisNode existingNode = result.getNodes().get(classFullId);
            if (existingNode != null) {
                return existingNode;
            }
            
            // 创建新的类节点
            String className = jsClass.getName();
            if (className == null) {
                return null;
            }
            
            String packageName = TypeScriptPsiUtils.getPackageName(jsClass);
            
            AnalysisNode classNode = new AnalysisNode(
                    classFullId,
                    AnalysisNode.NodeType.CLASS,
                    className,
                    className,
                    packageName,
                    TypeScriptPsiUtils.getLineNumber(jsClass),
                    className + "()",
                    packageName,
                    TypeScriptPsiUtils.getFilePath(jsClass),
                    TypeScriptPsiUtils.detectLanguage((JSFile) jsClass.getContainingFile()).getCode()
            );
            
            result.addNode(classNode);
            LOG.debug("Created class node: " + classFullId);
            
            return classNode;
        } catch (Exception e) {
            LOG.warn("Failed to find or create class node: " + classFullId, e);
            return null;
        }
    }

    /**
     * 获取包含的类名
     */
    private static String getContainingClassName(JSFunction function) {
        JSClass containingClass = PsiTreeUtil.getParentOfType(function, JSClass.class);
        return containingClass != null ? containingClass.getName() : null;
    }
}

