package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;

/**
 * 文件系统解析器
 * 
 * 职责：
 * - 通过文件系统查找文件
 * - 使用 VirtualFileManager 进行文件查找
 * - 支持多种扩展名的查找
 * 
 * <AUTHOR>
 */
public class FileSystemResolver {
    
    private static final Logger LOG = Logger.getInstance(FileSystemResolver.class);
    
    private static final String[] EXTENSIONS = {".ts", ".tsx", ".js", ".jsx", ".vue", ".d.ts"};
    
    /**
     * 通过文件系统解析路径
     * 
     * @param context PSI 上下文元素
     * @param moduleSpecifier 模块说明符
     * @return 解析的文件路径，失败时返回 null
     */
    public String resolve(PsiElement context, String moduleSpecifier) {
        try {
            if (context == null || moduleSpecifier == null) {
                return null;
            }
            
            LOG.debug("File system resolving: " + moduleSpecifier);
            
            // 只处理相对路径
            if (!isRelativePath(moduleSpecifier)) {
                LOG.debug("Not a relative path, skipping file system resolution");
                return null;
            }
            
            PsiFile currentFile = context.getContainingFile();
            if (currentFile == null || currentFile.getVirtualFile() == null) {
                LOG.debug("Cannot get current file");
                return null;
            }
            
            String currentFileAbsolutePath = currentFile.getVirtualFile().getPath();
            String targetAbsolutePath = resolveRelativePathToAbsolute(currentFileAbsolutePath, moduleSpecifier);
            
            if (targetAbsolutePath != null) {
                return findFileInFileSystem(context.getProject(), targetAbsolutePath);
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error in file system resolution: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 在文件系统中查找文件
     * 
     * @param project 项目
     * @param basePath 基础路径
     * @return 找到的文件路径，失败时返回 null
     */
    private String findFileInFileSystem(Project project, String basePath) {
        try {
            if (project == null || basePath == null) {
                return null;
            }
            
            LOG.debug("Searching in file system: " + basePath);
            
            VirtualFileManager fileManager = VirtualFileManager.getInstance();
            PsiManager psiManager = PsiManager.getInstance(project);
            
            // 尝试不同的扩展名
            for (String extension : EXTENSIONS) {
                String pathWithExtension = basePath + extension;
                VirtualFile virtualFile = fileManager.findFileByUrl("file://" + pathWithExtension);
                
                if (virtualFile != null && virtualFile.exists()) {
                    PsiFile psiFile = psiManager.findFile(virtualFile);
                    if (psiFile != null) {
                        LOG.debug("Found file in file system: " + pathWithExtension);
                        return pathWithExtension;
                    }
                }
            }
            
            // 检查是否是目录，查找 index 文件
            for (String extension : EXTENSIONS) {
                String indexPath = basePath + "/index" + extension;
                VirtualFile virtualFile = fileManager.findFileByUrl("file://" + indexPath);
                
                if (virtualFile != null && virtualFile.exists()) {
                    PsiFile psiFile = psiManager.findFile(virtualFile);
                    if (psiFile != null) {
                        LOG.debug("Found index file in file system: " + indexPath);
                        return indexPath;
                    }
                }
            }
            
            LOG.debug("No file found in file system for: " + basePath);
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error searching in file system: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析相对路径为绝对路径
     * 
     * @param currentFilePath 当前文件路径
     * @param relativeModuleSpecifier 相对模块说明符
     * @return 解析的绝对路径
     */
    private String resolveRelativePathToAbsolute(String currentFilePath, String relativeModuleSpecifier) {
        try {
            if (currentFilePath == null || relativeModuleSpecifier == null) {
                return null;
            }
            
            // 获取当前文件的目录
            String currentDir = currentFilePath.substring(0, currentFilePath.lastIndexOf('/'));
            
            // 解析相对路径
            String[] currentParts = currentDir.split("/");
            String[] relativeParts = relativeModuleSpecifier.split("/");
            
            java.util.List<String> resultParts = new java.util.ArrayList<>(java.util.Arrays.asList(currentParts));
            
            for (String part : relativeParts) {
                if (part.equals("..")) {
                    if (!resultParts.isEmpty()) {
                        resultParts.remove(resultParts.size() - 1);
                    }
                } else if (!part.equals(".") && !part.isEmpty()) {
                    resultParts.add(part);
                }
            }
            
            return String.join("/", resultParts);
            
        } catch (Exception e) {
            LOG.debug("Error resolving relative path to absolute: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查是否为相对路径
     * 
     * @param moduleSpecifier 模块说明符
     * @return 是否为相对路径
     */
    private boolean isRelativePath(String moduleSpecifier) {
        return moduleSpecifier != null && 
               (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../"));
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param project 项目
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public boolean fileExists(Project project, String filePath) {
        try {
            if (project == null || filePath == null) {
                return false;
            }
            
            VirtualFileManager fileManager = VirtualFileManager.getInstance();
            VirtualFile virtualFile = fileManager.findFileByUrl("file://" + filePath);
            
            return virtualFile != null && virtualFile.exists();
            
        } catch (Exception e) {
            LOG.debug("Error checking file existence: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取文件的 PsiFile
     * 
     * @param project 项目
     * @param filePath 文件路径
     * @return PsiFile，失败时返回 null
     */
    public PsiFile getPsiFile(Project project, String filePath) {
        try {
            if (project == null || filePath == null) {
                return null;
            }
            
            VirtualFileManager fileManager = VirtualFileManager.getInstance();
            VirtualFile virtualFile = fileManager.findFileByUrl("file://" + filePath);
            
            if (virtualFile != null && virtualFile.exists()) {
                PsiManager psiManager = PsiManager.getInstance(project);
                return psiManager.findFile(virtualFile);
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error getting PsiFile: " + e.getMessage());
            return null;
        }
    }
}
