package com.sankuai.deepcode.astplugin.typescript.export.exporter;

import com.sankuai.deepcode.astplugin.model.AnalysisResult;

/**
 * 包含文件路径信息的分析结果
 * 用于在导出过程中传递完整的文件和分析信息
 *
 * <AUTHOR>
 */
public record AnalysisFileResult(String filePath, AnalysisResult analysisResult) {

    /**
     * 获取相对文件路径
     *
     * @param projectBasePath 项目根路径
     * @return 相对路径
     */
    public String getRelativeFilePath(String projectBasePath) {
        if (projectBasePath != null && filePath.startsWith(projectBasePath)) {
            return filePath.substring(projectBasePath.length() + 1);
        }
        return filePath;
    }

    /**
     * 获取模块名
     *
     * @param projectBasePath 项目根路径
     * @return 模块名
     */
    public String getModuleName(String projectBasePath) {
        String relativeFilePath = getRelativeFilePath(projectBasePath);
        String fileName = relativeFilePath.substring(relativeFilePath.lastIndexOf('/') + 1);
        // 移除 TypeScript/JavaScript 文件扩展名
        if (fileName.endsWith(".ts") || fileName.endsWith(".js")) {
            return fileName.substring(0, fileName.length() - 3);
        } else if (fileName.endsWith(".tsx") || fileName.endsWith(".jsx")) {
            return fileName.substring(0, fileName.length() - 4);
        }
        return fileName;
    }

    /**
     * 获取模块路径
     *
     * @param projectBasePath 项目根路径
     * @return 模块路径
     */
    public String getModulePath(String projectBasePath) {
        String relativeFilePath = getRelativeFilePath(projectBasePath);
        // 移除文件扩展名并转换为模块路径格式
        String modulePathBase = relativeFilePath;
        if (relativeFilePath.endsWith(".ts") || relativeFilePath.endsWith(".js")) {
            modulePathBase = relativeFilePath.substring(0, relativeFilePath.length() - 3);
        } else if (relativeFilePath.endsWith(".tsx") || relativeFilePath.endsWith(".jsx")) {
            modulePathBase = relativeFilePath.substring(0, relativeFilePath.length() - 4);
        }
        return modulePathBase.replace('/', '.');
    }
}

