package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.lang.javascript.psi.JSCallExpression;
import com.intellij.lang.javascript.psi.JSExpression;
import com.intellij.lang.javascript.psi.JSLiteralExpression;
import com.intellij.lang.javascript.psi.JSReferenceExpression;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.sankuai.deepcode.astplugin.model.ImportInfo;
import com.sankuai.deepcode.astplugin.typescript.util.ImportClassifier;
import com.sankuai.deepcode.astplugin.typescript.util.ImportUtils;
import com.sankuai.deepcode.astplugin.typescript.util.TypeScriptPsiUtils;

import java.util.List;

/**
 * CommonJS require() 解析器
 * 
 * 职责：
 * - 解析 CommonJS require 调用
 * - 提取模块说明符
 * - 使用统一的兜底策略
 * 
 * <AUTHOR>
 */
public class CommonJSRequireResolver implements ImportResolver {
    
    private static final Logger LOG = Logger.getInstance(CommonJSRequireResolver.class);
    
    private final PSIResolver psiResolver;
    private final PathResolver pathResolver;
    private final FallbackStrategy fallbackStrategy;
    
    public CommonJSRequireResolver() {
        this.psiResolver = new PSIResolver();
        this.pathResolver = new PathResolver();
        this.fallbackStrategy = new FallbackStrategy();
    }
    
    @Override
    public ImportInfo resolveImport(PsiElement importElement, String filePath) {
        try {
            if (!(importElement instanceof JSCallExpression)) {
                return null;
            }
            
            JSCallExpression callExpression = (JSCallExpression) importElement;
            
            // 检查是否为 require 调用
            if (!isRequireCall(callExpression)) {
                return null;
            }
            
            String importText = callExpression.getText();
            LOG.debug("Resolving CommonJS require: " + importText);
            
            // 提取模块说明符
            String moduleSpecifier = extractModuleSpecifier(callExpression);
            if (moduleSpecifier == null) {
                LOG.warn("Could not extract module specifier from: " + importText);
                return null;
            }
            
            // 创建导入名称列表（require 通常是单个导入）
            List<String> importedNames = List.of(moduleSpecifier);
            
            // 确定导入类型（require 通常是 SINGLE）
            ImportInfo.ImportType type = ImportInfo.ImportType.SINGLE;
            
            // 判断是否为外部模块
            boolean isExternal = ImportClassifier.isExternalModule(moduleSpecifier);
            
            // 获取行号
            int lineNumber = TypeScriptPsiUtils.getLineNumber(callExpression);
            
            // 解析目标文件路径
            String targetFilePath = resolveTargetFilePath(callExpression, moduleSpecifier, isExternal);
            
            return new ImportInfo(importText, lineNumber, type, isExternal, importedNames, filePath, targetFilePath);
            
        } catch (Exception e) {
            LOG.warn("Failed to resolve CommonJS require", e);
            return null;
        }
    }
    
    @Override
    public boolean canResolve(PsiElement importElement) {
        if (!(importElement instanceof JSCallExpression)) {
            return false;
        }
        
        JSCallExpression callExpression = (JSCallExpression) importElement;
        return isRequireCall(callExpression);
    }
    
    @Override
    public String getResolverType() {
        return "CommonJSRequire";
    }
    
    @Override
    public String resolveTargetFilePath(PsiElement importElement, String moduleSpecifier, boolean isExternal) {
        try {
            LOG.debug("[CommonJS] Resolving target file path for: " + moduleSpecifier + " (isExternal: " + isExternal + ")");
            
            // 步骤1: 尝试 PSI 解析
            String psiResolvedPath = psiResolver.resolveTargetFilePath(importElement, moduleSpecifier);
            if (psiResolvedPath != null) {
                LOG.debug("[CommonJS] ✅ PSI resolution successful: " + psiResolvedPath);
                
                // 转换为项目相对路径
                String relativePath = pathResolver.convertToProjectRelative(psiResolvedPath, importElement);
                return relativePath != null ? relativePath : psiResolvedPath;
            }
            
            // 步骤2: 尝试增强文件解析
            String enhancedPath = tryEnhancedResolution(importElement, moduleSpecifier);
            if (enhancedPath != null) {
                LOG.debug("[CommonJS] ✅ Enhanced resolution successful: " + enhancedPath);
                
                String relativePath = pathResolver.convertToProjectRelative(enhancedPath, importElement);
                return relativePath != null ? relativePath : enhancedPath;
            }
            
            // 步骤3: 执行兜底策略
            if (fallbackStrategy.shouldExecuteFallback(moduleSpecifier, isExternal)) {
                String fallbackPath = fallbackStrategy.executeFallback(
                    importElement, moduleSpecifier, isExternal, getResolverType());
                
                if (fallbackPath != null) {
                    LOG.debug("[CommonJS] ✅ Fallback strategy successful: " + fallbackPath);
                    return fallbackPath;
                }
            }
            
            LOG.debug("[CommonJS] ❌ All resolution methods failed for: " + moduleSpecifier);
            return null;
            
        } catch (Exception e) {
            LOG.warn("[CommonJS] Error resolving target file path for: " + moduleSpecifier, e);
            
            // 异常情况下的兜底策略
            if (pathResolver.isRelativePath(moduleSpecifier)) {
                try {
                    String emergencyPath = fallbackStrategy.executeFallback(
                        importElement, moduleSpecifier, isExternal, getResolverType() + "-Emergency");
                    
                    if (emergencyPath != null) {
                        LOG.debug("[CommonJS] ✅ Emergency fallback successful: " + emergencyPath);
                        return emergencyPath;
                    }
                } catch (Exception emergencyException) {
                    LOG.warn("[CommonJS] Emergency fallback also failed", emergencyException);
                }
            }
            
            return null;
        }
    }
    
    /**
     * 检查是否为 require 调用
     */
    private boolean isRequireCall(JSCallExpression callExpression) {
        try {
            JSExpression methodExpression = callExpression.getMethodExpression();
            
            if (methodExpression instanceof JSReferenceExpression) {
                JSReferenceExpression reference = (JSReferenceExpression) methodExpression;
                return "require".equals(reference.getReferenceName());
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Error checking if require call: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 提取模块说明符
     */
    private String extractModuleSpecifier(JSCallExpression callExpression) {
        try {
            JSExpression[] arguments = callExpression.getArguments();
            if (arguments.length == 0) {
                return null;
            }
            
            JSExpression firstArg = arguments[0];
            if (firstArg instanceof JSLiteralExpression) {
                JSLiteralExpression literal = (JSLiteralExpression) firstArg;
                if (literal.isStringLiteral()) {
                    String moduleSpec = literal.getStringValue();
                    if (moduleSpec != null && !moduleSpec.isEmpty()) {
                        LOG.debug("Extracted module specifier via PSI: " + moduleSpec);
                        return moduleSpec;
                    }
                }
            }
            
            // 回退到文本分析
            String callText = callExpression.getText();
            String textResult = ImportUtils.extractModuleSpecifierFromText(callText);
            if (textResult != null && !textResult.isEmpty()) {
                LOG.debug("Extracted module specifier via text: " + textResult);
                return textResult;
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error extracting module specifier: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 尝试增强解析
     */
    private String tryEnhancedResolution(PsiElement importElement, String moduleSpecifier) {
        try {
            ImportUtils.ResourceResolutionResult result = 
                ImportUtils.resolveResourceWithPSI(importElement, moduleSpecifier);
            
            if (result.getAbsolutePath() != null) {
                LOG.debug("Enhanced resolution found: " + result.getAbsolutePath());
                return result.getAbsolutePath();
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error in enhanced resolution: " + e.getMessage());
            return null;
        }
    }
}
