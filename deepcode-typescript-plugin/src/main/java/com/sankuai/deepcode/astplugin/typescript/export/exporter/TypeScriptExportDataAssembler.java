package com.sankuai.deepcode.astplugin.typescript.export.exporter;

import com.intellij.openapi.project.Project;
import com.sankuai.deepcode.astplugin.export.ExportOption;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * TypeScript 导出数据组装器
 * 负责根据导出选项组装并导出数据
 *
 * <AUTHOR>
 */
public class TypeScriptExportDataAssembler {

    /**
     * 组装并导出数据
     *
     * @param project 项目
     * @param analysisFileResults 分析结果列表
     * @param exportOption 导出选项
     * @param outputDirPath 输出目录路径
     * @throws IOException 导出异常
     */
    public static void assembleAndExport(Project project,
                                         List<AnalysisFileResult> analysisFileResults,
                                         ExportOption exportOption,
                                         String outputDirPath) throws IOException {

        // 生成时间戳
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));

        // 获取相应的导出器并执行导出
        TypeScriptDataExporter exporter = TypeScriptExporterFactory.getExporter(exportOption);
        exporter.export(project, analysisFileResults, outputDirPath, timestamp);
    }
}

