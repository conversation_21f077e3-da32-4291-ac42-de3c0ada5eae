package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.psi.PsiElement;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

/**
 * 导入解析器接口
 * 
 * 职责：
 * - 定义统一的导入解析接口
 * - 支持不同类型的导入语句解析
 * - 提供统一的错误处理和兜底策略
 * 
 * <AUTHOR>
 */
public interface ImportResolver {
    
    /**
     * 解析导入语句
     * 
     * @param importElement PSI 导入元素
     * @param filePath 当前文件路径
     * @return 解析后的导入信息，失败时返回 null
     */
    ImportInfo resolveImport(PsiElement importElement, String filePath);
    
    /**
     * 检查是否支持解析指定的 PSI 元素
     * 
     * @param importElement PSI 导入元素
     * @return 是否支持解析
     */
    boolean canResolve(PsiElement importElement);
    
    /**
     * 获取解析器类型名称（用于日志和调试）
     * 
     * @return 解析器类型名称
     */
    String getResolverType();
    
    /**
     * 解析目标文件路径
     * 
     * @param importElement PSI 导入元素
     * @param moduleSpecifier 模块说明符
     * @param isExternal 是否为外部模块
     * @return 目标文件路径，失败时返回 null
     */
    String resolveTargetFilePath(PsiElement importElement, String moduleSpecifier, boolean isExternal);
}
