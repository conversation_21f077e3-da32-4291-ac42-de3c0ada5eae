package com.sankuai.deepcode.astplugin.typescript.export.exporter;

import com.intellij.openapi.project.Project;

import java.io.FileWriter;
import java.io.IOException;
import java.util.List;

/**
 * TypeScript 调用关系导出器
 * 负责将函数调用关系导出为CSV格式
 *
 * <AUTHOR>
 */
public class TypeScriptCallRelationExporter implements TypeScriptDataExporter {

    @Override
    public void export(Project project,
                       List<AnalysisFileResult> analysisFileResults,
                       String outputDirPath,
                       String timestamp) throws IOException {
        String fileName = String.format("TypeScript_Call_Relations_%s.csv", timestamp);
        String filePath = outputDirPath + "/" + fileName;

        try (FileWriter writer = new FileWriter(filePath)) {
            // 写入CSV头部
            writer.append("filePath,caller,callee,lineNumber,isInternal,context\n");

            // 写入数据
            String projectBasePath = project.getBasePath();
            for (AnalysisFileResult fileResult : analysisFileResults) {
                String relativeFilePath = fileResult.getRelativeFilePath(projectBasePath);

                // TODO: 从 AnalysisResult 中提取实际的调用关系数据
                // fileResult.analysisResult().getCallRelations()...
            }
        }
    }

    @Override
    public String getExporterName() {
        return "TypeScript Call Relations (CSV)";
    }

    @Override
    public String getFileExtension() {
        return "csv";
    }
}

