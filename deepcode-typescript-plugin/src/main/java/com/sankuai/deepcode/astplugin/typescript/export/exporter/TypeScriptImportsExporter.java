package com.sankuai.deepcode.astplugin.typescript.export.exporter;

import com.intellij.openapi.project.Project;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * TypeScript Import 导出器
 * 负责将 TypeScript/JavaScript 导入信息导出为 CSV 格式
 *
 * <AUTHOR>
 */
public class TypeScriptImportsExporter implements TypeScriptDataExporter {

    @Override
    public void export(Project project,
                       List<AnalysisFileResult> analysisFileResults,
                       String outputDirPath,
                       String timestamp) throws IOException {
        // 导出CSV格式
        exportCsvFormat(project, analysisFileResults, outputDirPath, timestamp);
    }

    @Override
    public String getExporterName() {
        return "TypeScript Imports Export (CSV)";
    }

    @Override
    public String getFileExtension() {
        return "csv";
    }

    /**
     * 导出CSV格式
     */
    private void exportCsvFormat(Project project,
                                 List<AnalysisFileResult> analysisFileResults,
                                 String outputDirPath,
                                 String timestamp) throws IOException {
        String fileName = String.format("TypeScript_Imports_Export_%s.csv", timestamp);
        String filePath = outputDirPath + "/" + fileName;

        try (FileWriter writer = new FileWriter(filePath)) {
            // 写入CSV头部，添加 targetFilePath 列
            writer.append("filePath,lineNumber,importModule,isExternal,targetFilePath\n");

            // 收集所有导入记录并按行号排序
            List<ImportRecord> importRecords = new ArrayList<>();
            String projectBasePath = project.getBasePath();

            for (AnalysisFileResult fileResult : analysisFileResults) {
                String relativeFilePath = fileResult.getRelativeFilePath(projectBasePath);
                List<ImportInfo> imports = fileResult.analysisResult().getImports();

                for (ImportInfo importInfo : imports) {
                    List<String> resolvedClasses = importInfo.getResolvedClasses();

                    if (resolvedClasses.isEmpty()) {
                        // 如果没有resolvedClasses，使用导入语句本身作为importModule
                        String importModule = extractImportModule(importInfo.getStatement());
                        importRecords.add(new ImportRecord(
                                relativeFilePath,
                                importInfo.getLineNumber(),
                                importModule,
                                importInfo.isExternal(),
                                importInfo.getTargetFilePath()
                        ));
                    } else {
                        // 如果有resolvedClasses，为每个类创建一条记录
                        for (String resolvedClass : resolvedClasses) {
                            importRecords.add(new ImportRecord(
                                    relativeFilePath,
                                    importInfo.getLineNumber(),
                                    resolvedClass,
                                    importInfo.isExternal(),
                                    importInfo.getTargetFilePath()
                            ));
                        }
                    }
                }
            }

            // 按行号升序排列
            importRecords.sort(Comparator.comparingInt(ImportRecord::lineNumber));

            // 写入CSV数据，包含 targetFilePath
            for (ImportRecord record : importRecords) {
                writer.append(String.format("%s,%d,%s,%s,%s\n",
                        escapeCsv(record.filePath()),
                        record.lineNumber(),
                        escapeCsv(record.importModule()),
                        record.isExternal() ? "true" : "false",
                        escapeCsv(record.targetFilePath() != null ? record.targetFilePath() : "")
                ));
            }
        }
    }

    /**
     * 从导入语句中提取模块名
     */
    private String extractImportModule(String statement) {
        if (statement == null || statement.trim().isEmpty()) {
            return "";
        }

        String trimmed = statement.trim();

        // 处理 ES6 import 格式
        if (trimmed.startsWith("import ")) {
            // 提取引号中的模块路径
            int fromIndex = trimmed.lastIndexOf(" from ");
            if (fromIndex > 0) {
                String modulePart = trimmed.substring(fromIndex + 6).trim();
                return extractQuotedString(modulePart);
            }

            // 处理 import "module" 格式
            String importPart = trimmed.substring(6).trim();
            if (importPart.startsWith("'") || importPart.startsWith("\"")) {
                return extractQuotedString(importPart);
            }
        }

        // 处理 CommonJS require 格式
        if (trimmed.contains("require(")) {
            int requireIndex = trimmed.indexOf("require(");
            int startQuote = -1;
            for (int i = requireIndex + 8; i < trimmed.length(); i++) {
                char c = trimmed.charAt(i);
                if (c == '\'' || c == '"') {
                    startQuote = i;
                    break;
                }
            }
            if (startQuote > 0) {
                char quoteChar = trimmed.charAt(startQuote);
                int endQuote = trimmed.indexOf(quoteChar, startQuote + 1);
                if (endQuote > startQuote) {
                    return trimmed.substring(startQuote + 1, endQuote);
                }
            }
        }

        return trimmed;
    }

    /**
     * 提取引号中的字符串
     */
    private String extractQuotedString(String str) {
        if (str == null || str.length() < 2) {
            return str;
        }

        str = str.trim();
        char firstChar = str.charAt(0);
        if ((firstChar == '\'' || firstChar == '"') && str.length() > 1) {
            int endQuote = str.lastIndexOf(firstChar);
            if (endQuote > 0) {
                return str.substring(1, endQuote);
            }
        }
        return str;
    }

    /**
     * 导入记录数据类，包含 targetFilePath
     */
    private record ImportRecord(String filePath, int lineNumber, String importModule, boolean isExternal, String targetFilePath) {
    }
}

