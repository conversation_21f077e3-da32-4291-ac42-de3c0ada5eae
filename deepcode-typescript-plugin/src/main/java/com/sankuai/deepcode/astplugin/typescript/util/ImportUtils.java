package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

/**
 * 导入分析工具类 - 提供基础的辅助方法
 * <AUTHOR>
 */
public final class ImportUtils {

    private static final Logger LOG = Logger.getInstance(ImportUtils.class);

    private ImportUtils() {
        // 工具类不允许实例化
    }

    /**
     * 检查是否为重复导入
     */
    public static boolean isDuplicateImport(AnalysisResult result, ImportInfo newImport) {
        if (result == null || newImport == null) {
            return false;
        }

        for (ImportInfo existingImport : result.getImports()) {
            // 比较导入语句文本，去除空白字符进行规范化比较
            String existingStatement = existingImport.getStatement().trim().replaceAll("\\s+", " ");
            String newStatement = newImport.getStatement().trim().replaceAll("\\s+", " ");

            if (existingStatement.equals(newStatement)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从文本中提取模块路径
     */
    public static String extractModuleSpecifierFromText(String importText) {
        try {
            // 优先检查 "from" 关键字的导入
            int fromIndex = importText.indexOf("from");
            if (fromIndex > 0) {
                String fromPart = importText.substring(fromIndex + 4).trim();
                if (fromPart.startsWith("\"") || fromPart.startsWith("'")) {
                    char quote = fromPart.charAt(0);
                    int endQuote = fromPart.indexOf(quote, 1);
                    if (endQuote > 0) {
                        return fromPart.substring(1, endQuote);
                    }
                }
            }

            // 检查 require 调用
            if (importText.contains("require(")) {
                int start = importText.indexOf("require(") + 8;
                String part = importText.substring(start).trim();
                if (part.startsWith("\"") || part.startsWith("'")) {
                    char quote = part.charAt(0);
                    int endQuote = part.indexOf(quote, 1);
                    if (endQuote > 0) {
                        return part.substring(1, endQuote);
                    }
                }
            }

            // 检查没有 "from" 的直接导入，如 import './styles.css';
            if (importText.trim().startsWith("import ") && !importText.contains("from")) {
                String importPart = importText.trim().substring(7).trim(); // 移除 "import "
                if (importPart.startsWith("\"") || importPart.startsWith("'")) {
                    char quote = importPart.charAt(0);
                    int endQuote = importPart.indexOf(quote, 1);
                    if (endQuote > 0) {
                        return importPart.substring(1, endQuote);
                    }
                }
            }

            // 检查动态导入 import() 调用
            if (importText.contains("import(")) {
                int start = importText.indexOf("import(") + 7;
                String part = importText.substring(start).trim();
                if (part.startsWith("\"") || part.startsWith("'")) {
                    char quote = part.charAt(0);
                    int endQuote = part.indexOf(quote, 1);
                    if (endQuote > 0) {
                        return part.substring(1, endQuote);
                    }
                }
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 判断是否为 CSS 或样式文件导入
     */
    public static boolean isCssOrStyleImport(String importText) {
        if (importText == null) return false;

        // 提取模块路径
        String moduleSpecifier = extractModuleSpecifierFromText(importText);
        if (moduleSpecifier == null) return false;

        // 检查文件扩展名
        return moduleSpecifier.endsWith(".css") ||
               moduleSpecifier.endsWith(".scss") ||
               moduleSpecifier.endsWith(".sass") ||
               moduleSpecifier.endsWith(".less") ||
               moduleSpecifier.endsWith(".styl") ||
               moduleSpecifier.endsWith(".stylus");
    }

    /**
     * 判断是否为资源文件导入（图片、字体、音视频等）
     */
    public static boolean isAssetImport(String importText) {
        if (importText == null) {
            return false;
        }

        // 提取模块路径
        String moduleSpecifier = extractModuleSpecifierFromText(importText);
        if (moduleSpecifier == null) {
            return false;
        }

        // 图片文件扩展名
        if (moduleSpecifier.endsWith(".png") || moduleSpecifier.endsWith(".jpg") ||
            moduleSpecifier.endsWith(".jpeg") || moduleSpecifier.endsWith(".gif") ||
            moduleSpecifier.endsWith(".bmp") || moduleSpecifier.endsWith(".webp") ||
            moduleSpecifier.endsWith(".svg") || moduleSpecifier.endsWith(".ico") ||
            moduleSpecifier.endsWith(".tiff") || moduleSpecifier.endsWith(".tif")) {
            return true;
        }

        // 字体文件扩展名
        if (moduleSpecifier.endsWith(".woff") || moduleSpecifier.endsWith(".woff2") ||
            moduleSpecifier.endsWith(".ttf") || moduleSpecifier.endsWith(".eot") ||
            moduleSpecifier.endsWith(".otf")) {
            return true;
        }

        // 音视频文件扩展名
        if (moduleSpecifier.endsWith(".mp3") || moduleSpecifier.endsWith(".mp4") ||
            moduleSpecifier.endsWith(".avi") || moduleSpecifier.endsWith(".mov") ||
            moduleSpecifier.endsWith(".wmv") || moduleSpecifier.endsWith(".webm") ||
            moduleSpecifier.endsWith(".ogg") || moduleSpecifier.endsWith(".wav") ||
            moduleSpecifier.endsWith(".flac")) {
            return true;
        }

        // 文档文件扩展名
        if (moduleSpecifier.endsWith(".pdf") || moduleSpecifier.endsWith(".doc") ||
            moduleSpecifier.endsWith(".docx") || moduleSpecifier.endsWith(".txt") ||
            moduleSpecifier.endsWith(".md")) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否为静态资源文件导入（包括 CSS 和其他资源）
     */
    public static boolean isStaticResourceImport(String importText) {
        return isCssOrStyleImport(importText) || isAssetImport(importText);
    }

    /**
     * 从文件路径中提取文件名（不含扩展名）
     */
    public static String extractFileNameWithoutExtension(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "unknown";
        }

        // 获取最后一个路径分隔符后的文件名
        String fileName = filePath;
        int lastSlash = filePath.lastIndexOf('/');
        if (lastSlash >= 0) {
            fileName = filePath.substring(lastSlash + 1);
        }

        // 移除文件扩展名
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            fileName = fileName.substring(0, lastDot);
        }

        return fileName.isEmpty() ? "unknown" : fileName;
    }

    /**
     * 清理模块路径（移除引号等）
     */
    public static String cleanModuleSpecifier(String raw) {
        if (raw == null) return null;
        return raw.replaceAll("['\"]", "").trim();
    }

    /**
     * 获取项目根目录
     */
    public static VirtualFile getProjectRoot(PsiElement element) {
        try {
            // 优先使用项目文件的父目录
            if (element.getProject().getProjectFile() != null) {
                return element.getProject().getProjectFile().getParent();
            }

            // 使用现代的 getBasePath() 方法
            String basePath = element.getProject().getBasePath();
            if (basePath != null) {
                return com.intellij.openapi.vfs.LocalFileSystem.getInstance().findFileByPath(basePath);
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Failed to get project root: " + e.getMessage());
            return null;
        }
    }

    /**
     * 使用 PSI 能力解析资源文件路径并判断是否为项目内部文件
     */
    public static ResourceResolutionResult resolveResourceWithPSI(PsiElement element, String moduleSpecifier) {
        if (element == null || moduleSpecifier == null) {
            return new ResourceResolutionResult(moduleSpecifier, false, null);
        }

        try {
            LOG.debug("=== PSI Resource Path Resolution ===");
            LOG.debug("Original module specifier: " + moduleSpecifier);

            // 获取当前文件的虚拟文件
            VirtualFile currentFile = element.getContainingFile().getVirtualFile();
            if (currentFile == null) {
                LOG.debug("Current file virtual file is null");
                return new ResourceResolutionResult(moduleSpecifier, false, null);
            }

            LOG.debug("Current file path: " + currentFile.getPath());

            // 获取项目根目录
            VirtualFile projectRoot = getProjectRoot(element);
            if (projectRoot == null) {
                LOG.debug("Project root is null");
                return new ResourceResolutionResult(moduleSpecifier, false, null);
            }

            String projectPath = projectRoot.getPath();
            LOG.debug("Project root path: " + projectPath);

            // 尝试通过PSI解析模块引用获得实际文件路径
            VirtualFile resolvedFile = resolveModuleFile(element, moduleSpecifier, currentFile, projectRoot);

            if (resolvedFile != null) {
                String absolutePath = resolvedFile.getPath();
                LOG.debug("PSI resolved absolute path: " + absolutePath);

                // 判断文件是否在项目内部
                boolean isInternal = absolutePath.startsWith(projectPath);

                // 计算相对于项目根的路径
                String relativePath = moduleSpecifier;
                if (isInternal) {
                    relativePath = absolutePath.substring(projectPath.length());
                    if (relativePath.startsWith("/")) {
                        relativePath = relativePath.substring(1);
                    }
                }

                LOG.debug("Resolved path: " + relativePath + " (internal: " + isInternal + ")");
                return new ResourceResolutionResult(relativePath, isInternal, absolutePath);
            }

            // 如果PSI解析失败，回退到文本路径分析
            return fallbackPathAnalysis(moduleSpecifier, projectPath);

        } catch (Exception e) {
            LOG.debug("Failed to resolve resource path with PSI: " + e.getMessage());
            return fallbackPathAnalysis(moduleSpecifier, null);
        }
    }

    /**
     * 尝试通过PSI解析模块文件
     */
    private static VirtualFile resolveModuleFile(PsiElement element, String moduleSpecifier,
                                               VirtualFile currentFile, VirtualFile projectRoot) {
        try {
            // 方法1: 如果是相对路径，使用增强的相对路径解析
            if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
                VirtualFile resolved = resolveRelativeModulePath(currentFile, moduleSpecifier);
                if (resolved != null) {
                    LOG.debug("Resolved via enhanced relative path: " + resolved.getPath());
                    return resolved;
                }
            }

            // 方法2: 尝试通过 IntelliJ 的增强模块解析系统（处理所有类型的别名）
            VirtualFile resolved = tryIntellijModuleResolution(element, moduleSpecifier);
            if (resolved != null) {
                return resolved;
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Failed to resolve module file: " + e.getMessage());
            return null;
        }
    }

    /**
     * 增强的相对路径解析，支持多级相对路径和文件扩展名自动补全
     */
    private static VirtualFile resolveRelativeModulePath(VirtualFile currentFile, String moduleSpecifier) {
        try {
            VirtualFile currentDir = currentFile.getParent();
            if (currentDir == null) {
                LOG.debug("Current file has no parent directory");
                return null;
            }

            LOG.debug("Resolving relative path: " + moduleSpecifier + " from " + currentDir.getPath());

            // 方法1: 直接尝试解析路径
            VirtualFile resolved = currentDir.findFileByRelativePath(moduleSpecifier);
            if (resolved != null && resolved.exists()) {
                LOG.debug("Direct resolution successful: " + resolved.getPath());
                return resolved;
            }

            // 方法2: 尝试添加常见的 TypeScript/JavaScript 文件扩展名
            String[] extensions = {".ts", ".tsx", ".js", ".jsx", ".d.ts"};
            for (String ext : extensions) {
                resolved = currentDir.findFileByRelativePath(moduleSpecifier + ext);
                if (resolved != null && resolved.exists()) {
                    LOG.debug("Resolution with extension " + ext + " successful: " + resolved.getPath());
                    return resolved;
                }
            }

            // 方法3: 尝试解析为目录并查找 index 文件
            resolved = currentDir.findFileByRelativePath(moduleSpecifier);
            if (resolved != null && resolved.isDirectory()) {
                LOG.debug("Found directory, looking for index files: " + resolved.getPath());

                // 查找 index 文件的各种变体
                String[] indexFiles = {"index.ts", "index.tsx", "index.js", "index.jsx", "index.d.ts"};
                for (String indexFile : indexFiles) {
                    VirtualFile indexResolved = resolved.findChild(indexFile);
                    if (indexResolved != null && indexResolved.exists()) {
                        LOG.debug("Found index file: " + indexResolved.getPath());
                        return indexResolved;
                    }
                }
            }

            // 方法4: 手动解析复杂的相对路径（处理 findFileByRelativePath 可能失败的情况）
            resolved = manuallyResolveRelativePath(currentDir, moduleSpecifier);
            if (resolved != null) {
                LOG.debug("Manual resolution successful: " + resolved.getPath());
                return resolved;
            }

            LOG.debug("Failed to resolve relative path: " + moduleSpecifier);
            return null;

        } catch (Exception e) {
            LOG.debug("Error resolving relative module path: " + e.getMessage());
            return null;
        }
    }

    /**
     * 手动解析相对路径，处理复杂的多级路径
     */
    private static VirtualFile manuallyResolveRelativePath(VirtualFile baseDir, String relativePath) {
        try {
            VirtualFile current = baseDir;

            // 分割路径
            String[] pathParts = relativePath.split("/");

            for (String part : pathParts) {
                if (part.isEmpty()) {
                    continue;
                }

                if ("..".equals(part)) {
                    // 向上一级目录
                    current = current.getParent();
                    if (current == null) {
                        LOG.debug("Cannot go up from root directory");
                        return null;
                    }
                } else if (".".equals(part)) {
                    // 当前目录，不需要操作
                    continue;
                } else {
                    // 进入子目录或文件
                    VirtualFile child = current.findChild(part);
                    if (child != null && child.exists()) {
                        current = child;
                    } else {
                        // 尝试添加扩展名
                        String[] extensions = {".ts", ".tsx", ".js", ".jsx", ".d.ts"};
                        boolean found = false;
                        for (String ext : extensions) {
                            child = current.findChild(part + ext);
                            if (child != null && child.exists()) {
                                current = child;
                                found = true;
                                break;
                            }
                        }

                        if (!found) {
                            LOG.debug("Cannot find path part: " + part + " in " + current.getPath());
                            return null;
                        }
                    }
                }
            }

            return current;

        } catch (Exception e) {
            LOG.debug("Error in manual path resolution: " + e.getMessage());
            return null;
        }
    }

    /**
     * 尝试使用 IntelliJ 内置的模块解析
     */
    private static VirtualFile tryIntellijModuleResolution(PsiElement element, String moduleSpecifier) {
        try {
            // 方法1: 尝试解析常见的别名模式
            VirtualFile resolved = tryCommonAliasPatterns(element, moduleSpecifier);
            if (resolved != null) {
                return resolved;
            }

            // 方法2: 尝试通过 tsconfig.json 或 webpack.config.js 解析路径映射
            resolved = tryConfigBasedResolution(element, moduleSpecifier);
            if (resolved != null) {
                return resolved;
            }

            // 方法3: 尝试通过 IntelliJ 的引用解析
            resolved = tryPsiReferenceResolution(element, moduleSpecifier);
            if (resolved != null) {
                return resolved;
            }

            LOG.debug("IntelliJ module resolution failed for: " + moduleSpecifier);
            return null;
        } catch (Exception e) {
            LOG.debug("IntelliJ module resolution failed: " + e.getMessage());
            return null;
        }
    }

    /**
     * 尝试解析常见的别名模式
     */
    private static VirtualFile tryCommonAliasPatterns(PsiElement element, String moduleSpecifier) {
        String methodMsg = "=== tryCommonAliasPatterns() for: " + moduleSpecifier + " ===";
        LOG.debug(methodMsg);

        try {
            VirtualFile projectRoot = getProjectRoot(element);
            if (projectRoot == null) {
                String noRootMsg = "Project root is null, cannot resolve alias";
                LOG.debug(noRootMsg);
                return null;
            }

            String rootPathMsg = "Project root: " + projectRoot.getPath();
            LOG.debug(rootPathMsg);

            // 1. @/ 别名 (通常指向 src 目录)
            if (moduleSpecifier.startsWith("@/")) {
                String standardAliasMsg = "Processing standard @/ alias";
                LOG.debug(standardAliasMsg);

                String aliasPath = moduleSpecifier.substring(2);
                VirtualFile srcDir = projectRoot.findChild("src");
                if (srcDir != null) {
                    VirtualFile resolved = findFileWithExtensions(srcDir, aliasPath);
                    if (resolved != null) {
                        String resolvedMsg = "Resolved @/ alias: " + resolved.getPath();
                        LOG.debug(resolvedMsg);
                        return resolved;
                    }
                }
                // 如果 src 目录不存在，尝试直接在项目根目录查找
                VirtualFile resolved = findFileWithExtensions(projectRoot, aliasPath);
                if (resolved != null) {
                    LOG.debug("Resolved @/ alias in project root: " + resolved.getPath());
                    return resolved;
                }
            }

            // 2. ~/ 别名 (另一种常见的项目根别名)
            if (moduleSpecifier.startsWith("~/")) {
                String aliasPath = moduleSpecifier.substring(2);
                VirtualFile resolved = findFileWithExtensions(projectRoot, aliasPath);
                if (resolved != null) {
                    LOG.debug("Resolved ~/ alias: " + resolved.getPath());
                    return resolved;
                }
            }

            // 3. 其他自定义别名模式 (如 @page/, @components/, @utils/ 等)
            if (moduleSpecifier.startsWith("@") && moduleSpecifier.contains("/")) {
                String customAliasMsg = "Processing custom alias: " + moduleSpecifier;
                LOG.debug(customAliasMsg);

                VirtualFile customResolved = tryCustomAliasResolution(projectRoot, moduleSpecifier);
                if (customResolved != null) {
                    String customResolvedMsg = "Custom alias resolved: " + customResolved.getPath();
                    LOG.debug(customResolvedMsg);
                    return customResolved;
                } else {
                    String customFailedMsg = "Custom alias resolution failed for: " + moduleSpecifier;
                    LOG.debug(customFailedMsg);
                }
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error in common alias pattern resolution: " + e.getMessage());
            return null;
        }
    }

    /**
     * 尝试自定义别名解析
     */
    private static VirtualFile tryCustomAliasResolution(VirtualFile projectRoot, String moduleSpecifier) {
        String methodMsg = "=== tryCustomAliasResolution() for: " + moduleSpecifier + " ===";
        LOG.debug(methodMsg);

        try {
            // 常见的别名映射模式
            String[] commonSrcDirs = {"src", "static", "app", "lib", "source"};
            String srcDirsMsg = "Will search in directories: " + String.join(", ", commonSrcDirs);
            LOG.debug(srcDirsMsg);

            int slashIndex = moduleSpecifier.indexOf("/");
            if (slashIndex > 1) {
                String aliasPrefix = moduleSpecifier.substring(1, slashIndex); // 移除 @ 符号
                String remainingPath = moduleSpecifier.substring(slashIndex + 1);

                String parseMsg = "Parsed alias - prefix: '" + aliasPrefix + "', remaining: '" + remainingPath + "'";
                LOG.debug(parseMsg);

                // 尝试在常见目录中查找
                for (String srcDirName : commonSrcDirs) {
                    String searchingMsg = "Searching in directory: " + srcDirName;
                    LOG.debug(searchingMsg);

                    VirtualFile srcDir = projectRoot.findChild(srcDirName);
                    if (srcDir != null) {
                        String foundSrcMsg = "Found source directory: " + srcDir.getPath();
                        LOG.debug(foundSrcMsg);

                        // 直接查找别名目录
                        VirtualFile aliasDir = srcDir.findChild(aliasPrefix);
                        if (aliasDir != null) {
                            String foundAliasMsg = "Found alias directory: " + aliasDir.getPath();
                            LOG.debug(foundAliasMsg);

                            VirtualFile resolved = findFileWithExtensions(aliasDir, remainingPath);
                            if (resolved != null) {
                                String resolvedMsg = "Resolved custom alias @" + aliasPrefix + "/" + remainingPath + " in " + srcDirName + ": " + resolved.getPath();
                                LOG.debug(resolvedMsg);
                                return resolved;
                            }
                        }
                    }
                }
            }

            return null;
        } catch (Exception e) {
            LOG.debug("Error in custom alias resolution: " + e.getMessage());
            return null;
        }
    }

    /**
     * 查找带扩展名的文件
     */
    private static VirtualFile findFileWithExtensions(VirtualFile baseDir, String filePath) {
        if (baseDir == null || filePath == null) {
            return null;
        }

        // 常见的 TypeScript/JavaScript 扩展名
        String[] extensions = {"", ".ts", ".tsx", ".js", ".jsx", ".vue", ".d.ts"};

        for (String ext : extensions) {
            VirtualFile file = baseDir.findFileByRelativePath(filePath + ext);
            if (file != null && file.exists() && !file.isDirectory()) {
                return file;
            }
        }

        // 如果是目录，尝试查找 index 文件
        VirtualFile dir = baseDir.findFileByRelativePath(filePath);
        if (dir != null && dir.isDirectory()) {
            String[] indexNames = {"index.ts", "index.tsx", "index.js", "index.jsx", "index.vue"};
            for (String indexName : indexNames) {
                VirtualFile indexFile = dir.findChild(indexName);
                if (indexFile != null && indexFile.exists()) {
                    return indexFile;
                }
            }
        }

        return null;
    }

    /**
     * 尝试基于配置文件的解析 (tsconfig.json, webpack.config.js 等)
     */
    private static VirtualFile tryConfigBasedResolution(PsiElement element, String moduleSpecifier) {
        try {
            // TODO: 实现 tsconfig.json paths 解析
            // TODO: 实现 webpack.config.js alias 解析
            LOG.debug("Config-based resolution not yet implemented for: " + moduleSpecifier);
            return null;
        } catch (Exception e) {
            LOG.debug("Error in config-based resolution: " + e.getMessage());
            return null;
        }
    }

    /**
     * 尝试通过 PSI 引用解析
     */
    private static VirtualFile tryPsiReferenceResolution(PsiElement element, String moduleSpecifier) {
        try {
            // TODO: 使用 IntelliJ 的引用解析 API
            LOG.debug("PSI reference resolution not yet implemented for: " + moduleSpecifier);
            return null;
        } catch (Exception e) {
            LOG.debug("Error in PSI reference resolution: " + e.getMessage());
            return null;
        }
    }

    /**
     * 回退到简单的路径分析
     */
    private static ResourceResolutionResult fallbackPathAnalysis(String moduleSpecifier, String projectPath) {
        // 基于路径模式判断是否可能是内部文件
        boolean likelyInternal = false;

        if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../") ||
            moduleSpecifier.startsWith("@/") || moduleSpecifier.startsWith("~/") ||
            (moduleSpecifier.startsWith("@") && moduleSpecifier.contains("/"))) {
            likelyInternal = true;
        }

        LOG.debug("Fallback analysis: " + moduleSpecifier + " likely internal: " + likelyInternal);
        return new ResourceResolutionResult(moduleSpecifier, likelyInternal, null);
    }

    /**
     * 资源解析结果包装类
     */
    public static class ResourceResolutionResult {
        private final String resolvedPath;
        private final boolean isInternal;
        private final String absolutePath;

        public ResourceResolutionResult(String resolvedPath, boolean isInternal, String absolutePath) {
            this.resolvedPath = resolvedPath;
            this.isInternal = isInternal;
            this.absolutePath = absolutePath;
        }

        public String getResolvedPath() {
            return resolvedPath;
        }

        public boolean isInternal() {
            return isInternal;
        }

        public String getAbsolutePath() {
            return absolutePath;
        }
    }

    /**
     * 使用 PSI 能力解析资源文件路径（向后兼容方法）
     */
    public static String resolveResourcePathWithPSI(PsiElement element, String moduleSpecifier) {
        ResourceResolutionResult result = resolveResourceWithPSI(element, moduleSpecifier);
        return result.getResolvedPath();
    }

    /**
     * 判断文本行是否为导入语句 - 保留作为回退方法
     */
    public static boolean isImportLine(String line) {
        if (line == null || line.trim().isEmpty()) {
            return false;
        }

        line = line.trim();

        // ES6/TypeScript import
        if (line.startsWith("import ")) {
            return true;
        }

        // CommonJS require
        if (line.contains("require(") &&
            (line.startsWith("const ") || line.startsWith("var ") || line.startsWith("let "))) {
            return true;
        }

        return false;
    }

    /**
     * 基于 PSI 判断元素是否为导入相关 - 优先使用的方法
     */
    public static boolean isImportRelatedElementPSI(PsiElement element) {
        try {
            // 直接检查 PSI 类型
            if (element instanceof com.intellij.lang.javascript.psi.ecma6.TypeScriptImportStatement) {
                return true;
            }

            if (element instanceof com.intellij.lang.ecmascript6.psi.ES6ImportDeclaration) {
                return true;
            }

            // 检查其他导入相关类型
            String className = element.getClass().getSimpleName();
            return className.contains("Import") && !className.contains("ImportError");

        } catch (Exception e) {
            LOG.debug("Failed to check PSI import element: " + e.getMessage());
            return false;
        }
    }
}

