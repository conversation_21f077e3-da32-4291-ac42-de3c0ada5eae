package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;

/**
 * 紧急路径生成器
 * 
 * 职责：
 * - 在所有解析方法失败时生成兜底路径
 * - 确保永不返回 null
 * - 基于模块说明符生成合理的路径
 * 
 * <AUTHOR>
 */
public class EmergencyPathGenerator {
    
    private static final Logger LOG = Logger.getInstance(EmergencyPathGenerator.class);
    
    private final PathResolver pathResolver;
    
    public EmergencyPathGenerator(PathResolver pathResolver) {
        this.pathResolver = pathResolver;
    }
    
    /**
     * 生成紧急路径（永不返回 null）
     * 
     * @param context PSI 上下文元素
     * @param moduleSpecifier 模块说明符
     * @return 生成的紧急路径
     */
    public String generate(PsiElement context, String moduleSpecifier) {
        try {
            LOG.debug("Generating emergency path for: " + moduleSpecifier);
            
            if (moduleSpecifier == null) {
                return "unknown.ts";
            }
            
            // 对于相对路径，尝试基于当前文件生成
            if (pathResolver.isRelativePath(moduleSpecifier)) {
                return generateForRelativePath(context, moduleSpecifier);
            } else {
                return generateForAbsolutePath(moduleSpecifier);
            }
            
        } catch (Exception e) {
            LOG.debug("Error generating emergency path: " + e.getMessage());
            return moduleSpecifier + ".ts"; // 最终兜底
        }
    }
    
    /**
     * 为相对路径生成紧急路径
     * 
     * @param context PSI 上下文元素
     * @param relativeModuleSpecifier 相对模块说明符
     * @return 生成的路径
     */
    private String generateForRelativePath(PsiElement context, String relativeModuleSpecifier) {
        try {
            LOG.debug("Generating emergency path for relative: " + relativeModuleSpecifier);
            
            // 尝试基于当前文件位置生成
            if (context != null && context.getContainingFile() != null && 
                context.getContainingFile().getVirtualFile() != null) {
                
                String currentFilePath = context.getContainingFile().getVirtualFile().getPath();
                String resolved = pathResolver.resolveRelativeToAbsolute(currentFilePath, relativeModuleSpecifier);
                
                if (resolved != null) {
                    String withExtension = pathResolver.inferExtension(resolved);
                    LOG.debug("Generated emergency path from current file: " + withExtension);
                    return withExtension;
                }
            }
            
            // 兜底：直接基于相对路径生成
            String withExtension = pathResolver.inferExtension(relativeModuleSpecifier);
            LOG.debug("Generated emergency path directly: " + withExtension);
            return withExtension;
            
        } catch (Exception e) {
            LOG.debug("Error generating emergency path for relative: " + e.getMessage());
            return pathResolver.inferExtension(relativeModuleSpecifier);
        }
    }
    
    /**
     * 为绝对路径生成紧急路径
     * 
     * @param moduleSpecifier 模块说明符
     * @return 生成的路径
     */
    private String generateForAbsolutePath(String moduleSpecifier) {
        try {
            LOG.debug("Generating emergency path for absolute: " + moduleSpecifier);
            
            // 对于别名路径
            if (moduleSpecifier.startsWith("@/")) {
                String relativePath = moduleSpecifier.substring(2);
                String result = "src/" + relativePath;
                String withExtension = pathResolver.inferExtension(result);
                LOG.debug("Generated emergency path for alias: " + withExtension);
                return withExtension;
            }
            
            // 对于 src/ 开头的路径
            if (moduleSpecifier.startsWith("src/")) {
                String withExtension = pathResolver.inferExtension(moduleSpecifier);
                LOG.debug("Generated emergency path for src: " + withExtension);
                return withExtension;
            }
            
            // 对于简单模块名（没有路径分隔符）
            if (!moduleSpecifier.contains("/")) {
                String result = "src/" + moduleSpecifier;
                String withExtension = pathResolver.inferExtension(result);
                LOG.debug("Generated emergency path for simple module: " + withExtension);
                return withExtension;
            }
            
            // 对于其他情况，直接使用
            String withExtension = pathResolver.inferExtension(moduleSpecifier);
            LOG.debug("Generated emergency path for other case: " + withExtension);
            return withExtension;
            
        } catch (Exception e) {
            LOG.debug("Error generating emergency path for absolute: " + e.getMessage());
            return pathResolver.inferExtension(moduleSpecifier);
        }
    }
    
    /**
     * 生成基于路径特征的紧急路径
     * 
     * @param moduleSpecifier 模块说明符
     * @return 生成的路径
     */
    public String generateByPathFeatures(String moduleSpecifier) {
        try {
            if (moduleSpecifier == null) {
                return "unknown.ts";
            }
            
            LOG.debug("Generating emergency path by features: " + moduleSpecifier);
            
            String lowerSpecifier = moduleSpecifier.toLowerCase();
            
            // 根据路径特征推断目录结构
            String basePath = moduleSpecifier;
            
            // 如果不是以常见目录开头，添加 src/ 前缀
            if (!moduleSpecifier.startsWith("src/") && 
                !moduleSpecifier.startsWith("lib/") && 
                !moduleSpecifier.startsWith("app/") && 
                !moduleSpecifier.startsWith("components/") &&
                !pathResolver.isRelativePath(moduleSpecifier)) {
                
                if (lowerSpecifier.contains("component")) {
                    basePath = "src/components/" + moduleSpecifier;
                } else if (lowerSpecifier.contains("page")) {
                    basePath = "src/pages/" + moduleSpecifier;
                } else if (lowerSpecifier.contains("util") || lowerSpecifier.contains("helper")) {
                    basePath = "src/utils/" + moduleSpecifier;
                } else if (lowerSpecifier.contains("service")) {
                    basePath = "src/services/" + moduleSpecifier;
                } else if (lowerSpecifier.contains("type") || lowerSpecifier.contains("interface")) {
                    basePath = "src/types/" + moduleSpecifier;
                } else {
                    basePath = "src/" + moduleSpecifier;
                }
            }
            
            String withExtension = pathResolver.inferExtension(basePath);
            LOG.debug("Generated emergency path by features: " + withExtension);
            return withExtension;
            
        } catch (Exception e) {
            LOG.debug("Error generating emergency path by features: " + e.getMessage());
            return moduleSpecifier + ".ts";
        }
    }
    
    /**
     * 生成最小化的紧急路径（最终兜底）
     * 
     * @param moduleSpecifier 模块说明符
     * @return 生成的最小化路径
     */
    public String generateMinimal(String moduleSpecifier) {
        if (moduleSpecifier == null || moduleSpecifier.isEmpty()) {
            return "unknown.ts";
        }
        
        try {
            // 移除特殊字符，只保留字母、数字、斜杠、点、下划线、连字符
            String cleaned = moduleSpecifier.replaceAll("[^a-zA-Z0-9/./_-]", "");
            
            if (cleaned.isEmpty()) {
                return "unknown.ts";
            }
            
            // 如果没有扩展名，添加 .ts
            if (!cleaned.matches(".*\\.(ts|tsx|js|jsx|vue|d\\.ts)$")) {
                cleaned += ".ts";
            }
            
            LOG.debug("Generated minimal emergency path: " + cleaned);
            return cleaned;
            
        } catch (Exception e) {
            LOG.debug("Error generating minimal emergency path: " + e.getMessage());
            return "unknown.ts";
        }
    }
}
