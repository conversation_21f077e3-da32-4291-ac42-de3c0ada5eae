package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.lang.javascript.psi.ecma6.TypeScriptImportStatement;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.sankuai.deepcode.astplugin.model.ImportInfo;
import com.sankuai.deepcode.astplugin.typescript.util.ImportClassifier;
import com.sankuai.deepcode.astplugin.typescript.util.ImportNameExtractor;
import com.sankuai.deepcode.astplugin.typescript.util.ImportUtils;
import com.sankuai.deepcode.astplugin.typescript.util.TypeScriptPsiUtils;

import java.util.List;

/**
 * TypeScript Import 解析器
 * 
 * 职责：
 * - 解析 TypeScript import 语句
 * - 提取模块说明符和导入名称
 * - 使用统一的兜底策略
 * 
 * <AUTHOR>
 */
public class TypeScriptImportResolver implements ImportResolver {
    
    private static final Logger LOG = Logger.getInstance(TypeScriptImportResolver.class);
    
    private final PSIResolver psiResolver;
    private final PathResolver pathResolver;
    private final FallbackStrategy fallbackStrategy;
    
    public TypeScriptImportResolver() {
        this.psiResolver = new PSIResolver();
        this.pathResolver = new PathResolver();
        this.fallbackStrategy = new FallbackStrategy();
    }
    
    @Override
    public ImportInfo resolveImport(PsiElement importElement, String filePath) {
        try {
            if (!(importElement instanceof TypeScriptImportStatement)) {
                return null;
            }
            
            TypeScriptImportStatement importStatement = (TypeScriptImportStatement) importElement;
            String importText = importStatement.getText();
            
            LOG.debug("Resolving TypeScript import: " + importText);
            
            // 提取模块说明符
            String moduleSpecifier = extractModuleSpecifier(importStatement);
            if (moduleSpecifier == null) {
                LOG.warn("Could not extract module specifier from: " + importText);
                return null;
            }
            
            // 提取导入名称
            List<String> importedNames = ImportNameExtractor.extractImportedNamesFromTypeScriptImport(importStatement);
            
            // 确定导入类型
            ImportInfo.ImportType type = determineImportType(importStatement);
            
            // 判断是否为外部模块
            boolean isExternal = ImportClassifier.isExternalModuleWithPSI(importStatement, moduleSpecifier);
            
            // 获取行号
            int lineNumber = TypeScriptPsiUtils.getLineNumber(importStatement);
            
            // 解析目标文件路径
            String targetFilePath = resolveTargetFilePath(importStatement, moduleSpecifier, isExternal);
            
            return new ImportInfo(importText, lineNumber, type, isExternal, importedNames, filePath, targetFilePath);
            
        } catch (Exception e) {
            LOG.warn("Failed to resolve TypeScript import", e);
            return null;
        }
    }
    
    @Override
    public boolean canResolve(PsiElement importElement) {
        return importElement instanceof TypeScriptImportStatement;
    }
    
    @Override
    public String getResolverType() {
        return "TypeScriptImport";
    }
    
    @Override
    public String resolveTargetFilePath(PsiElement importElement, String moduleSpecifier, boolean isExternal) {
        try {
            LOG.debug("[TypeScript] Resolving target file path for: " + moduleSpecifier + " (isExternal: " + isExternal + ")");
            
            // 步骤1: 尝试 PSI 解析
            String psiResolvedPath = psiResolver.resolveTargetFilePath(importElement, moduleSpecifier);
            if (psiResolvedPath != null) {
                LOG.debug("[TypeScript] ✅ PSI resolution successful: " + psiResolvedPath);
                
                // 转换为项目相对路径
                String relativePath = pathResolver.convertToProjectRelative(psiResolvedPath, importElement);
                return relativePath != null ? relativePath : psiResolvedPath;
            }
            
            // 步骤2: 尝试增强文件解析
            String enhancedPath = tryEnhancedResolution(importElement, moduleSpecifier);
            if (enhancedPath != null) {
                LOG.debug("[TypeScript] ✅ Enhanced resolution successful: " + enhancedPath);
                
                String relativePath = pathResolver.convertToProjectRelative(enhancedPath, importElement);
                return relativePath != null ? relativePath : enhancedPath;
            }
            
            // 步骤3: 执行兜底策略
            if (fallbackStrategy.shouldExecuteFallback(moduleSpecifier, isExternal)) {
                String fallbackPath = fallbackStrategy.executeFallback(
                    importElement, moduleSpecifier, isExternal, getResolverType());
                
                if (fallbackPath != null) {
                    LOG.debug("[TypeScript] ✅ Fallback strategy successful: " + fallbackPath);
                    return fallbackPath;
                }
            }
            
            LOG.debug("[TypeScript] ❌ All resolution methods failed for: " + moduleSpecifier);
            return null;
            
        } catch (Exception e) {
            LOG.warn("[TypeScript] Error resolving target file path for: " + moduleSpecifier, e);
            
            // 异常情况下的兜底策略
            if (pathResolver.isRelativePath(moduleSpecifier)) {
                try {
                    String emergencyPath = fallbackStrategy.executeFallback(
                        importElement, moduleSpecifier, isExternal, getResolverType() + "-Emergency");
                    
                    if (emergencyPath != null) {
                        LOG.debug("[TypeScript] ✅ Emergency fallback successful: " + emergencyPath);
                        return emergencyPath;
                    }
                } catch (Exception emergencyException) {
                    LOG.warn("[TypeScript] Emergency fallback also failed", emergencyException);
                }
            }
            
            return null;
        }
    }
    
    /**
     * 提取模块说明符
     */
    private String extractModuleSpecifier(TypeScriptImportStatement importStatement) {
        try {
            // 使用现有的工具方法
            String moduleSpec = ImportUtils.extractModuleSpecifierFromText(importStatement.getText());
            if (moduleSpec != null && !moduleSpec.isEmpty()) {
                LOG.debug("Extracted module specifier: " + moduleSpec);
                return moduleSpec;
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error extracting module specifier: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 确定导入类型
     */
    private ImportInfo.ImportType determineImportType(TypeScriptImportStatement importStatement) {
        try {
            String importText = importStatement.getText();
            
            if (importText.contains("import * as") || importText.contains("import *")) {
                return ImportInfo.ImportType.WILDCARD;
            } else if (importText.contains("import {") && importText.contains("}")) {
                return ImportInfo.ImportType.FROM_IMPORT;
            } else {
                return ImportInfo.ImportType.SINGLE;
            }
            
        } catch (Exception e) {
            LOG.debug("Error determining import type: " + e.getMessage());
            return ImportInfo.ImportType.SINGLE;
        }
    }
    
    /**
     * 尝试增强解析
     */
    private String tryEnhancedResolution(PsiElement importElement, String moduleSpecifier) {
        try {
            ImportUtils.ResourceResolutionResult result = 
                ImportUtils.resolveResourceWithPSI(importElement, moduleSpecifier);
            
            if (result.getAbsolutePath() != null) {
                LOG.debug("Enhanced resolution found: " + result.getAbsolutePath());
                return result.getAbsolutePath();
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error in enhanced resolution: " + e.getMessage());
            return null;
        }
    }
}
