package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.javascript.psi.*;
import com.intellij.lang.javascript.psi.ecmal4.JSClass;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * TypeScript/JavaScript 调用关系分析器
 * 分析函数调用关系
 *
 * <AUTHOR>
 */
public final class TypeScriptCallAnalyzer {

    private static final Logger LOG = Logger.getInstance(TypeScriptCallAnalyzer.class);

    private TypeScriptCallAnalyzer() {
        // 工具类不允许实例化
    }

    /**
     * 安全地提取PSI文本，确保不持有PSI引用
     */
    private static String extractSafeText(String psiText) {
        return psiText == null ? null : String.valueOf(psiText);
    }

    /**
     * 分析调用关系
     */
    public static void analyzeCallRelations(JSFile jsFile, AnalysisResult result) {
        try {
            Collection<JSCallExpression> callExpressions = PsiTreeUtil.findChildrenOfType(jsFile, JSCallExpression.class);

            for (JSCallExpression callExpression : callExpressions) {
                analyzeCallExpression(callExpression, result);
            }

        } catch (Exception e) {
            LOG.warn("Failed to analyze call relations in file: " + jsFile.getName(), e);
            result.addError("Call analysis failed: " + e.getMessage());
        }
    }

    /**
     * 分析单个函数调用表达式
     */
    public static void analyzeCallExpression(JSCallExpression callExpression, AnalysisResult result) {
        try {
            // 立即提取所有PSI文本并转换为安全字符串，避免后续持有PSI引用
            String safeCallExpressionText = extractSafeText(callExpression.getText());

            // 获取被调用的表达式
            JSExpression methodExpression = callExpression.getMethodExpression();
            if (methodExpression == null) {
                return;
            }
                
            String calleeText = extractSafeText(methodExpression.getText());
                    
            // 找到调用方（可能是函数内调用、类内调用或模块级调用）
            AnalysisNode callerNode = findCallerNode(callExpression, result);
            if (callerNode == null) {
                LOG.debug("No valid caller context found for call: " + safeCallExpressionText);
                return;
            }
                    
            // 尝试解析被调用的函数/方法
            PsiElement resolved = null;
            if (methodExpression instanceof JSReferenceExpression referenceExpression) {
                resolved = referenceExpression.resolve();
            }

            // 初始化变量，确保在所有分支中都有默认值
            AnalysisNode calleeNode;
            boolean isExternal = true;

            // === 添加详细的函数调用解析调试日志（只对特定函数） ===
            boolean isTargetFunction = safeCallExpressionText.contains("replaceToPlainStr") ||
                                     safeCallExpressionText.contains("mapProps") ||
                                     safeCallExpressionText.contains("connect");

            if (isTargetFunction) {
                System.out.println("[DeepCode] === Function Call Resolution Debug ===");
                System.out.println("[DeepCode] Call expression: " + safeCallExpressionText);
                System.out.println("[DeepCode] Method expression: " + methodExpression.getText());
                System.out.println("[DeepCode] Method expression class: " + methodExpression.getClass().getSimpleName());
                System.out.println("[DeepCode] Resolved element: " + (resolved != null ? resolved.getClass().getSimpleName() : "NULL"));

                if (resolved != null) {
                    System.out.println("[DeepCode] Resolved element text: " + resolved.getText().substring(0, Math.min(100, resolved.getText().length())) + "...");
                    if (resolved.getContainingFile() != null) {
                        System.out.println("[DeepCode] Resolved file name: " + resolved.getContainingFile().getName());
                        if (resolved.getContainingFile().getVirtualFile() != null) {
                            System.out.println("[DeepCode] Resolved absolute path: " + resolved.getContainingFile().getVirtualFile().getPath());
                        }
                    }
                }
            }

            // 使用新的解析器来处理不同类型的解析结果
            if (resolved instanceof JSFunction resolvedFunction) {
                if (isTargetFunction) System.out.println("[DeepCode] 🔍 Resolving as JSFunction...");
                var resolveResult = TypeScriptCallExpressionResolver.resolveFunctionCall(resolvedFunction, callExpression, isTargetFunction);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
                if (isTargetFunction) System.out.println("[DeepCode] ✅ JSFunction resolution result - isExternal: " + isExternal);
            } else if (resolved instanceof JSClass resolvedClass) {
                if (isTargetFunction) System.out.println("[DeepCode] 🔍 Resolving as JSClass...");
                var resolveResult = TypeScriptCallExpressionResolver.resolveClassCall(resolvedClass, callExpression);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
                if (isTargetFunction) System.out.println("[DeepCode] ✅ JSClass resolution result - isExternal: " + isExternal);
            } else if (resolved != null && resolved.getClass().getSimpleName().contains("Variable")) {
                // 处理箭头函数变量（如 const func = () => {}）
                if (isTargetFunction) System.out.println("[DeepCode] 🔍 Resolving as Variable (arrow function)...");
                var resolveResult = TypeScriptCallExpressionResolver.resolveVariableCall(resolved, callExpression, isTargetFunction);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
                if (isTargetFunction) System.out.println("[DeepCode] ✅ Variable resolution result - isExternal: " + isExternal);
            } else if (methodExpression instanceof JSReferenceExpression referenceExpression && calleeText.contains(".")) {
                if (isTargetFunction) System.out.println("[DeepCode] 🔍 Resolving as property call...");
                // 属性访问调用（如 obj.method()）
                var resolveResult = TypeScriptCallExpressionResolver.resolvePropertyCall(referenceExpression, callExpression, calleeText);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
                if (isTargetFunction) System.out.println("[DeepCode] ✅ Property call resolution result - isExternal: " + isExternal);
            } else {
                if (isTargetFunction) System.out.println("[DeepCode] ❌ Resolving as unknown call (all other methods failed)...");
                // 无法解析的调用，创建外部方法节点
                var resolveResult = TypeScriptCallExpressionResolver.resolveUnknownCall(callExpression, calleeText);
                calleeNode = resolveResult.calleeNode();
                isExternal = resolveResult.isExternal();
                if (isTargetFunction) System.out.println("[DeepCode] ❌ Unknown call resolution result - isExternal: " + isExternal);
            }

            if (isTargetFunction) {
                System.out.println("[DeepCode] === Final Resolution Result ===");
                System.out.println("[DeepCode] Callee name: " + calleeNode.getName());
                System.out.println("[DeepCode] Callee signature: " + calleeNode.getSignature());
                System.out.println("[DeepCode] Is external: " + isExternal);
                System.out.println("[DeepCode] =====================================");
            }

            // 创建调用实例
            List<CallRelation.CallInstance> instances = new ArrayList<>();
            instances.add(new CallRelation.CallInstance(
                    TypeScriptPsiUtils.getLineNumber(callExpression),
                    safeCallExpressionText
            ));

            // 创建调用关系
            CallRelation relation = new CallRelation(
                    callerNode,
                    calleeNode,
                    TypeScriptPsiUtils.getLineNumber(callExpression),
                    safeCallExpressionText,
                    isExternal,
                    instances
            );

            result.addCallRelation(relation);
            LOG.debug("Added call relation: " + callerNode.getName() + " -> " + calleeNode.getName() + " at line " + TypeScriptPsiUtils.getLineNumber(callExpression));

        } catch (Exception e) {
            // 使用安全的文本，避免在错误处理时也访问PSI
            String errorText;
            try {
                errorText = extractSafeText(callExpression.getText());
            } catch (Exception psiException) {
                errorText = "[PSI_ACCESS_FAILED]";
            }
            LOG.warn("Error analyzing call expression: " + errorText, e);
        }
    }

    /**
     * 查找调用方节点（函数内调用、类方法调用或模块级调用）
     */
    public static AnalysisNode findCallerNode(JSCallExpression callExpression, AnalysisResult result) {
        try {
            // 首先查找是否在函数内部（优先级最高）
            JSFunction callerFunction = PsiTreeUtil.getParentOfType(callExpression, JSFunction.class);

            if (callerFunction != null) {
                // 函数内调用
                String functionName = callerFunction.getName() != null ? callerFunction.getName() : "anonymous";
                String packageName = TypeScriptPsiUtils.getPackageName(callerFunction);
                String className = getContainingClassName(callerFunction);

                String fullId = packageName + "." + (className != null ? className + "." : "") + functionName;
                String signature = TypeScriptPsiUtils.buildFunctionSignature(callerFunction);

                // 检查是否已存在该节点
                AnalysisNode existingNode = result.getNodes().get(fullId);
                if (existingNode != null) {
                    return existingNode;
                }

                // 创建新的函数节点
                AnalysisNode callerNode = new AnalysisNode(
                        fullId,
                        className != null ? AnalysisNode.NodeType.METHOD : AnalysisNode.NodeType.FUNCTION,
                        functionName,
                        className,
                        packageName,
                        TypeScriptPsiUtils.getLineNumber(callerFunction),
                        signature,
                        packageName,
                        TypeScriptPsiUtils.getFilePath(callerFunction),
                        TypeScriptPsiUtils.detectLanguage((JSFile) callExpression.getContainingFile()).getCode()
                );

                result.addNode(callerNode);
                return callerNode;
            }

            // 检查是否在类内部（类字段定义中的调用）
            JSClass callerClass = PsiTreeUtil.getParentOfType(callExpression, JSClass.class);
            if (callerClass != null) {
                // 类字段中的调用，caller 是类节点本身
                String className = callerClass.getName();
                if (className != null) {
                    String packageName = TypeScriptPsiUtils.getPackageName(callerClass);
                    String classFullId = packageName + "." + className;

                    // 查找或创建类节点
                    AnalysisNode existingClassNode = result.getNodes().get(classFullId);
                    if (existingClassNode != null) {
                        return existingClassNode;
                    }

                    // 创建新的类节点
                    AnalysisNode classNode = new AnalysisNode(
                            classFullId,
                            AnalysisNode.NodeType.CLASS,
                            className,
                            className,
                            packageName,
                            TypeScriptPsiUtils.getLineNumber(callerClass),
                            className + "()",
                            packageName,
                            TypeScriptPsiUtils.getFilePath(callerClass),
                            TypeScriptPsiUtils.detectLanguage((JSFile) callExpression.getContainingFile()).getCode()
                    );
                    
                    result.addNode(classNode);
                    return classNode;
                }
            }

            // 模块级调用 - 创建或获取模块节点作为调用者
            return findOrCreateModuleNode(callExpression, result);

        } catch (Exception e) {
            LOG.warn("Error finding caller node for call: " + callExpression.getText() + " - " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查找或创建模块节点
     */
    private static AnalysisNode findOrCreateModuleNode(JSCallExpression callExpression, AnalysisResult result) {
        try {
            // 获取模块的完整包名路径
            String moduleFullPath = TypeScriptPsiUtils.getPackageName(callExpression);

            // 检查是否已存在模块节点
            AnalysisNode existingModuleNode = result.getNodes().get(moduleFullPath);
            if (existingModuleNode != null) {
                return existingModuleNode;
            }

            // 创建模块节点，使用完整包名作为ID和signature
            String moduleName = TypeScriptPsiUtils.getModuleName((JSFile) callExpression.getContainingFile());
            AnalysisNode moduleNode = new AnalysisNode(
                    // 使用完整包名作为ID
                    moduleFullPath,
                    AnalysisNode.NodeType.MODULE,
                    moduleName, // 显示名称使用模块名
                    null, // 模块没有类名
                    moduleFullPath, // 包名就是自己的全路径
                    1, // 模块从第1行开始
                    moduleFullPath, // signature也使用完整包名
                    moduleFullPath,
                    TypeScriptPsiUtils.getFilePath(callExpression),
                    TypeScriptPsiUtils.detectLanguage((JSFile) callExpression.getContainingFile()).getCode()
            );

            result.addNode(moduleNode);
            LOG.debug("Created module node: " + moduleFullPath +
                    " for file: " + TypeScriptPsiUtils.getFilePath(callExpression));

            return moduleNode;
        } catch (Exception e) {
            LOG.warn("Error creating module node: " + e.getMessage(), e);

            // 回退方案：使用简单的模块名
            String simpleName = "unknown_module";
            try {
                simpleName = TypeScriptPsiUtils.getModuleName((JSFile) callExpression.getContainingFile());
            } catch (Exception moduleEx) {
                LOG.debug("Failed to get module name in fallback: " + moduleEx.getMessage());
            }

            AnalysisNode fallbackNode = new AnalysisNode(
                    simpleName,
                    AnalysisNode.NodeType.MODULE,
                    simpleName,
                    null,
                    simpleName,
                    1,
                    simpleName,
                    simpleName,
                    TypeScriptPsiUtils.getFilePath(callExpression),
                    TypeScriptPsiUtils.detectLanguage((JSFile) callExpression.getContainingFile()).getCode()
            );

            result.addNode(fallbackNode);
            return fallbackNode;
        }
    }

    /**
     * 获取包含的类名
     */
    private static String getContainingClassName(JSFunction function) {
        JSClass containingClass = PsiTreeUtil.getParentOfType(function, JSClass.class);
        return containingClass != null ? containingClass.getName() : null;
    }
}

