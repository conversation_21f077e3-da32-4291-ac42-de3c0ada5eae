package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;

/**
 * 项目路径转换器
 * 
 * 职责：
 * - 将绝对路径转换为项目相对路径
 * - 处理项目根目录的识别
 * - 统一路径格式
 * 
 * <AUTHOR>
 */
public class ProjectPathConverter {
    
    private static final Logger LOG = Logger.getInstance(ProjectPathConverter.class);
    
    /**
     * 转换绝对路径为项目相对路径
     * 
     * @param absolutePath 绝对路径
     * @param context PSI 上下文元素
     * @return 项目相对路径，失败时返回 null
     */
    public String convert(String absolutePath, PsiElement context) {
        try {
            if (absolutePath == null || context == null) {
                return null;
            }
            
            LOG.debug("Converting absolute path to project relative: " + absolutePath);
            
            // 获取项目根目录
            String projectBasePath = getProjectBasePath(context);
            if (projectBasePath == null) {
                LOG.debug("Cannot get project base path");
                return null;
            }
            
            LOG.debug("Project base path: " + projectBasePath);
            
            // 检查路径是否在项目内
            if (!absolutePath.startsWith(projectBasePath)) {
                LOG.debug("Path is outside project, keeping absolute: " + absolutePath);
                return null; // 项目外的文件保持绝对路径
            }
            
            // 移除项目根路径前缀
            String relativePath = absolutePath.substring(projectBasePath.length());
            
            // 移除开头的斜杠
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1);
            }
            
            LOG.debug("Converted to project relative: " + relativePath);
            return relativePath;
            
        } catch (Exception e) {
            LOG.debug("Error converting to project relative path: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 从 PsiFile 获取项目相对路径
     * 
     * @param psiFile PSI 文件
     * @return 项目相对路径，失败时返回 null
     */
    public String convertFromPsiFile(PsiFile psiFile) {
        try {
            if (psiFile == null || psiFile.getVirtualFile() == null) {
                return null;
            }
            
            String absolutePath = psiFile.getVirtualFile().getPath();
            return convert(absolutePath, psiFile);
            
        } catch (Exception e) {
            LOG.debug("Error converting PsiFile to project relative path: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查路径是否在项目内
     * 
     * @param absolutePath 绝对路径
     * @param context PSI 上下文元素
     * @return 是否在项目内
     */
    public boolean isWithinProject(String absolutePath, PsiElement context) {
        try {
            String projectBasePath = getProjectBasePath(context);
            return projectBasePath != null && absolutePath != null && 
                   absolutePath.startsWith(projectBasePath);
        } catch (Exception e) {
            LOG.debug("Error checking if path is within project: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取项目根目录路径
     * 
     * @param context PSI 上下文元素
     * @return 项目根目录路径，失败时返回 null
     */
    private String getProjectBasePath(PsiElement context) {
        try {
            Project project = context.getProject();
            if (project == null) {
                return null;
            }
            
            String basePath = project.getBasePath();
            if (basePath == null) {
                return null;
            }
            
            // 确保路径以斜杠结尾，便于后续处理
            return basePath.endsWith("/") ? basePath.substring(0, basePath.length() - 1) : basePath;
            
        } catch (Exception e) {
            LOG.debug("Error getting project base path: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 规范化项目相对路径
     * 
     * @param relativePath 相对路径
     * @return 规范化后的相对路径
     */
    public String normalizeRelativePath(String relativePath) {
        if (relativePath == null) {
            return null;
        }
        
        try {
            // 移除开头的 ./
            if (relativePath.startsWith("./")) {
                relativePath = relativePath.substring(2);
            }
            
            // 移除开头的斜杠
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1);
            }
            
            return relativePath;
            
        } catch (Exception e) {
            LOG.debug("Error normalizing relative path: " + e.getMessage());
            return relativePath; // 返回原始路径作为兜底
        }
    }
    
    /**
     * 将项目相对路径转换为绝对路径
     * 
     * @param relativePath 项目相对路径
     * @param context PSI 上下文元素
     * @return 绝对路径，失败时返回 null
     */
    public String convertToAbsolute(String relativePath, PsiElement context) {
        try {
            if (relativePath == null || context == null) {
                return null;
            }
            
            String projectBasePath = getProjectBasePath(context);
            if (projectBasePath == null) {
                return null;
            }
            
            // 规范化相对路径
            String normalizedRelativePath = normalizeRelativePath(relativePath);
            
            // 组合成绝对路径
            return projectBasePath + "/" + normalizedRelativePath;
            
        } catch (Exception e) {
            LOG.debug("Error converting relative path to absolute: " + e.getMessage());
            return null;
        }
    }
}
