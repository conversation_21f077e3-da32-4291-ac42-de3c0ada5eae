package com.sankuai.deepcode.astplugin.typescript.export;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.roots.ProjectRootManager;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.sankuai.deepcode.astplugin.analyzer.AnalyzerService;
import com.sankuai.deepcode.astplugin.export.ExportOption;
import com.sankuai.deepcode.astplugin.export.ExportService;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.typescript.export.exporter.AnalysisFileResult;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * TypeScript/JavaScript 导出服务 - 性能优化版本
 * 使用后台任务、进度指示器、并行处理避免UI阻塞
 *
 * <AUTHOR>
 */
public class TypeScriptExportService implements ExportService {

    @Override
    public @NotNull String getLanguageName() {
        return "TypeScript/JavaScript";
    }

    @Override
    public @NotNull List<ExportOption> getSupportedExportOptions() {
        return Arrays.asList(
                ExportOption.STATISTICS_CSV,
                ExportOption.CALL_DETAILS,
                ExportOption.EXPORT_IMPORTS
        );
    }

    @Override
    public void exportProject(@NotNull Project project, @NotNull ExportOption exportOption, @NotNull VirtualFile outputDir) {
        // 使用后台任务处理，避免阻塞 UI 线程
        ProgressManager.getInstance().run(new Task.Backgroundable(project, "Exporting TypeScript/JavaScript AST Report", true) {
            @Override
            public void run(@NotNull ProgressIndicator progressIndicator) {
                try {
                    List<AnalysisFileResult> analysisFileResults = performCompleteAnalysis(project, progressIndicator);

                    if (progressIndicator.isCanceled() || analysisFileResults.isEmpty()) {
                        if (analysisFileResults.isEmpty()) {
                            ApplicationManager.getApplication().invokeLater(() ->
                                    Messages.showInfoMessage("项目中没有找到 typeScript/javaScript 文件", "信息"));
                        }
                        return;
                    }

                    progressIndicator.setText("正在组装导出数据...");
                    // 简化导出逻辑 - 直接写入文件
                    exportAnalysisResults(analysisFileResults, exportOption, outputDir.getPath());

                    final int totalFiles = analysisFileResults.size();
                    ApplicationManager.getApplication().invokeLater(() -> {
                        int result = Messages.showYesNoDialog(project,
                                String.format("TypeScript/JavaScript AST %s 导出成功！\n\n分析了 %d 个文件。\n\n是否要打开包含文件夹？",
                                        exportOption.getDisplayName(), totalFiles),
                                "导出成功", "打开文件夹", "关闭", Messages.getInformationIcon());

                        if (result == Messages.YES) {
                            openFolder(outputDir);
                        }
                    });

                } catch (Exception ex) {
                    ApplicationManager.getApplication().invokeLater(() ->
                            Messages.showErrorDialog("导出项目报告失败: " + ex.getMessage(), "导出错误"));
                }
            }
        });
    }

    @Override
    public boolean hasLanguageFilesInProject(@NotNull Project project) {
        return !findTypeScriptFiles(project).isEmpty();
    }

    @Override
    public int getLanguageFileCount(@NotNull Project project) {
        return findTypeScriptFiles(project).size();
    }

    private List<AnalysisFileResult> performCompleteAnalysis(@NotNull Project project, @NotNull ProgressIndicator progressIndicator) {
        progressIndicator.setText("正在扫描 TypeScript/JavaScript 文件...");
        List<VirtualFile> typeScriptFiles = findTypeScriptFiles(project);

        if (typeScriptFiles.isEmpty()) {
            return new ArrayList<>();
        }

        progressIndicator.setText("正在分析 " + typeScriptFiles.size() + " 个 TypeScript/JavaScript 文件...");
        progressIndicator.setIndeterminate(false);

        return analyzeFilesInParallel(project, typeScriptFiles, progressIndicator);
    }

    private List<VirtualFile> findTypeScriptFiles(Project project) {
        List<VirtualFile> typeScriptFiles = new ArrayList<>();
        VirtualFile[] contentRoots = ProjectRootManager.getInstance(project).getContentRoots();

        for (VirtualFile contentRoot : contentRoots) {
            collectTypeScriptFiles(contentRoot, typeScriptFiles);
        }

        return typeScriptFiles;
    }

    private void collectTypeScriptFiles(VirtualFile directory, List<VirtualFile> typeScriptFiles) {
        if (!directory.isDirectory()) {
            return;
        }

        String dirName = directory.getName().toLowerCase();
        if (dirName.equals("node_modules") || dirName.equals(".git") || dirName.equals("dist") ||
            dirName.equals("build") || dirName.equals(".next") || dirName.equals("coverage") ||
            dirName.startsWith(".")) {
            return;
        }

        for (VirtualFile child : directory.getChildren()) {
            if (child.isDirectory()) {
                collectTypeScriptFiles(child, typeScriptFiles);
            } else if (isTypeScriptFile(child)) {
                typeScriptFiles.add(child);
            }
        }
    }

    private boolean isTypeScriptFile(VirtualFile file) {
        if (file == null || file.isDirectory()) return false;
        String name = file.getName();
        return name.endsWith(".ts") || name.endsWith(".tsx") || name.endsWith(".js") || name.endsWith(".jsx");
    }

    private List<AnalysisFileResult> analyzeFilesInParallel(Project project, List<VirtualFile> typeScriptFiles, ProgressIndicator progressIndicator) {
        List<AnalysisFileResult> records = Collections.synchronizedList(new ArrayList<>());
        AtomicInteger completedCount = new AtomicInteger(0);
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        AnalyzerService analyzerService = AnalyzerService.getInstance();

        try {
            List<Future<Void>> futures = new ArrayList<>();

            for (VirtualFile typeScriptFile : typeScriptFiles) {
                Future<Void> future = executorService.submit(() -> {
                    if (progressIndicator.isCanceled()) return null;

                    int completed = completedCount.incrementAndGet();
                    progressIndicator.setText("正在分析 " + typeScriptFile.getName() + " (" + completed + "/" + typeScriptFiles.size() + ")");
                    progressIndicator.setFraction((double) completed / typeScriptFiles.size());

                    try {
                        PsiFile psiFile = ReadAction.compute(() -> PsiManager.getInstance(project).findFile(typeScriptFile));
                        if (psiFile != null) {
                            AnalysisResult result = analyzerService.analyze(psiFile);
                            if (result != null) {
                                records.add(new AnalysisFileResult(typeScriptFile.getPath(), result));
                            }
                        }
                    } catch (Exception ex) {
                        System.err.println("Error analyzing file " + typeScriptFile.getPath() + ": " + ex.getMessage());
                    }
                    return null;
                });
                futures.add(future);
            }

            for (Future<Void> future : futures) {
                try {
                    if (!progressIndicator.isCanceled()) {
                        future.get(30, TimeUnit.SECONDS);
                    } else {
                        future.cancel(true);
                    }
                } catch (TimeoutException e) {
                    future.cancel(true);
                } catch (Exception ignored) {}
            }

        } finally {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        return records;
    }

    private void exportAnalysisResults(List<AnalysisFileResult> results, ExportOption option, String outputPath) {
        try {
            String timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = "typescript_analysis_" + timestamp + ".csv";
            java.nio.file.Path filePath = java.nio.file.Paths.get(outputPath, fileName);

            try (java.io.PrintWriter writer = new java.io.PrintWriter(java.nio.file.Files.newBufferedWriter(filePath))) {
                writer.println("filePath,targetFilePath,importStatement,lineNumber,isExternal,importType");
                for (AnalysisFileResult result : results) {
                    for (com.sankuai.deepcode.astplugin.model.ImportInfo imp : result.analysisResult().getImports()) {
                        writer.printf("%s,%s,\"%s\",%d,%s,%s%n",
                                result.filePath(),
                                imp.getTargetFilePath() != null ? imp.getTargetFilePath() : "",
                                imp.getStatement().replace("\"", "\"\""),
                                imp.getLineNumber(),
                                imp.isExternal(),
                                imp.getType());
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }

    private void openFolder(VirtualFile outputDir) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("mac")) {
                Runtime.getRuntime().exec("open " + outputDir.getPath());
            } else if (os.contains("win")) {
                Runtime.getRuntime().exec("explorer " + outputDir.getPath());
            } else {
                Runtime.getRuntime().exec("xdg-open " + outputDir.getPath());
            }
        } catch (IOException ex) {
            Messages.showWarningDialog("无法打开文件夹: " + ex.getMessage(), "警告");
        }
    }
}

