package com.sankuai.deepcode.astplugin.typescript.export.exporter;

import com.intellij.openapi.project.Project;

import java.io.FileWriter;
import java.io.IOException;
import java.util.List;

/**
 * TypeScript CSV 导出器
 * 负责将分析数据导出为CSV格式
 *
 * <AUTHOR>
 */
public class TypeScriptCsvExporter implements TypeScriptDataExporter {

    @Override
    public void export(Project project,
                       List<AnalysisFileResult> analysisFileResults,
                       String outputDirPath,
                       String timestamp) throws IOException {
        String fileName = String.format("TypeScript_AST_Statistics_%s.csv", timestamp);
        String filePath = outputDirPath + "/" + fileName;

        try (FileWriter writer = new FileWriter(filePath)) {
            // 写入CSV头部
            writer.append("filePath,moduleName,modulePath,funcCount,classCount,interfaceCount,enumCount,typeAliasCount\n");

            // 写入数据
            String projectBasePath = project.getBasePath();
            for (AnalysisFileResult fileResult : analysisFileResults) {
                String relativeFilePath = fileResult.getRelativeFilePath(projectBasePath);
                String moduleName = fileResult.getModuleName(projectBasePath);
                String modulePath = fileResult.getModulePath(projectBasePath);

                // TODO: 从 AnalysisResult 中提取实际的统计数据
                writer.append(String.format("%s,%s,%s,%d,%d,%d,%d,%d\n",
                        escapeCsv(relativeFilePath),
                        escapeCsv(moduleName),
                        escapeCsv(modulePath),
                        0, // funcCount - TODO: 实现
                        0, // classCount - TODO: 实现
                        0, // interfaceCount - TODO: 实现
                        0, // enumCount - TODO: 实现
                        0  // typeAliasCount - TODO: 实现
                ));
            }
        }
    }

    @Override
    public String getExporterName() {
        return "TypeScript Statistics (CSV)";
    }

    @Override
    public String getFileExtension() {
        return "csv";
    }
}

