package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.lang.ecmascript6.psi.ES6ImportDeclaration;
import com.intellij.lang.javascript.psi.JSLiteralExpression;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.ImportInfo;
import com.sankuai.deepcode.astplugin.typescript.util.ImportClassifier;
import com.sankuai.deepcode.astplugin.typescript.util.ImportNameExtractor;
import com.sankuai.deepcode.astplugin.typescript.util.ImportUtils;
import com.sankuai.deepcode.astplugin.typescript.util.TypeScriptPsiUtils;

import java.util.Collection;
import java.util.List;

/**
 * ES6 Import 解析器
 * 
 * 职责：
 * - 解析 ES6 import 声明
 * - 提取模块说明符和导入名称
 * - 使用统一的兜底策略
 * 
 * <AUTHOR>
 */
public class ES6ImportResolver implements ImportResolver {
    
    private static final Logger LOG = Logger.getInstance(ES6ImportResolver.class);
    
    private final PSIResolver psiResolver;
    private final PathResolver pathResolver;
    private final FallbackStrategy fallbackStrategy;
    
    public ES6ImportResolver() {
        this.psiResolver = new PSIResolver();
        this.pathResolver = new PathResolver();
        this.fallbackStrategy = new FallbackStrategy();
    }
    
    @Override
    public ImportInfo resolveImport(PsiElement importElement, String filePath) {
        try {
            if (!(importElement instanceof ES6ImportDeclaration)) {
                return null;
            }
            
            ES6ImportDeclaration importDeclaration = (ES6ImportDeclaration) importElement;
            String importText = importDeclaration.getText();
            
            LOG.debug("Resolving ES6 import: " + importText);
            
            // 提取模块说明符
            String moduleSpecifier = extractModuleSpecifier(importDeclaration);
            if (moduleSpecifier == null) {
                LOG.warn("Could not extract module specifier from: " + importText);
                return null;
            }
            
            // 提取导入名称
            List<String> importedNames = ImportNameExtractor.extractImportedNamesFromText(
                importText, moduleSpecifier, importDeclaration);
            
            // 确定导入类型
            ImportInfo.ImportType type = determineImportType(importText);
            
            // 判断是否为外部模块
            boolean isExternal = ImportClassifier.isExternalModuleWithES6PSI(importDeclaration, moduleSpecifier);
            
            // 获取行号
            int lineNumber = TypeScriptPsiUtils.getLineNumber(importDeclaration);
            
            // 解析目标文件路径
            String targetFilePath = resolveTargetFilePath(importDeclaration, moduleSpecifier, isExternal);
            
            return new ImportInfo(importText, lineNumber, type, isExternal, importedNames, filePath, targetFilePath);
            
        } catch (Exception e) {
            LOG.warn("Failed to resolve ES6 import", e);
            return null;
        }
    }
    
    @Override
    public boolean canResolve(PsiElement importElement) {
        return importElement instanceof ES6ImportDeclaration;
    }
    
    @Override
    public String getResolverType() {
        return "ES6Import";
    }
    
    @Override
    public String resolveTargetFilePath(PsiElement importElement, String moduleSpecifier, boolean isExternal) {
        try {
            LOG.debug("[ES6] Resolving target file path for: " + moduleSpecifier + " (isExternal: " + isExternal + ")");
            
            // 步骤1: 尝试 PSI 解析
            String psiResolvedPath = psiResolver.resolveTargetFilePath(importElement, moduleSpecifier);
            if (psiResolvedPath != null) {
                LOG.debug("[ES6] ✅ PSI resolution successful: " + psiResolvedPath);
                
                // 转换为项目相对路径
                String relativePath = pathResolver.convertToProjectRelative(psiResolvedPath, importElement);
                return relativePath != null ? relativePath : psiResolvedPath;
            }
            
            // 步骤2: 尝试增强文件解析
            String enhancedPath = tryEnhancedResolution(importElement, moduleSpecifier);
            if (enhancedPath != null) {
                LOG.debug("[ES6] ✅ Enhanced resolution successful: " + enhancedPath);
                
                String relativePath = pathResolver.convertToProjectRelative(enhancedPath, importElement);
                return relativePath != null ? relativePath : enhancedPath;
            }
            
            // 步骤3: 执行兜底策略
            if (fallbackStrategy.shouldExecuteFallback(moduleSpecifier, isExternal)) {
                String fallbackPath = fallbackStrategy.executeFallback(
                    importElement, moduleSpecifier, isExternal, getResolverType());
                
                if (fallbackPath != null) {
                    LOG.debug("[ES6] ✅ Fallback strategy successful: " + fallbackPath);
                    return fallbackPath;
                }
            }
            
            LOG.debug("[ES6] ❌ All resolution methods failed for: " + moduleSpecifier);
            return null;
            
        } catch (Exception e) {
            LOG.warn("[ES6] Error resolving target file path for: " + moduleSpecifier, e);
            
            // 异常情况下的兜底策略
            if (pathResolver.isRelativePath(moduleSpecifier)) {
                try {
                    String emergencyPath = fallbackStrategy.executeFallback(
                        importElement, moduleSpecifier, isExternal, getResolverType() + "-Emergency");
                    
                    if (emergencyPath != null) {
                        LOG.debug("[ES6] ✅ Emergency fallback successful: " + emergencyPath);
                        return emergencyPath;
                    }
                } catch (Exception emergencyException) {
                    LOG.warn("[ES6] Emergency fallback also failed", emergencyException);
                }
            }
            
            return null;
        }
    }
    
    /**
     * 提取模块说明符
     */
    private String extractModuleSpecifier(ES6ImportDeclaration importDeclaration) {
        try {
            // 方法1：直接查找字符串字面量
            Collection<JSLiteralExpression> literals = 
                PsiTreeUtil.findChildrenOfType(importDeclaration, JSLiteralExpression.class);
            
            for (JSLiteralExpression literal : literals) {
                if (literal.isStringLiteral()) {
                    String moduleSpec = literal.getStringValue();
                    if (moduleSpec != null && !moduleSpec.isEmpty()) {
                        LOG.debug("Extracted module specifier via PSI: " + moduleSpec);
                        return moduleSpec;
                    }
                }
            }
            
            // 方法2：回退到文本分析
            String importText = importDeclaration.getText();
            String textResult = ImportUtils.extractModuleSpecifierFromText(importText);
            if (textResult != null && !textResult.isEmpty()) {
                LOG.debug("Extracted module specifier via text: " + textResult);
                return textResult;
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error extracting module specifier: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 确定导入类型
     */
    private ImportInfo.ImportType determineImportType(String importText) {
        try {
            if (importText.contains("import * as") || importText.contains("import *")) {
                return ImportInfo.ImportType.NAMESPACE;
            } else if (importText.contains("import {") && importText.contains("}")) {
                return ImportInfo.ImportType.NAMED;
            } else if (importText.matches(".*import\\s+\\w+\\s+from.*")) {
                return ImportInfo.ImportType.DEFAULT;
            } else {
                return ImportInfo.ImportType.SINGLE;
            }
        } catch (Exception e) {
            LOG.debug("Error determining import type: " + e.getMessage());
            return ImportInfo.ImportType.SINGLE;
        }
    }
    
    /**
     * 尝试增强解析
     */
    private String tryEnhancedResolution(PsiElement importElement, String moduleSpecifier) {
        try {
            ImportUtils.ResourceResolutionResult result = 
                ImportUtils.resolveResourceWithPSI(importElement, moduleSpecifier);
            
            if (result.getAbsolutePath() != null) {
                LOG.debug("Enhanced resolution found: " + result.getAbsolutePath());
                return result.getAbsolutePath();
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error in enhanced resolution: " + e.getMessage());
            return null;
        }
    }
}
