package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.openapi.diagnostic.Logger;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;

/**
 * TypeScript/JavaScript 统计分析器
 * 负责统计分析结果的各项指标
 *
 * <AUTHOR>
 */
public final class TypeScriptStatisticsAnalyzer {

    private static final Logger LOG = Logger.getInstance(TypeScriptStatisticsAnalyzer.class);

    private TypeScriptStatisticsAnalyzer() {
        // 工具类不允许实例化
    }

    /**
     * 更新统计信息
     */
    public static void updateStatistics(JSFile jsFile, AnalysisResult result) {
        try {
            // 简单的统计信息更新
            int nodeCount = result.getNodes().size();
            int relationCount = result.getCallRelations().size();
            
            LOG.debug("Statistics for file " + jsFile.getName() +
                     ": Nodes=" + nodeCount + ", Relations=" + relationCount);
                    
            // 这里可以扩展来处理具体的统计逻辑
                    
        } catch (Exception e) {
            LOG.warn("Failed to update statistics for file: " + jsFile.getName(), e);
            result.addError("Statistics update failed: " + e.getMessage());
        }
    }
}

