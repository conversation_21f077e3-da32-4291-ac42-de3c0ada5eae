package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;

/**
 * 统一兜底策略
 * 
 * 职责：
 * - 为所有导入类型提供统一的兜底策略
 * - 确保相对路径永不返回 null
 * - 智能路径推断和文件系统解析
 * 
 * 设计原则：
 * - 相对路径必须解析（无论 PSI 是否成功）
 * - 永不返回 null（即使找不到实际文件）
 * - 智能扩展名推断
 * 
 * <AUTHOR>
 */
public class FallbackStrategy {
    
    private static final Logger LOG = Logger.getInstance(FallbackStrategy.class);
    
    private final PathResolver pathResolver;
    private final SmartPathInferrer smartInferrer;
    private final FileSystemResolver fileSystemResolver;
    private final EmergencyPathGenerator emergencyGenerator;
    
    public FallbackStrategy() {
        this.pathResolver = new PathResolver();
        this.smartInferrer = new SmartPathInferrer(pathResolver);
        this.fileSystemResolver = new FileSystemResolver();
        this.emergencyGenerator = new EmergencyPathGenerator(pathResolver);
    }
    
    /**
     * 执行兜底策略解析
     * 
     * @param context PSI 上下文元素
     * @param moduleSpecifier 模块说明符
     * @param isExternal 是否为外部模块
     * @param resolverType 解析器类型（用于日志）
     * @return 兜底解析的路径，永不返回 null
     */
    public String executeFallback(PsiElement context, String moduleSpecifier, boolean isExternal, String resolverType) {
        LOG.info("[" + resolverType + " Fallback] === executeFallback() for: " + moduleSpecifier + " ===");
        System.out.println("[" + resolverType + " Fallback] === executeFallback() for: " + moduleSpecifier + " ===");
        
        try {
            // 检查是否需要执行兜底策略
            boolean isRelativePath = pathResolver.isRelativePath(moduleSpecifier);
            
            if (!isExternal || isRelativePath) {
                if (isRelativePath) {
                    LOG.info("[" + resolverType + " Fallback] Detected relative path, forcing fallback resolution");
                    System.out.println("[" + resolverType + " Fallback] Detected relative path, forcing fallback resolution");
                }
                
                // 步骤1: 智能路径推断
                String smartPath = smartInferrer.infer(context, moduleSpecifier);
                if (smartPath != null) {
                    LOG.info("[" + resolverType + " Fallback] ✅ Smart inference successful: " + smartPath);
                    System.out.println("[" + resolverType + " Fallback] ✅ Smart inference successful: " + smartPath);
                    
                    // 转换为项目相对路径
                    String relativePath = pathResolver.convertToProjectRelative(smartPath, context);
                    if (relativePath != null) {
                        LOG.info("[" + resolverType + " Fallback] ✅ Converted to project relative: " + relativePath);
                        System.out.println("[" + resolverType + " Fallback] ✅ Converted to project relative: " + relativePath);
                        return relativePath;
                    } else {
                        LOG.info("[" + resolverType + " Fallback] ⚠️ Could not convert to relative, using inferred: " + smartPath);
                        System.out.println("[" + resolverType + " Fallback] ⚠️ Could not convert to relative, using inferred: " + smartPath);
                        return smartPath;
                    }
                }
                
                // 步骤2: 文件系统解析
                String fileSystemPath = fileSystemResolver.resolve(context, moduleSpecifier);
                if (fileSystemPath != null) {
                    LOG.info("[" + resolverType + " Fallback] ✅ File system resolution successful: " + fileSystemPath);
                    System.out.println("[" + resolverType + " Fallback] ✅ File system resolution successful: " + fileSystemPath);
                    
                    String relativePath = pathResolver.convertToProjectRelative(fileSystemPath, context);
                    return relativePath != null ? relativePath : fileSystemPath;
                }
                
                // 步骤3: 紧急路径生成（永不失败）
                String emergencyPath = emergencyGenerator.generate(context, moduleSpecifier);
                LOG.info("[" + resolverType + " Fallback] ⚠️ Using emergency path: " + emergencyPath);
                System.out.println("[" + resolverType + " Fallback] ⚠️ Using emergency path: " + emergencyPath);
                return emergencyPath;
                
            } else {
                LOG.info("[" + resolverType + " Fallback] Skipping fallback for true external module: " + moduleSpecifier);
                System.out.println("[" + resolverType + " Fallback] Skipping fallback for true external module: " + moduleSpecifier);
                return null;
            }
            
        } catch (Exception e) {
            LOG.warn("[" + resolverType + " Fallback] Exception in fallback strategy: " + e.getMessage(), e);
            System.out.println("[" + resolverType + " Fallback] Exception in fallback strategy: " + e.getMessage());
            
            // 即使异常也要尝试紧急生成
            try {
                String emergencyPath = emergencyGenerator.generate(context, moduleSpecifier);
                LOG.info("[" + resolverType + " Fallback] ✅ Emergency generation successful: " + emergencyPath);
                System.out.println("[" + resolverType + " Fallback] ✅ Emergency generation successful: " + emergencyPath);
                return emergencyPath;
            } catch (Exception emergencyException) {
                LOG.warn("[" + resolverType + " Fallback] Emergency generation also failed: " + emergencyException.getMessage());
                System.out.println("[" + resolverType + " Fallback] Emergency generation also failed: " + emergencyException.getMessage());
                
                // 最后的兜底：基于模块说明符生成基本路径
                return moduleSpecifier + ".ts";
            }
        }
    }
    
    /**
     * 检查是否应该执行兜底策略
     * 
     * @param moduleSpecifier 模块说明符
     * @param isExternal 是否为外部模块
     * @return 是否应该执行兜底策略
     */
    public boolean shouldExecuteFallback(String moduleSpecifier, boolean isExternal) {
        boolean isRelativePath = pathResolver.isRelativePath(moduleSpecifier);
        return !isExternal || isRelativePath;
    }
}
