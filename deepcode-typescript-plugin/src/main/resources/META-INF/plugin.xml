<idea-plugin>
    <!-- 基本信息现在通过Gradle配置 -->

    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.modules.javascript</depends>

    <!-- 可选依赖：如果可用则启用增强功能，但不是必需的 -->
    <depends optional="true" config-file="typescript-enhanced.xml">com.intellij.modules.ultimate</depends>

    <extensions defaultExtensionNs="com.intellij">
        <!-- 注册共享的分析器服务实现 -->
        <applicationService
                serviceInterface="com.sankuai.deepcode.astplugin.analyzer.AnalyzerService"
                serviceImplementation="com.sankuai.deepcode.astplugin.analyzer.AnalyzerService"/>

        <!-- 注册TypeScript分析器服务 -->
        <applicationService
                serviceInterface="com.sankuai.deepcode.astplugin.typescript.TypeScriptAnalyzerService"
                serviceImplementation="com.sankuai.deepcode.astplugin.typescript.TypeScriptAnalyzerService"/>

        <!-- 插件启动时初始化组件 -->
        <postStartupActivity
                implementation="com.sankuai.deepcode.astplugin.typescript.TypeScriptPluginStartup"/>

        <!-- 注册工具窗口 - 使用shared-ui中的类 -->
        <toolWindow id="AST Analysis"
                   factoryClass="com.sankuai.deepcode.astplugin.ui.ASTAnalysisToolWindow"
                   anchor="right"
                   icon="AllIcons.Toolwindows.ToolWindowStructure"/>
    </extensions>

    <actions>
        <!-- 工具栏动作 - 使用shared-ui中的类 -->
        <group id="AST.AnalysisActions" text="AST Analysis" popup="true">
            <action id="AST.QuickAnalyzer"
                   class="com.sankuai.deepcode.astplugin.action.QuickAnalyzerAction"
                   text="Quick AST Analysis"
                   description="Quick AST analysis for current file"/>

            <action id="AST.ShowToolWindow"
                   class="com.sankuai.deepcode.astplugin.ui.ShowASTToolWindowAction"
                   text="Show AST Analysis Window"
                   description="Show AST Analysis tool window"/>

            <action id="AST.ExportReport"
                   class="com.sankuai.deepcode.astplugin.action.ExportReportAction"
                   text="Export AST Report"
                   description="Export AST analysis report">
                <keyboard-shortcut first-keystroke="ctrl alt E" keymap="$default"/>
            </action>

            <action id="AST.ExportProject"
                   class="com.sankuai.deepcode.astplugin.action.ExportProjectAction"
                   text="Export Project AST Report"
                   description="Export AST analysis report for entire project">
            </action>

            <add-to-group group-id="ToolsMenu" anchor="last"/>
        </group>

        <!-- 上下文菜单动作 -->
        <group id="AST.EditorPopup">
            <reference ref="AST.QuickAnalyzer"/>
            <add-to-group group-id="EditorPopupMenu" anchor="last"/>
            <separator/>
        </group>
    </actions>
</idea-plugin>

