# DeepCode TypeScript Plugin

TypeScript 语言专用的 AST 分析插件，支持精确的 TypeScript/JavaScript 代码结构分析和调用关系提取，在有 JavaScript 支持的 IDE 中提供完整的 PSI API 功能。

## 🎯 插件特性

### 核心功能
- 🚀 **完整的 TypeScript AST 解析**：支持类、接口、函数、枚举的结构分析
- 🔍 **智能函数分析**：解析函数签名、参数、类型注解、泛型约束
- 📊 **变量分析**：识别模块级变量、实例属性、类属性
- 📞 **调用关系分析**：追踪函数/方法调用，包括内置方法调用
- 📦 **模块结构支持**：识别 ES6 模块、命名空间、导入导出
- 🎯 **精确行号定位**：准确计算代码元素的位置信息

### TypeScript 语言特性支持
- ✅ **类型系统**：接口、类型别名、联合类型、交叉类型、泛型
- ✅ **装饰器支持**：类装饰器、方法装饰器、属性装饰器、参数装饰器
- ✅ **类继承关系**：类继承、接口实现、抽象类、mixin 模式
- ✅ **模块系统**：ES6 import/export、动态导入、命名空间、模块声明
- ✅ **现代特性**：async/await、生成器、解构赋值、模板字符串
- ✅ **高级类型**：条件类型、映射类型、索引类型、递归类型

## 🏗️ 适用场景

### 目标 IDE
- **WebStorm** - 主要目标平台
- **IntelliJ IDEA Ultimate** (带 JavaScript 插件) - 完全兼容
- **IntelliJ IDEA Community** (带 JavaScript 插件) - 基本兼容

### 支持的 TypeScript/JavaScript 版本
- TypeScript 4.0+
- TypeScript 4.5+
- TypeScript 5.0+ (最新特性)
- JavaScript ES2015+
- JavaScript ES2020+
- Node.js 16+ (运行时)

## 📦 安装和配置

### 前置条件
确保 IDE 中已安装并启用 JavaScript 插件：
- WebStorm 自带 TypeScript 支持
- IntelliJ IDEA 需要启用 JavaScript 和 TypeScript 插件

### 安装方式一：构建安装
```bash
# 构建 TypeScript 插件
./gradlew :deepcode-typescript-plugin:build

# 生成的插件位于
# deepcode-typescript-plugin/build/distributions/deepcode-typescript-plugin-*.zip
```

### 安装方式二：开发模式
```bash
# 启动带插件的 WebStorm 开发环境
./gradlew :deepcode-typescript-plugin:runIde

# 在沙盒环境中测试插件
./gradlew :deepcode-typescript-plugin:runIdeSandboxed
```

### 手动安装
1. 打开 WebStorm 或 IntelliJ IDEA (带 JavaScript 插件)
2. `File` → `Settings` → `Plugins`
3. 齿轮图标 → `Install Plugin from Disk...`
4. 选择构建生成的 zip 文件
5. 重启 IDE

## 🚀 使用方法

### 基本使用

1. **打开 TypeScript 文件**
   - 在 IDE 中打开任意 `.ts`、`.tsx`、`.js`、`.jsx` 文件
   - 确保文件属于一个有效的 TypeScript/JavaScript 项目
   - 建议配置 tsconfig.json 或 jsconfig.json

2. **启动分析**
   ```
   方式一：右键菜单 → "Quick AST Analyzer"
   方式二：快捷键 Ctrl+Alt+Shift+T
   方式三：Tools → AST Analysis → Quick Analysis
   ```

3. **查看结果**
   - 弹窗显示分析摘要
   - 工具窗口显示详细信息
   - 支持导出为 JSON 格式

### 工具窗口使用

通过 `View` → `Tool Windows` → `AST Analysis` 打开工具窗口，包含：

- **节点树**：显示类、接口、函数、枚举的层级结构
- **调用关系**：显示函数/方法间的调用关系
- **类型关系**：显示类型继承和实现关系
- **统计信息**：显示代码结构统计数据

### 导出功能

支持将分析结果导出为：
- **JSON 格式**：完整的结构化数据
- **CSV 格式**：适合数据分析
- **HTML 报告**：可视化分析报告

## 🔍 分析示例

### TypeScript 类分析

对于以下 TypeScript 代码：

```typescript
// user-service.ts
import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';

export interface IUser {
  id: number;
  name: string;
  email: string;
  createdAt: Date;
}

export interface IUserRepository {
  findById(id: number): Promise<IUser | null>;
  findAll(): Promise<IUser[]>;
  create(user: Omit<IUser, 'id'>): Promise<IUser>;
}

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: IUserRepository
  ) {}

  async getUserById(userId: number): Promise<IUser | null> {
    if (userId <= 0) {
      throw new Error('Invalid user ID');
    }

    const user = await this.userRepository.findById(userId);
    return user;
  }

  async getAllUsers(): Promise<IUser[]> {
    return this.userRepository.findAll();
  }

  async createUser(userData: Omit<IUser, 'id' | 'createdAt'>): Promise<IUser> {
    const newUser = {
      ...userData,
      createdAt: new Date()
    };

    return this.userRepository.create(newUser);
  }
}

export type UserCreateRequest = Pick<IUser, 'name' | 'email'>;
export type UserUpdateRequest = Partial<Pick<IUser, 'name' | 'email'>>;
```

### 分析结果

**识别的节点**：
```
1. INTERFACE: user-service.IUser
   - 位置: user-service.ts:4
   - 属性: id: number, name: string, email: string, createdAt: Date

2. INTERFACE: user-service.IUserRepository
   - 位置: user-service.ts:11
   - 方法: findById, findAll, create

3. CLASS: user-service.UserService
   - 位置: user-service.ts:18
   - 装饰器: @Injectable
   - 实现: 无

4. METHOD: user-service.UserService.constructor
   - 位置: user-service.ts:19
   - 参数: userRepository: IUserRepository
   - 访问修饰符: private readonly

5. METHOD: user-service.UserService.getUserById
   - 位置: user-service.ts:24
   - 参数: userId: number
   - 返回类型: Promise<IUser | null>
   - 异步: true

6. METHOD: user-service.UserService.getAllUsers
   - 位置: user-service.ts:32
   - 返回类型: Promise<IUser[]>
   - 异步: true

7. TYPE_ALIAS: user-service.UserCreateRequest
   - 位置: user-service.ts:43
   - 类型定义: Pick<IUser, 'name' | 'email'>

8. TYPE_ALIAS: user-service.UserUpdateRequest
   - 位置: user-service.ts:44
   - 类型定义: Partial<Pick<IUser, 'name' | 'email'>>
```

**调用关系**：
```
1. UserService.getUserById() → this.userRepository.findById() [外部调用]
   - 位置: user-service.ts:29

2. UserService.getUserById() → Error.constructor() [外部调用]
   - 位置: user-service.ts:26

3. UserService.getAllUsers() → this.userRepository.findAll() [外部调用]
   - 位置: user-service.ts:33

4. UserService.createUser() → this.userRepository.create() [外部调用]
   - 位置: user-service.ts:41

5. UserService.createUser() → Date.constructor() [外部调用]
   - 位置: user-service.ts:39
```

## 🔧 特殊功能

### 模块和导入分析

自动识别 TypeScript/JavaScript 模块结构：

```typescript
// ES6 导入
import { Injectable, Controller } from '@nestjs/common';
import * as fs from 'fs';
import path from 'path';

// 动态导入
const component = await import('./components/UserComponent');

// 命名空间
namespace Utils {
  export function formatDate(date: Date): string {
    return date.toISOString();
  }
}
```

### 类型系统分析

支持 TypeScript 复杂类型分析：

```typescript
// 泛型约束
interface Repository<T extends BaseEntity> {
  save(entity: T): Promise<T>;
  findById<K extends keyof T>(id: T[K]): Promise<T | null>;
}

// 条件类型
type NonNullable<T> = T extends null | undefined ? never : T;

// 映射类型
type Readonly<T> = {
  readonly [P in keyof T]: T[P];
};

// 联合和交叉类型
type Status = 'pending' | 'approved' | 'rejected';
type ApiResponse<T> = { success: true; data: T } | { success: false; error: string };
```

### 装饰器处理

支持多种装饰器分析：

```typescript
// 类装饰器
@Component({
  selector: 'app-user',
  templateUrl: './user.component.html'
})
export class UserComponent {
  @Input() user: IUser;
  @Output() userClick = new EventEmitter<IUser>();

  @ViewChild('userForm') userForm: ElementRef;

  @HostListener('click', ['$event'])
  onClick(event: MouseEvent) {
    this.userClick.emit(this.user);
  }
}
```

### 内外部调用区分

**内部调用判定条件**：
- 被调用函数在同一模块中定义
- 被调用类在同一项目中定义
- 项目内部的模块调用

**外部调用判定条件**：
- JavaScript/Node.js 内置对象：`console.log()`, `JSON.parse()`, `Array.from()` 等
- 浏览器 API：`document.*`, `window.*`, `fetch()`, `localStorage.*`
- Node.js API：`fs.*`, `path.*`, `http.*`, `process.*`
- 第三方库：`lodash.*`, `axios.*`, `react.*`, `@nestjs/*`

## 🛠️ 开发指南

### 环境搭建

```bash
# 克隆项目
git clone <repository>
cd deepcode-ast-plugin

# 确保 Node.js 环境
node --version  # Node.js 16+ 推荐
npm --version   # 或 yarn/pnpm

# 构建项目
./gradlew :deepcode-typescript-plugin:build

# 运行开发环境
./gradlew :deepcode-typescript-plugin:runIde
```

### 代码结构

```
deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/
├── TypeScriptASTAnalyzer.java          # 主分析器实现
├── TypeScriptNodeVisitor.java          # TypeScript PSI 节点访问器
├── TypeScriptCallAnalyzer.java         # 调用关系分析器
├── TypeScriptImportAnalyzer.java       # 导入语句分析器
├── TypeScriptTypeAnalyzer.java         # 类型注解分析器
└── util/
    ├── TypeScriptPsiUtils.java         # TypeScript PSI 工具方法
    ├── TypeScriptSignatureUtils.java   # 函数签名工具
    └── TypeScriptModuleDetector.java   # 模块检测
```

### 核心分析流程

```java
public class TypeScriptASTAnalyzer implements ASTAnalyzer {

    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        return ReadAction.compute(() -> {
            AnalysisResult result = new AnalysisResult();

            // 1. 分析文件结构
            analyzeFileStructure(psiFile, result);

            // 2. 分析调用关系
            analyzeCallRelations(psiFile, result);

            // 3. 分析导入关系
            analyzeImports(psiFile, result);

            // 4. 分析类型系统
            analyzeTypeSystem(psiFile, result);

            // 5. 计算统计信息
            calculateStatistics(result);

            return result;
        });
    }
}
```

### 调试技巧

#### 启用调试模式

```bash
# 方式一：环境变量
export AST_ANALYZER_DEBUG=true
./gradlew :deepcode-typescript-plugin:runIde

# 方式二：JVM 参数
./gradlew :deepcode-typescript-plugin:runIde -Dast.analyzer.debug=true
```

#### 查看调试输出

```java
private void debugLog(String message, Object... args) {
    if (isDebugEnabled()) {
        logger.info("[TYPESCRIPT-DEBUG] " + String.format(message, args));
    }
}

// 使用示例
debugLog("Analyzing interface: %s at line %d", interfaceName, lineNumber);
debugLog("Found call: %s -> %s", caller, callee);
```

## ⚠️ 使用注意事项

### 项目配置

1. **确保 TypeScript 配置正确**
   - 配置 tsconfig.json 或 jsconfig.json
   - 设置正确的编译目标和模块系统
   - 确保类型声明文件可访问

2. **处理依赖库**
   - 安装项目依赖：`npm install` 或 `yarn install`
   - 确保 @types 类型声明包已安装
   - 配置正确的模块解析策略

### 性能考虑

1. **大文件处理**
   - TypeScript 文件可能较大且复杂
   - 类型推断可能消耗较多时间
   - 考虑分析深度设置

2. **内存使用**
   - TypeScript PSI 树较为复杂
   - 类型系统分析消耗额外内存

### 限制说明

1. **动态特性**
   - 运行时动态创建的类型无法分析
   - eval() 和 动态导入的某些情况无法完全分析
   - 复杂的类型操作可能无法完全识别

2. **类型推断**
   - 复杂的泛型约束可能推断不准确
   - any 类型会影响分析精度
   - 第三方库的类型定义质量影响分析结果

## 🔗 相关文档

- [项目总览](../README.md)
- [共享核心模块](../shared-core/README.md)
- [共享UI模块](../shared-ui/README.md)
- [Java插件](../deepcode-java-plugin/README.md)
- [Python插件](../deepcode-python-plugin/README.md)
- [开发文档](DEVELOPMENT.md)
- [使用手册](USAGE.md)

## 🐛 问题排查

### 常见问题

1. **插件未加载**
   - 检查 WebStorm/IDEA 版本兼容性（>=2024.1.4）
   - 确认 JavaScript 插件已启用
   - 查看 IDE 错误日志

2. **分析结果为空**
   - 检查文件是否为有效 TypeScript/JavaScript 源文件
   - 确认项目配置文件（tsconfig.json）正确
   - 启用调试模式查看详细过程

3. **类型分析不准确**
   - 检查类型声明文件是否完整
   - 确认编译器选项设置正确
   - 查看类型推断日志

4. **调用关系不完整**
   - 检查模块解析配置
   - 确认依赖库正确安装
   - 验证导入路径正确性

### 日志查看

IDE 日志位置：
- **Windows**: `%APPDATA%\JetBrains\<IDE>\<version>\log\idea.log`
- **macOS**: `~/Library/Logs/JetBrains/<IDE>/`
- **Linux**: `~/.cache/JetBrains/<IDE>/log/`

搜索关键词：
- `TypeScriptASTAnalyzer`
- `TYPESCRIPT-DEBUG`
- `deepcode.astplugin.typescript`

通过 DeepCode TypeScript Plugin，您可以深入分析 TypeScript/JavaScript 代码的结构和调用关系，充分利用 TypeScript 的类型系统和现代 JavaScript 特性，提高代码理解和重构效率。

