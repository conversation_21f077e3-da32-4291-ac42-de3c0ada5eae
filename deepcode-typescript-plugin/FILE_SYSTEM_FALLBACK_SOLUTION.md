# 文件系统回退解决方案

## 问题分析

从日志分析可以看出，所有的 PSI 解析方法都失败了：

1. **ES6ImportDeclaration 没有 `findReferencedElements` 方法**
2. **FromClause 的 `resolve()` 方法失败**
3. **Reference 是 `JSModuleCompletionOnlyReference` 类型，无法解析到实际文件**

这说明 IntelliJ 的 PSI 系统无法直接解析外部 npm 包的引用，但我们可以通过文件系统查找来解决这个问题。

## 解决方案

### 核心思想

**当所有 PSI 解析方法都失败时，对于外部模块使用文件系统直接查找 node_modules 中的文件。**

### 技术实现

#### 1. 新增文件系统回退机制

```java
// 方法3: 如果所有 PSI 方法都失败，对于外部模块尝试文件系统查找
if (isExternal) {
    String fileSystemPath = resolveExternalModuleByFileSystem(importElement, moduleSpecifier);
    if (fileSystemPath != null) {
        return fileSystemPath;
    }
}
```

#### 2. resolveExternalModuleByFileSystem() 方法

```java
private static String resolveExternalModuleByFileSystem(PsiElement context, String moduleSpecifier) {
    // 获取项目根目录
    Project project = context.getProject();
    String projectBasePath = project.getBasePath();
    
    // 构建 node_modules 路径
    String nodeModulesPath = projectBasePath + "/node_modules";
    File nodeModulesDir = new File(nodeModulesPath);
    
    if (!nodeModulesDir.exists()) {
        return null;
    }
    
    // 解析模块路径
    return resolveNodeModulePath(nodeModulesPath, moduleSpecifier);
}
```

#### 3. resolveNodeModulePath() 方法

```java
private static String resolveNodeModulePath(String nodeModulesPath, String moduleSpecifier) {
    // 处理 scoped 包 (如 @ant-design/icons)
    String modulePath;
    if (moduleSpecifier.startsWith("@")) {
        // Scoped package: @ant-design/icons -> node_modules/@ant-design/icons
        modulePath = nodeModulesPath + "/" + moduleSpecifier;
    } else {
        // Regular package: react -> node_modules/react
        modulePath = nodeModulesPath + "/" + moduleSpecifier;
    }
    
    File moduleDir = new File(modulePath);
    if (!moduleDir.exists()) {
        return null;
    }
    
    // 尝试查找入口文件
    String[] possibleEntries = {
        "index.d.ts",    // TypeScript 定义文件
        "index.ts",      // TypeScript 源文件
        "index.js",      // JavaScript 文件
        "lib/index.d.ts", // 常见的 lib 目录
        "lib/index.ts",
        "lib/index.js",
        "dist/index.d.ts", // 常见的 dist 目录
        "dist/index.ts",
        "dist/index.js"
    };
    
    for (String entry : possibleEntries) {
        String entryPath = modulePath + "/" + entry;
        File entryFile = new File(entryPath);
        if (entryFile.exists()) {
            return entryPath;
        }
    }
    
    // 尝试读取 package.json 获取入口文件
    return parsePackageJsonEntry(modulePath + "/package.json", modulePath);
}
```

#### 4. parsePackageJsonEntry() 方法

```java
private static String parsePackageJsonEntry(String packageJsonPath, String modulePath) {
    try {
        String content = new String(Files.readAllBytes(Paths.get(packageJsonPath)));
        
        // 简单的 JSON 解析（查找 types, typings, main 字段）
        String[] entryFields = {"\"types\":", "\"typings\":", "\"main\":"};
        
        for (String field : entryFields) {
            int fieldIndex = content.indexOf(field);
            if (fieldIndex != -1) {
                // 提取字段值
                String entryFile = extractJsonFieldValue(content, fieldIndex + field.length());
                if (entryFile != null) {
                    String fullPath = modulePath + "/" + entryFile;
                    File file = new File(fullPath);
                    if (file.exists()) {
                        return fullPath;
                    }
                }
            }
        }
    } catch (Exception e) {
        // 解析失败，忽略
    }
    return null;
}
```

## 支持的模块类型

### 1. 标准 npm 包
- **模块**: `react`
- **路径**: `node_modules/react/index.d.ts`

### 2. Scoped 包
- **模块**: `@ant-design/icons`
- **路径**: `node_modules/@ant-design/icons/index.d.ts`

### 3. 子路径导入
- **模块**: `antd/lib/checkbox`
- **路径**: `node_modules/antd/lib/checkbox/index.d.ts`

### 4. 自定义入口点
- **模块**: `some-package`
- **package.json**: `"main": "dist/bundle.js"`
- **路径**: `node_modules/some-package/dist/bundle.js`

## 查找优先级

### 1. 常见入口文件（按优先级）
1. `index.d.ts` - TypeScript 定义文件（最优先）
2. `index.ts` - TypeScript 源文件
3. `index.js` - JavaScript 文件
4. `lib/index.d.ts` - lib 目录下的定义文件
5. `lib/index.ts` - lib 目录下的源文件
6. `lib/index.js` - lib 目录下的 JS 文件
7. `dist/index.d.ts` - dist 目录下的定义文件
8. `dist/index.ts` - dist 目录下的源文件
9. `dist/index.js` - dist 目录下的 JS 文件

### 2. package.json 字段（按优先级）
1. `"types"` - TypeScript 定义文件入口
2. `"typings"` - TypeScript 定义文件入口（旧版）
3. `"main"` - 主入口文件

## 预期效果

修复后，所有外部模块导入都应该能正确解析 `targetFilePath`：

### 1. React 导入
```json
{
  "statement": "import React, { forwardRef } from 'react';",
  "targetFilePath": "/path/to/project/node_modules/react/index.d.ts"  // ✅ 正确
}
```

### 2. Antd 导入
```json
{
  "statement": "import { Checkbox, Tooltip } from 'antd';",
  "targetFilePath": "/path/to/project/node_modules/antd/index.d.ts"  // ✅ 正确
}
```

### 3. Scoped 包导入
```json
{
  "statement": "import { QuestionCircleFilled } from '@ant-design/icons';",
  "targetFilePath": "/path/to/project/node_modules/@ant-design/icons/index.d.ts"  // ✅ 正确
}
```

### 4. 子路径导入
```json
{
  "statement": "import Checkbox from 'antd/lib/checkbox';",
  "targetFilePath": "/path/to/project/node_modules/antd/lib/checkbox/index.d.ts"  // ✅ 正确
}
```

## 调试信息

新的日志会显示详细的文件系统解析过程：

```
[DeepCode] Trying file system resolution for external module...
[DeepCode] === resolveExternalModuleByFileSystem() for: react ===
[DeepCode] Project base path: /path/to/project
[DeepCode] Found node_modules: /path/to/project/node_modules
[DeepCode] === resolveNodeModulePath() for: react ===
[DeepCode] Searching for module at: /path/to/project/node_modules/react
[DeepCode] Found entry file: /path/to/project/node_modules/react/index.d.ts
[DeepCode] Resolved module path: /path/to/project/node_modules/react/index.d.ts
[DeepCode] File system resolution found: /path/to/project/node_modules/react/index.d.ts
```

## 技术优势

### 1. 可靠性
- **直接文件系统访问**: 不依赖 PSI 的复杂解析逻辑
- **多种入口点支持**: 支持各种常见的包结构

### 2. 准确性
- **实际文件验证**: 确保文件真实存在
- **package.json 解析**: 支持自定义入口点

### 3. 兼容性
- **标准包结构**: 支持标准的 npm 包结构
- **Scoped 包**: 支持 @scope/package 格式
- **子路径导入**: 支持深层路径导入

### 4. 性能
- **文件系统缓存**: 操作系统级别的文件系统缓存
- **快速查找**: 直接路径访问，无需复杂的 PSI 遍历

这个文件系统回退解决方案应该能解决所有外部模块的 `targetFilePath` 解析问题，特别是当 PSI 无法解析时的情况。
