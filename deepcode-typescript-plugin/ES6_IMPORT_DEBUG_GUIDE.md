# ES6 导入调试指南

## 当前问题

所有外部模块的 ES6 导入的 `targetFilePath` 都是 `null`：

```json
{
  "statement": "import React, { forwardRef } from 'react';",
  "targetFilePath": null  // ❌ 应该有值
},
{
  "statement": "import { Checkbox, Tooltip } from 'antd';",
  "targetFilePath": null  // ❌ 应该有值
}
```

## 新增的调试功能

我已经添加了三层 ES6 导入解析策略，每一层都有详细的日志输出：

### 第一层：反射调用 findReferencedElements()

```
[DeepCode] ES6 import detected, trying to get referenced elements...
[DeepCode] ES6 import found X referenced elements via reflection
```

或者失败时：
```
[DeepCode] ES6 findReferencedElements method not available: NoSuchMethodException
```

### 第二层：FromClause 解析

```
[DeepCode] Trying to resolve via FromClause...
[DeepCode] FromClause type: ES6FromClause
[DeepCode] === resolveES6FromClause() for: react ===
[DeepCode] FromClause resolved to: TypeScriptFile
```

### 第三层：直接 ES6 解析

```
[DeepCode] Trying direct ES6 resolution methods...
[DeepCode] === tryDirectES6Resolution() for: react ===
[DeepCode] ES6ImportDeclaration resolved to: SomeElementType
```

或者详细的子元素分析：
```
[DeepCode] ES6ImportDeclaration has 5 children
[DeepCode] Checking child: ES6ImportSpecifier - React
[DeepCode] Checking child: ES6FromClause - from 'react'
[DeepCode] Child resolved to: TypeScriptFile
```

### 第四层：增强文件解析

```
[DeepCode] PSI reference resolution failed, trying enhanced file resolution...
[DeepCode] Enhanced resolution found: /path/to/node_modules/react/index.d.ts
```

## 测试步骤

### 1. 运行测试

运行您的测试用例，特别关注这些导入语句：
- `import React, { forwardRef } from 'react';`
- `import { Checkbox, Tooltip } from 'antd';`
- `import { connect, mapProps } from '@sfe/xform-core';`
- `import { QuestionCircleFilled } from '@ant-design/icons';`

### 2. 收集日志

查看控制台输出，收集所有以 `[DeepCode]` 开头的日志，特别是：

**对于每个 ES6 导入，应该看到：**
```
[DeepCode] === resolveTypeScriptTargetFilePath() for: react (isExternal: true) ===
[DeepCode] ES6 import detected, trying to get referenced elements...
[DeepCode] ES6 findReferencedElements method not available: NoSuchMethodException
[DeepCode] Trying to resolve via FromClause...
[DeepCode] FromClause type: ES6FromClause
[DeepCode] === resolveES6FromClause() for: react ===
[DeepCode] FromClause resolved to: TypeScriptFile
[DeepCode] Found target file: /path/to/node_modules/react/index.d.ts
```

### 3. 关键检查点

**检查点 1: ES6 导入检测**
- 确认看到 `ES6 import detected, trying to get referenced elements...`
- 如果没有看到，说明导入类型判断有问题

**检查点 2: 反射方法调用**
- 查看是否有 `ES6 import found X referenced elements via reflection`
- 如果是 `method not available`，这是正常的，会进入下一层

**检查点 3: FromClause 解析**
- 确认看到 `Trying to resolve via FromClause...`
- 查看 `FromClause type` 是什么
- 确认是否进入了 `resolveES6FromClause`

**检查点 4: 直接解析**
- 如果前面失败，应该看到 `Trying direct ES6 resolution methods...`
- 查看子元素分析过程

**检查点 5: 文件发现**
- 最终应该看到 `Found target file: /path/to/some/file`
- 如果没有，说明所有解析方法都失败了

## 可能的问题和解决方案

### 问题 1: 没有进入 ES6 分支
**症状**: 没有看到 `ES6 import detected`
**原因**: `importElement instanceof ES6ImportDeclaration` 判断失败
**解决**: 检查导入元素的实际类型

### 问题 2: FromClause 为 null
**症状**: 看到 `ES6 import has no FromClause`
**原因**: ES6ImportDeclaration 结构异常
**解决**: 需要检查 ES6ImportDeclaration 的结构

### 问题 3: 所有解析方法都失败
**症状**: 看到所有错误消息，但没有 `Found target file`
**原因**: PSI 无法解析这些外部模块
**解决**: 可能需要使用文件系统查找作为最后的回退

### 问题 4: 找到文件但 targetFilePath 仍为 null
**症状**: 看到 `Found target file` 但结果仍为 null
**原因**: 路径转换或返回逻辑有问题
**解决**: 检查 `getProjectRelativeFilePath` 和 `convertToProjectRelativePath` 方法

## 预期的正确日志流程

对于 `import React from 'react';`，预期的日志应该是：

```
[DeepCode] === resolveTypeScriptTargetFilePath() for: react (isExternal: true) ===
[DeepCode] ES6 import detected, trying to get referenced elements...
[DeepCode] ES6 findReferencedElements method not available: NoSuchMethodException
[DeepCode] Trying to resolve via FromClause...
[DeepCode] FromClause type: ES6FromClause
[DeepCode] === resolveES6FromClause() for: react ===
[DeepCode] FromClause resolved to: TypeScriptFile
[DeepCode] Analyzing referenced element: TypeScriptFile
[DeepCode] Found target file: /path/to/project/node_modules/react/index.d.ts
[DeepCode] Using absolute path for external file: /path/to/project/node_modules/react/index.d.ts
```

## 下一步

1. **运行测试**: 执行您的测试用例
2. **收集完整日志**: 复制所有相关的 `[DeepCode]` 日志
3. **分析日志**: 对比实际日志和预期日志，找出差异
4. **定位问题**: 确定在哪个步骤失败了

请提供完整的日志输出，这样我就能准确定位问题并进行针对性修复。特别关注：
- ES6 导入是否被正确检测
- 哪种解析方法被尝试了
- 是否找到了目标文件
- 如果找到了文件，为什么 targetFilePath 仍为 null

这些详细的日志将帮助我们快速定位并解决问题。
