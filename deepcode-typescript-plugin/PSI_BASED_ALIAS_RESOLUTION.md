# 基于 PSI 的别名解析修复

## 问题分析

根据用户反馈，之前的别名解析存在以下问题：

1. **写死别名枚举**: 在 `isNpmPackage` 方法中硬编码了项目别名列表，不够灵活
2. **别名识别错误**: `@page/actForm/utils` 被错误识别为外部模块
3. **targetFilePath 为 null**: 无法正确解析到实际的文件路径
4. **相对路径解析错误**: 相对路径计算不准确

## 解决方案

### 核心思想

**完全依赖 PSI 的文件跳转能力来解析路径，而不是写死别名枚举。**

通过 IntelliJ 的 PSI 接口：
1. 使用 `importStatement.findReferencedElements()` 获取引用的元素
2. 通过 `element.getContainingFile().getVirtualFile()` 获取实际文件路径
3. 基于文件路径判断是否在项目内部

### 技术实现

#### 1. 重构 ImportClassifier.isExternalModuleWithPSI()

```java
public static boolean isExternalModuleWithPSI(TypeScriptImportStatement importStatement, String moduleSpecifier) {
    // 方法1: 使用 PSI 的引用解析能力
    Collection<? extends PsiElement> referencedElements = importStatement.findReferencedElements();
    
    if (!referencedElements.isEmpty()) {
        for (PsiElement element : referencedElements) {
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null && containingFile.getVirtualFile() != null) {
                VirtualFile virtualFile = containingFile.getVirtualFile();
                
                // 判断文件是否在项目内部
                boolean isInternal = isFileInProject(importStatement, virtualFile);
                return !isInternal;
            }
        }
    }
    
    // 方法2: 如果 PSI 引用解析失败，使用增强的文件解析
    ImportUtils.ResourceResolutionResult resolutionResult =
        ImportUtils.resolveResourceWithPSI(importStatement, moduleSpecifier);
    
    if (resolutionResult.getAbsolutePath() != null) {
        return !resolutionResult.isInternal();
    }
    
    // 方法3: 回退到简单分类
    return isExternalModule(moduleSpecifier);
}
```

#### 2. 新增 isFileInProject() 方法

```java
private static boolean isFileInProject(PsiElement contextElement, VirtualFile virtualFile) {
    // 1. 获取项目根目录
    VirtualFile projectRoot = ImportUtils.getProjectRoot(contextElement);
    String projectRootPath = projectRoot.getPath();
    String filePath = virtualFile.getPath();
    
    // 2. 检查文件是否在项目根目录下
    if (!filePath.startsWith(projectRootPath)) {
        return false; // 外部文件
    }
    
    // 3. 排除项目内的 node_modules（外部依赖）
    if (filePath.contains("/node_modules/")) {
        return false; // npm 包
    }
    
    return true; // 项目内部文件
}
```

#### 3. 改进 ImportUtils.resolveModuleFile()

```java
private static VirtualFile resolveModuleFile(PsiElement element, String moduleSpecifier,
                                           VirtualFile currentFile, VirtualFile projectRoot) {
    // 1. 处理相对路径
    if (moduleSpecifier.startsWith("./") || moduleSpecifier.startsWith("../")) {
        // 相对路径解析逻辑
    }
    
    // 2. 使用增强的模块解析系统（处理所有类型的别名）
    VirtualFile resolved = tryIntellijModuleResolution(element, moduleSpecifier);
    if (resolved != null) {
        return resolved;
    }
    
    return null;
}
```

#### 4. 删除硬编码的别名枚举

移除了 `isNpmPackage()` 方法中的硬编码别名列表：
```java
// 删除了这些硬编码的别名
String[] commonProjectAliases = {
    "@page", "@components", "@utils", "@services", "@assets", 
    // ...
};
```

## 优势

### 1. 灵活性
- **自动适应**: 无需手动维护别名列表，自动适应项目的别名配置
- **通用性**: 支持任何类型的别名（`@page/`, `@components/`, `~/`, 等）
- **扩展性**: 支持 tsconfig.json paths 和 webpack alias 配置

### 2. 准确性
- **PSI 驱动**: 完全依赖 IntelliJ 的 PSI 解析能力，与 IDE 的跳转行为一致
- **文件验证**: 实际验证文件是否存在和可访问
- **路径解析**: 正确解析相对路径和绝对路径

### 3. 可靠性
- **多层回退**: PSI 解析 → 增强文件解析 → 简单分类
- **异常处理**: 完善的错误处理和日志记录
- **兼容性**: 保持与现有代码的兼容性

## 预期效果

修复后，之前的问题应该得到解决：

```json
{
  "statement": "import { replaceToPlainStr } from '@page/actForm/utils';",
  "isExternal": false,  // ✅ 正确识别为内部模块
  "targetFilePath": "static/page/actForm/utils/index.ts"  // ✅ 正确的目标文件路径
}
```

### 支持的场景

1. **标准别名**: `@/components/Button` → `src/components/Button.tsx`
2. **自定义别名**: `@page/actForm/utils` → `static/page/actForm/utils/index.ts`
3. **相对路径**: `./styles.css` → `static/components/activity/CheckboxGroupPro/styles.css`
4. **外部模块**: `antd/lib/checkbox` → `node_modules/antd/lib/checkbox/index.d.ts`

## 测试建议

1. **别名测试**: 验证各种项目别名是否正确识别
2. **路径解析测试**: 确认 `targetFilePath` 是否正确
3. **PSI 跳转测试**: 验证解析结果与 IDE 跳转行为一致
4. **性能测试**: 确保 PSI 解析不会影响性能

## 注意事项

1. **PSI 依赖**: 解析质量依赖于 IntelliJ PSI 的解析能力
2. **项目配置**: 确保项目有正确的 TypeScript/JavaScript 配置
3. **文件索引**: 需要 IntelliJ 完成文件索引才能正确解析
4. **错误处理**: 当 PSI 解析失败时会回退到简单分类

这种基于 PSI 的方法更加智能和灵活，能够自动适应各种项目配置，无需手动维护别名列表。
