// 测试相对路径 require 的修复
// 这个文件专门测试相对路径解析的问题

// 1. 当前目录的相对路径
const utils = require('./utils');
const config = require('./config');

// 2. 上级目录的相对路径
const parentUtils = require('../utils');
const parentConfig = require('../config');

// 3. 多级上级目录
const rootUtils = require('../../utils');
const deepUtils = require('../../../utils');

// 4. 复杂的相对路径（问题案例）
const vueLib = require('../../../../static/lib/vue/2.5.11/vue.common.js');
const fileDownload = require('../modules/filedownload/jquery.fileDownload');
const jqueryWidget = require('./vendor/jquery.ui.widget');

// 5. 相对路径到 TypeScript 文件
const tsUtils = require('./typescript-utils');
const tsConfig = require('../typescript-config');

// 6. 相对路径到 Vue 文件
const vueComponent = require('./components/MyComponent.vue');
const vueUtils = require('../vue-utils');

// 7. 相对路径到 index 文件
const moduleIndex = require('./modules');
const libIndex = require('../lib');

console.log('Testing relative path require resolution');

// 使用这些导入
console.log('utils:', typeof utils);
console.log('config:', typeof config);
console.log('vueLib:', typeof vueLib);
console.log('fileDownload:', typeof fileDownload);
console.log('jqueryWidget:', typeof jqueryWidget);

module.exports = {
    utils,
    config,
    vueLib,
    fileDownload,
    jqueryWidget,
    tsUtils,
    vueComponent
};
