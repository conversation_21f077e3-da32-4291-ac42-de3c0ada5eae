// 测试 CommonJS require 导入的 targetFilePath 解析

// 1. 外部三方库 require
const React = require('react');
const lodash = require('lodash');
const moment = require('moment');
const axios = require('axios');

// 2. 相对路径 require
const utils = require('./utils');
const config = require('../config');
const helpers = require('../../helpers');

// 3. 别名路径 require
const pageUtils = require('@page/actForm/utils');
const components = require('@components/Button');

// 4. Node.js 内置模块 require
const fs = require('fs');
const path = require('path');
const http = require('http');

// 5. 复杂的 require 语句
const { 
    Component, 
    useState, 
    useEffect 
} = require('react');

// 6. 动态 require
const dynamicModule = require(process.env.NODE_ENV === 'production' ? 'prod-module' : 'dev-module');

// 7. 条件 require
if (typeof window !== 'undefined') {
    const browserUtils = require('./browser-utils');
}

// 8. 嵌套 require
const nestedRequire = require(require('./config').modulePath);

// 9. 解构赋值 require
const { merge, cloneDeep } = require('lodash');

// 10. 重命名 require
const $ = require('jquery');

console.log('CommonJS require test file loaded');

module.exports = {
    React,
    lodash,
    utils,
    pageUtils
};
