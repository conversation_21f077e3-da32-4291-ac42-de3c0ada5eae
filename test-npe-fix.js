// 测试 NPE 修复的文件
// 这个文件包含一些可能导致 resolved 为 null 的调用表达式

// 1. 简单的方法调用，可能无法解析
const result = stations.split(',');

// 2. 链式调用，可能在中间环节解析失败
const data = obj.method().anotherMethod();

// 3. 动态属性访问
const value = obj[dynamicKey]();

// 4. 可能未定义的函数调用
const output = unknownFunction();

// 5. 复杂的嵌套调用
const complex = a.b.c.d.e();

// 6. 条件调用
const conditional = condition ? func1() : func2();

// 7. 箭头函数调用
const arrow = (() => {})();

// 8. 立即执行函数
(function() {
    return something();
})();

// 9. 模板字符串中的表达式
const template = `Result: ${getValue()}`;

// 10. 解构赋值中的调用
const { result: destructured } = getObject();

console.log('NPE fix test file loaded');
