// 测试文件：模拟截图中的复杂相对导入场景
// 当前文件路径：static/page/org/pages/StationMark/components/index.tsx

// 多级相对路径导入 - 这是问题所在的导入
import { 
    StaffLabelStationBatchMark, 
    StaffLabelStationBatchMarkProps 
} from '../../../Stations/index.hooks';

// 其他相对导入示例
import { utils } from './utils';
import { Modal } from '../Modal';
import { Form } from '../../Form';

// 测试不同类型的相对路径
import React from 'react';
import { ChangeEvent, FC, useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import Selector from '@roo/roo/Selector';
import DataPicker from '@roo/roo/DataPicker';
import { Col, Row } from '@roo/roo/Grid';
import Panel from '@roo/roo/Panel';
import Toast from '@roo/roo/Toast';
import Box from '@roo/roo/Box';
import Loading from '@roo/roo/Loading';
import CheckBox from '@roo/roo/CheckBox';
import MultiMarkVerifyMsg from '../MultiMarkVerifyMsg';

// 这个导入应该能够正确解析到 static/page/org/Stations/index.hooks.ts
const StationMarkComponent: FC = () => {
    return (
        <div>
            <StaffLabelStationBatchMark />
        </div>
    );
};

export default StationMarkComponent;
