# 开发说明文档

## 🏗️ 项目架构

### 整体架构设计

本项目提供了一套统一的 AST（抽象语法树）分析框架，支持多种编程语言的代码分析。采用模块化设计，易于扩展和维护。

```
src/main/java/com/sankuai/deepcode/astplugin/
├── analyzer/           # 分析器核心
│   ├── JavaASTAnalyzer.java     # Java 分析器
│   └── PythonASTAnalyzer.java   # Python 分析器
├── model/              # 数据模型
│   ├── AnalysisNode.java        # 分析节点
│   ├── AnalysisResult.java      # 分析结果
│   └── CallRelation.java        # 调用关系
├── ui/                 # 用户界面
│   └── ASTAnalysisAction.java   # UI 集成
└── action/             # 操作处理
    └── QuickASTAnalyzerAction.java
```

### 核心组件说明

#### 1. 数据模型层 (`model` 包)

**AnalysisNode** - 分析节点
- 表示代码中的可分析单元（类、方法、字段、函数、变量等）
- 包含完整的元数据：ID、类型、名称、包名、行号、签名等
- 支持多语言扩展属性：模块名、文件路径、编程语言

**CallRelation** - 调用关系
- 表示方法/函数之间的调用关系
- 支持调用实例聚合，记录所有调用位置
- 区分内部调用和外部调用

**AnalysisResult** - 分析结果容器
- 统一的结果格式，支持多语言
- 包含统计信息和错误信息
- 提供便捷的查询和过滤方法

#### 2. 分析器层 (`analyzer` 包)

**JavaASTAnalyzer** - Java 专用分析器
- 基于 IntelliJ PSI API 的精确解析
- 支持 Maven 模块自动识别
- 智能的内外部调用区分

**PythonASTAnalyzer** - Python 专用分析器
- 基于正则表达式的模式匹配
- 支持 Python 包结构识别
- 内置方法调用检测

#### 3. 用户界面层 (`ui` 包)

**ASTAnalysisAction** - UI 集成
- 提供菜单和快捷键支持
- 工具窗口集成
- 结果展示和交互

## 🛠️ 开发环境搭建

### 环境要求

```bash
# 必需环境
Java 17+                    # 编程语言
IntelliJ IDEA 2024.1.4+    # 开发 IDE
Gradle 7.6+                 # 构建工具

# 可选环境
Python 3.8+                 # Python 分析支持
Git                         # 版本控制
```

### 项目配置

#### Gradle 配置 (`build.gradle.kts`)

```kotlin
plugins {
    id("java")
    id("org.jetbrains.intellij") version "1.17.4"
}

group = "com.sankuai.deepcode"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
}

dependencies {
    // IntelliJ Platform 依赖自动管理
}

intellij {
    version.set("2024.1.4")
    type.set("IC") // IntelliJ IDEA Community Edition
    
    plugins.set(listOf(
        "com.intellij.java",           // Java 插件（必需）
        "PythonCore"                   // Python 插件（可选）
    ))
}

tasks {
    withType<JavaCompile> {
        sourceCompatibility = "17"
        targetCompatibility = "17"
        options.encoding = "UTF-8"
    }
    
    patchPluginXml {
        sinceBuild.set("241")
        untilBuild.set("242.*")
    }
    
    signPlugin {
        certificateChain.set(System.getenv("CERTIFICATE_CHAIN"))
        privateKey.set(System.getenv("PRIVATE_KEY"))
        password.set(System.getenv("PRIVATE_KEY_PASSWORD"))
    }
    
    publishPlugin {
        token.set(System.getenv("PUBLISH_TOKEN"))
    }
}
```

#### 插件配置 (`plugin.xml`)

```xml
<idea-plugin>
    <id>com.sankuai.deepcode.astplugin</id>
    <name>AST Analysis Plugin</name>
    <vendor>Sankuai</vendor>
    
    <description><![CDATA[
    多语言 AST 分析插件，支持 Java 和 Python 代码结构分析和调用关系提取。
    ]]></description>
    
    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.java</depends>
    <depends optional="true" config-file="python-support.xml">com.intellij.modules.python</depends>
    
    <extensions defaultExtensionNs="com.intellij">
        <!-- 工具窗口 -->
        <toolWindow id="AST Analysis" 
                   factoryClass="com.sankuai.deepcode.astplugin.ui.ASTAnalysisToolWindowFactory"
                   anchor="bottom"/>
    </extensions>
    
    <actions>
        <!-- 快速分析器 -->
        <action id="QuickASTAnalyzer"
                class="com.sankuai.deepcode.astplugin.action.QuickASTAnalyzerAction"
                text="Quick AST Analyzer"
                description="快速 AST 分析">
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt shift T"/>
            <add-to-group group-id="EditorPopupMenu" anchor="last"/>
        </action>
        
        <!-- 工具窗口显示 -->
        <action id="ShowASTAnalysis"
                class="com.sankuai.deepcode.astplugin.action.ShowASTAnalysisAction"
                text="Show AST Analysis"
                description="显示 AST 分析工具窗口">
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt T"/>
            <add-to-group group-id="ToolsMenu" anchor="last"/>
        </action>
    </actions>
</idea-plugin>
```

### 开发流程

#### 1. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd demo_plugins

# 检查 Java 版本
java -version  # 确保是 Java 17+

# 检查 Gradle 版本
./gradlew --version
```

#### 2. 开发环境启动

```bash
# 编译项目
./gradlew compileJava

# 启动沙盒 IDE（推荐开发方式）
./gradlew runIde

# 构建插件
./gradlew buildPlugin

# 运行测试
./gradlew test
```

#### 3. 调试配置

**启用调试模式**：
```bash
# 方式1：JVM 参数
./gradlew runIde -Dast.analyzer.debug=true

# 方式2：环境变量
export AST_ANALYZER_DEBUG=true
./gradlew runIde

# 方式3：IDE 中设置
System.setProperty("ast.analyzer.debug", "true");
```

**调试输出配置**：
```java
// 在代码中添加调试日志
private static final Logger logger = Logger.getInstance(JavaASTAnalyzer.class);

private boolean isDebugEnabled() {
    return Boolean.getBoolean("ast.analyzer.debug") || 
           "true".equals(System.getenv("AST_ANALYZER_DEBUG"));
}

private void debugLog(String message) {
    if (isDebugEnabled()) {
        logger.info("[DEBUG] " + message);
    }
}
```

## 🔧 核心开发指南

### 添加新的分析器

#### 1. 创建分析器类

```java
public class NewLanguageASTAnalyzer {
    private static final Logger logger = Logger.getInstance(NewLanguageASTAnalyzer.class);
    
    public AnalysisResult analyze(PsiFile file) {
        return ReadAction.compute(() -> {
            try {
                return performAnalysis(file);
            } catch (Exception e) {
                logger.error("Analysis failed", e);
                return createErrorResult(e);
            }
        });
    }
    
    private AnalysisResult performAnalysis(PsiFile file) {
        AnalysisResult result = new AnalysisResult();
        
        // 实现具体的分析逻辑
        analyzeStructure(file, result);
        analyzeCallRelations(file, result);
        
        return result;
    }
}
```

#### 2. 扩展节点类型

```java
public enum NodeType {
    // 现有类型
    CLASS, METHOD, FIELD, FUNCTION, VARIABLE,
    
    // 新增类型
    INTERFACE, ENUM, ANNOTATION,
    PROPERTY, DECORATOR, MODULE
}
```

#### 3. 注册分析器

```java
public class AnalyzerFactory {
    private static final Map<String, Function<PsiFile, AnalysisResult>> analyzers = new HashMap<>();
    
    static {
        analyzers.put("java", file -> new JavaASTAnalyzer().analyze(file));
        analyzers.put("py", file -> new PythonASTAnalyzer().analyze(file));
        // 注册新的分析器
        analyzers.put("js", file -> new JavaScriptASTAnalyzer().analyze(file));
    }
    
    public static AnalysisResult analyze(PsiFile file) {
        String extension = file.getVirtualFile().getExtension();
        Function<PsiFile, AnalysisResult> analyzer = analyzers.get(extension);
        
        if (analyzer != null) {
            return analyzer.apply(file);
        } else {
            return createUnsupportedResult(file);
        }
    }
}
```

### 性能优化指南

#### 1. PSI 访问优化

```java
// ✅ 正确：在 ReadAction 中访问 PSI
public AnalysisResult analyze(PsiFile file) {
    return ReadAction.compute(() -> {
        // 所有 PSI 访问都在这里
        return performAnalysis(file);
    });
}

// ❌ 错误：在 ReadAction 外访问 PSI
public AnalysisResult analyze(PsiFile file) {
    String fileName = file.getName(); // 可能导致线程安全问题
    return ReadAction.compute(() -> {
        return performAnalysis(file);
    });
}
```

#### 2. 缓存策略

```java
public class CachedAnalyzer {
    private final Map<String, AnalysisResult> cache = new ConcurrentHashMap<>();
    
    public AnalysisResult analyze(PsiFile file) {
        String fileKey = generateFileKey(file);
        
        return cache.computeIfAbsent(fileKey, key -> {
            return ReadAction.compute(() -> performAnalysis(file));
        });
    }
    
    private String generateFileKey(PsiFile file) {
        VirtualFile vFile = file.getVirtualFile();
        return vFile.getPath() + ":" + vFile.getModificationStamp();
    }
}
```

#### 3. 批量处理

```java
public class BatchAnalyzer {
    public Map<PsiFile, AnalysisResult> analyzeFiles(Collection<PsiFile> files) {
        return ReadAction.compute(() -> {
            Map<PsiFile, AnalysisResult> results = new HashMap<>();
            
            for (PsiFile file : files) {
                try {
                    results.put(file, performAnalysis(file));
                } catch (Exception e) {
                    logger.warn("Failed to analyze file: " + file.getName(), e);
                    results.put(file, createErrorResult(e));
                }
            }
            
            return results;
        });
    }
}
```

### 错误处理最佳实践

#### 1. 分层错误处理

```java
public class RobustAnalyzer {
    public AnalysisResult analyze(PsiFile file) {
        try {
            return ReadAction.compute(() -> performAnalysis(file));
        } catch (ProcessCanceledException e) {
            // 用户取消操作，不记录错误
            throw e;
        } catch (Exception e) {
            logger.error("Analysis failed for file: " + file.getName(), e);
            return createErrorResult(e);
        }
    }
    
    private AnalysisResult performAnalysis(PsiFile file) {
        AnalysisResult result = new AnalysisResult();
        
        // 安全地分析每个元素
        file.accept(new PsiRecursiveElementVisitor() {
            @Override
            public void visitElement(@NotNull PsiElement element) {
                try {
                    analyzeElement(element, result);
                } catch (Exception e) {
                    if (isDebugEnabled()) {
                        logger.warn("Failed to analyze element: " + element.getText(), e);
                    }
                    // 继续分析其他元素
                }
                super.visitElement(element);
            }
        });
        
        return result;
    }
}
```

#### 2. 优雅降级

```java
public class GracefulAnalyzer {
    public AnalysisResult analyze(PsiFile file) {
        if (!isPluginAvailable()) {
            return createFallbackAnalysis(file);
        }
        
        try {
            return performFullAnalysis(file);
        } catch (Exception e) {
            logger.warn("Full analysis failed, falling back to basic analysis", e);
            return performBasicAnalysis(file);
        }
    }
    
    private AnalysisResult createFallbackAnalysis(PsiFile file) {
        AnalysisResult result = new AnalysisResult();
        result.addWarning("Plugin not available, using fallback analysis");
        
        // 基本的文本分析
        performTextBasedAnalysis(file, result);
        
        return result;
    }
}
```

## 🧪 测试指南

### 单元测试

```java
public class JavaASTAnalyzerTest {
    private JavaASTAnalyzer analyzer;
    private PsiFile testFile;
    
    @BeforeEach
    void setUp() {
        analyzer = new JavaASTAnalyzer();
        testFile = createTestFile("TestClass.java", """
            public class TestClass {
                private String name;
                
                public void setName(String name) {
                    this.name = name;
                }
                
                public String getName() {
                    return name;
                }
            }
            """);
    }
    
    @Test
    void testClassAnalysis() {
        AnalysisResult result = analyzer.analyze(testFile);
        
        assertThat(result.getNodes()).hasSize(3); // class + 2 methods
        assertThat(result.getNodes().values())
            .extracting(AnalysisNode::getType)
            .contains(NodeType.CLASS, NodeType.METHOD, NodeType.METHOD);
    }
    
    @Test
    void testCallRelationAnalysis() {
        AnalysisResult result = analyzer.analyze(testFile);
        
        // 验证调用关系
        assertThat(result.getCallRelations()).isEmpty(); // 这个例子中没有方法调用
    }
}
```

### 集成测试

```java
public class PluginIntegrationTest {
    @Test
    void testPluginLoading() {
        // 测试插件是否正确加载
        assertThat(PluginManager.isPluginInstalled(PluginId.getId("com.sankuai.deepcode.astplugin")))
            .isTrue();
    }
    
    @Test
    void testActionRegistration() {
        // 测试 Action 是否正确注册
        ActionManager actionManager = ActionManager.getInstance();
        AnAction action = actionManager.getAction("QuickASTAnalyzer");
        
        assertThat(action).isNotNull();
        assertThat(action).isInstanceOf(QuickASTAnalyzerAction.class);
    }
}
```

## 📦 构建和发布

### 构建配置

```bash
# 开发构建
./gradlew build

# 生产构建
./gradlew buildPlugin

# 验证构建
./gradlew verifyPlugin

# 运行所有检查
./gradlew check
```

### 发布流程

```bash
# 1. 更新版本号
# 编辑 build.gradle.kts 中的 version

# 2. 构建发布版本
./gradlew buildPlugin

# 3. 测试插件
./gradlew runIde

# 4. 发布到插件市场（需要配置 token）
./gradlew publishPlugin
```

### CI/CD 配置

```yaml
# .github/workflows/build.yml
name: Build and Test

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: ~/.gradle/caches
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*') }}
    
    - name: Build with Gradle
      run: ./gradlew build
    
    - name: Run tests
      run: ./gradlew test
    
    - name: Verify plugin
      run: ./gradlew verifyPlugin
```

## 🔍 调试技巧

### 常见问题排查

1. **插件未加载**
   - 检查 `plugin.xml` 配置
   - 验证依赖项是否正确
   - 查看 IDE 日志文件

2. **PSI 访问错误**
   - 确保所有 PSI 访问都在 ReadAction 中
   - 检查线程安全性
   - 使用 Application.invokeLater() 处理 UI 更新

3. **性能问题**
   - 启用性能分析
   - 检查是否有不必要的 PSI 遍历
   - 优化正则表达式

### 日志配置

```xml
<!-- 在 IDE 中配置日志级别 -->
<configuration>
    <logger name="com.sankuai.deepcode.astplugin" level="DEBUG"/>
</configuration>
```

通过遵循这些开发指南，您可以高效地开发和维护 AST 分析插件，确保代码质量和性能。