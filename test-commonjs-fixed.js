// 测试修复后的 CommonJS require 解析
// 这个文件用于验证 TypeScriptModuleImpl 的处理

// 1. Node.js 内置模块 - 应该能正确解析 TypeScriptModuleImpl
const crypto = require('crypto');

// 2. 其他 Node.js 内置模块
const fs = require('fs');
const path = require('path');
const http = require('http');

// 3. 外部三方库
const React = require('react');
const lodash = require('lodash');

// 4. 相对路径导入
const utils = require('./utils');

console.log('Testing fixed CommonJS require resolution');

// 使用这些导入
console.log('crypto.createHash:', typeof crypto.createHash);
console.log('fs.readFile:', typeof fs.readFile);
console.log('path.join:', typeof path.join);
console.log('React.Component:', typeof React.Component);

module.exports = {
    crypto,
    fs,
    path,
    React,
    lodash,
    utils
};
