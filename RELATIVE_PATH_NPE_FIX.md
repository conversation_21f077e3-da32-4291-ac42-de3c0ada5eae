# 相对路径解析和 NPE 修复

## 问题描述

1. **相对路径解析问题**：工程中相对导入的多级文件路径（如 `../../../Stations/index.hooks`）当前解析为空
2. **NPE 异常**：在 `TypeScriptCallAnalyzer.java:123` 行出现 `NullPointerException`，错误信息为 `Cannot invoke "Object.getClass()" because "resolved" is null`

## 修复方案

### 1. 相对路径解析增强

#### 修改文件
- `deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/util/ImportUtils.java`

#### 主要修改
1. **增强 `resolveModuleFile` 方法**：
   - 将简单的 `currentDir.findFileByRelativePath(moduleSpecifier)` 替换为增强的相对路径解析
   - 调用新的 `resolveRelativeModulePath` 方法

2. **新增 `resolveRelativeModulePath` 方法**：
   - **方法1**：直接尝试解析路径
   - **方法2**：尝试添加常见的 TypeScript/JavaScript 文件扩展名（`.ts`, `.tsx`, `.js`, `.jsx`, `.d.ts`）
   - **方法3**：尝试解析为目录并查找 index 文件
   - **方法4**：手动解析复杂的相对路径（回退机制）

3. **新增 `manuallyResolveRelativePath` 方法**：
   - 手动逐级解析相对路径
   - 处理 `..` 和 `.` 路径组件
   - 支持文件扩展名自动补全

#### 关键特性
- **多级相对路径支持**：正确处理 `../../../Stations/index.hooks` 等复杂路径
- **文件扩展名自动补全**：自动尝试各种 TypeScript/JavaScript 扩展名
- **Index 文件自动解析**：当路径指向目录时，自动查找 index 文件
- **PSI 接口充分利用**：完全基于 IntelliJ 的 PSI 系统
- **回退机制**：当标准 PSI 方法失败时，提供手动解析

### 2. NPE 异常修复

#### 修改文件
- `deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/util/TypeScriptCallAnalyzer.java`

#### 问题定位
在第123行，代码直接调用 `resolved.getClass()` 而没有检查 `resolved` 是否为 null：
```java
} else if (resolved.getClass().getSimpleName().contains("Variable")) {
```

#### 修复方案
添加 null 检查：
```java
} else if (resolved != null && resolved.getClass().getSimpleName().contains("Variable")) {
```

#### 安全性验证
检查了所有使用 `resolved` 的地方，确认都有适当的 null 检查：
- 第97行：使用三元操作符进行 null 检查
- 第99-107行：在 `if (resolved != null)` 块内使用
- 第111行：使用 `instanceof` 检查（null 安全）
- 第117行：使用 `instanceof` 检查（null 安全）
- 第123行：现在有明确的 `resolved != null &&` 检查

## 测试验证

### 1. 文件结构测试
创建了完整的测试文件结构：
- 源文件：`static/page/org/pages/StationMark/components/index.tsx`
- 目标文件：`static/page/org/Stations/index.hooks.ts`
- 相对路径：`../../../Stations/index.hooks`

### 2. 路径解析验证
使用 `realpath` 命令验证了路径解析的正确性：
```bash
✓ realpath 解析成功: /Users/<USER>/Documents/projects/qa/tools/deepcode/deepcode_ast_plugin/static/page/org/Stations/index.hooks.ts
```

### 3. NPE 测试
创建了包含各种可能导致 `resolved` 为 null 的调用表达式的测试文件 `test-npe-fix.js`。

### 4. 构建验证
- 编译成功，无错误
- 所有测试通过
- 构建完全成功

## 修复效果

1. **相对路径解析**：现在能够正确解析复杂的多级相对路径，不再返回空的 `targetFilePath`
2. **NPE 消除**：消除了 `TypeScriptCallAnalyzer` 中的 NPE 异常
3. **健壮性提升**：增加了多层回退机制，提高了路径解析的成功率
4. **兼容性保持**：完全基于 PSI 接口，与 IntelliJ IDEA 行为一致

## 使用说明

修复后的代码会自动生效，无需额外配置。在 IntelliJ IDEA 中分析包含复杂相对导入的 TypeScript 文件时，应该能够：

1. 正确解析多级相对路径（如 `../../../Stations/index.hooks`）
2. 自动补全文件扩展名
3. 自动解析 index 文件
4. 避免 NPE 异常
5. 提供准确的 `targetFilePath` 信息

## 相关文件

- `ImportUtils.java` - 相对路径解析增强
- `TypeScriptCallAnalyzer.java` - NPE 修复
- `test-relative-imports.tsx` - 测试用例
- `static/page/org/Stations/index.hooks.ts` - 目标文件
- `static/page/org/pages/StationMark/components/index.tsx` - 源文件
- `test-npe-fix.js` - NPE 测试用例
