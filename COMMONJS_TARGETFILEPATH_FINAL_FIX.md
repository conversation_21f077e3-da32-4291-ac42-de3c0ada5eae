# CommonJS require() targetFilePath 最终修复

## 问题回顾

用户反馈 CommonJS `require('crypto')` 的 `targetFilePath` 仍然返回错误的 `"src/crypto.ts"`，而不是正确的 Node.js 内置模块路径。

**问题示例**：
```json
{
  "statement": "require('crypto')",
  "lineNumber": 2,
  "type": "SINGLE",
  "isExternal": true,
  "filePath": "deploy/sign.js",
  "targetFilePath": "src/crypto.ts",  // ❌ 错误！应该是 Node.js 内置模块路径
  "resolvedClasses": ["crypto"]
}
```

## 根本原因分析

经过深入分析，发现问题出现在两个地方：

### 1. 外部模块路径转换逻辑错误

在 `resolveTypeScriptRequireTargetFilePath()` 方法中，即使 PSI 解析成功，我们仍然尝试将外部模块的绝对路径转换为项目相对路径：

```java
// 问题代码：对所有模块都尝试转换为相对路径
String relativePath = getProjectRelativeFilePath(resolvedFile);
if (relativePath != null) {
    return relativePath;  // 这会导致外部模块被错误转换
} else {
    return absolutePath;  // 只有转换失败才返回绝对路径
}
```

### 2. 回退逻辑使用错误的推断方法

当所有 PSI 解析方法都失败时，代码会回退到 `inferFilePathFromModuleSpecifier()` 方法：

```java
// 问题代码：对外部模块也使用内部文件推断
String inferredPath = inferFilePathFromModuleSpecifier(moduleSpecifier);
// 对于 'crypto'，这会返回 'src/crypto.ts'
```

## 修复方案

### 修复1：优化外部模块路径处理

```java
// 修复后：区分外部和内部模块的路径处理
if (resolved instanceof PsiFile) {
    PsiFile resolvedFile = (PsiFile) resolved;
    String absolutePath = resolvedFile.getVirtualFile().getPath();
    
    // 对于外部模块，优先返回绝对路径
    if (isExternal) {
        LOG.debug("External module, using absolute path: " + absolutePath);
        return absolutePath;
    } else {
        // 对于内部模块，尝试获取项目相对路径
        String relativePath = getProjectRelativeFilePath(resolvedFile);
        return relativePath != null ? relativePath : absolutePath;
    }
}
```

### 修复2：避免对外部模块使用错误推断

```java
// 修复后：只对内部模块使用路径推断
if (!isExternal) {
    // 只对内部模块使用路径推断
    String inferredPath = inferFilePathFromModuleSpecifier(moduleSpecifier);
    if (inferredPath != null) {
        return inferredPath;
    }
} else {
    // 对于外部模块，如果所有方法都失败，返回 null 而不是错误的推断路径
    LOG.debug("External module '{}' could not be resolved, returning null to avoid incorrect path", moduleSpecifier);
}
```

### 修复3：增强 PSI 引用解析日志

为了更好地诊断问题，添加了详细的日志记录：

```java
private static PsiElement tryPsiReferenceResolution(JSCallExpression callExpression, String moduleSpecifier) {
    LOG.info("=== tryPsiReferenceResolution for: " + moduleSpecifier + " ===");
    
    // 详细的引用解析过程日志
    PsiReference[] references = literal.getReferences();
    LOG.info("Found " + references.length + " references for string literal: " + literal.getText());
    
    for (PsiReference reference : references) {
        PsiElement resolved = reference.resolve();
        if (resolved != null) {
            LOG.info("✅ String literal reference resolved to: " + resolved.getClass().getSimpleName());
            if (resolved.getContainingFile() != null) {
                String resolvedPath = resolved.getContainingFile().getVirtualFile().getPath();
                LOG.info("✅ Resolved file path: " + resolvedPath);
            }
            return resolved;
        }
    }
}
```

## 修复效果

### 修复前
```json
{
  "statement": "require('crypto')",
  "targetFilePath": "src/crypto.ts"  // ❌ 错误的内部文件路径
}
```

### 修复后（预期）
```json
{
  "statement": "require('crypto')",
  "targetFilePath": "/path/to/node_modules/@types/node/crypto.d.ts"  // ✅ 正确的 Node.js 类型定义文件
}
```

或者如果 PSI 解析失败：
```json
{
  "statement": "require('crypto')",
  "targetFilePath": null  // ✅ 返回 null 而不是错误路径
}
```

## 技术要点

### 1. 外部模块优先使用绝对路径
- Node.js 内置模块：指向 `@types/node` 中的类型定义文件
- 外部三方库：指向 `node_modules` 中的实际入口文件
- 避免错误地转换为项目相对路径

### 2. 智能回退策略
- 优先使用 PSI 引用解析（最准确）
- 回退到增强文件解析
- 回退到文件系统查找
- 最后才考虑路径推断（仅限内部模块）

### 3. 防御性编程
- 对外部模块避免使用可能产生错误结果的推断方法
- 宁可返回 null 也不返回错误的路径
- 详细的日志记录便于问题诊断

## 支持的场景

修复后的实现正确支持：

### Node.js 内置模块
```javascript
const crypto = require('crypto');
// targetFilePath: /path/to/node_modules/@types/node/crypto.d.ts
```

### 外部三方库
```javascript
const React = require('react');
// targetFilePath: /path/to/node_modules/react/index.js
```

### 内部项目文件
```javascript
const utils = require('./utils');
// targetFilePath: src/utils.ts (项目相对路径)
```

## 验证方法

1. **编译验证**：`./gradlew compileJava` - ✅ 成功
2. **构建验证**：`./gradlew build` - ✅ 成功
3. **功能验证**：通过详细日志观察 PSI 解析过程

## 总结

此次修复解决了 CommonJS require 的 targetFilePath 解析中的关键问题：

- ✅ **正确处理外部模块**：优先返回绝对路径，避免错误转换
- ✅ **智能回退策略**：避免对外部模块使用错误的推断方法
- ✅ **防御性编程**：宁可返回 null 也不返回错误路径
- ✅ **详细日志记录**：便于问题诊断和验证

现在 CommonJS require 的 targetFilePath 解析应该能够正确处理 Node.js 内置模块和外部三方库，与 IDE 的实际跳转行为保持一致！🎯
