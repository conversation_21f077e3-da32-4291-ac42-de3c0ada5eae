// 测试 CommonJS require 的 PSI 引用解析能力
// 这个文件专门测试 IDE 中点击跳转的场景

// 1. Node.js 内置模块 - 应该跳转到类型定义文件
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const http = require('http');
const util = require('util');

// 2. 外部三方库 - 应该跳转到 node_modules 中的实际文件
const React = require('react');
const lodash = require('lodash');
const moment = require('moment');
const axios = require('axios');
const express = require('express');

// 3. Scoped 包 - 应该正确解析 @scope/package 格式
const antdIcons = require('@ant-design/icons');
const babelCore = require('@babel/core');
const typesNode = require('@types/node');

// 4. 相对路径导入 - 应该跳转到项目内文件
const utils = require('./utils');
const config = require('../config');
const helpers = require('../../helpers');

// 5. 别名路径导入 - 应该根据 tsconfig.json 或 webpack 配置解析
const pageUtils = require('@page/actForm/utils');
const components = require('@components/Button');
const services = require('@services/api');

// 6. 复杂的解构赋值 require
const { 
    createHash, 
    randomBytes 
} = require('crypto');

const { 
    readFile, 
    writeFile 
} = require('fs');

const { 
    Component, 
    useState, 
    useEffect 
} = require('react');

// 7. 重命名导入
const $ = require('jquery');
const _ = require('lodash');
const R = require('ramda');

// 8. 条件导入
if (process.env.NODE_ENV === 'development') {
    const devTools = require('redux-devtools');
}

// 9. 动态导入（虽然这不是标准的 CommonJS，但在某些环境中可能存在）
const moduleName = 'lodash';
const dynamicModule = require(moduleName);

// 10. 嵌套的 require 调用
const configPath = require('./config').paths.modules;
const nestedModule = require(configPath + '/nested');

// 使用导入的模块来确保它们被正确解析
console.log('Testing CommonJS PSI resolution:');
console.log('crypto.createHash:', typeof crypto.createHash);
console.log('React.Component:', typeof React.Component);
console.log('lodash.merge:', typeof lodash.merge);
console.log('utils:', typeof utils);

// 导出一些内容
module.exports = {
    crypto,
    React,
    lodash,
    utils,
    testFunction: () => {
        return 'CommonJS PSI resolution test';
    }
};
