package com.sankuai.deepcode.astplugin.export;

import com.intellij.openapi.project.Project;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 导出服务注册中心
 * 用于管理不同语言的导出服务实现
 *
 * <AUTHOR>
 */
public class ExportRegistry {
    
    private static final ExportRegistry INSTANCE = new ExportRegistry();
    
    private final Map<String, ExportService> exportServices = new ConcurrentHashMap<>();
    
    private ExportRegistry() {
    }
    
    public static ExportRegistry getInstance() {
        return INSTANCE;
    }
    
    /**
     * 注册导出服务
     * @param exportService 导出服务实现
     */
    public void registerExportService(@NotNull ExportService exportService) {
        exportServices.put(exportService.getLanguageName(), exportService);
    }
    
    /**
     * 注销导出服务
     * @param languageName 语言名称
     */
    public void unregisterExportService(@NotNull String languageName) {
        exportServices.remove(languageName);
    }
    
    /**
     * 获取指定语言的导出服务
     * @param languageName 语言名称
     * @return 导出服务，如果不存在返回null
     */
    @Nullable
    public ExportService getExportService(@NotNull String languageName) {
        return exportServices.get(languageName);
    }
    
    /**
     * 获取所有注册的导出服务
     * @return 所有导出服务
     */
    @NotNull
    public Collection<ExportService> getAllExportServices() {
        return Collections.unmodifiableCollection(exportServices.values());
    }
    
    /**
     * 获取项目中可用的导出服务（项目中包含相应语言文件的服务）
     * @param project 项目对象
     * @return 可用的导出服务
     */
    @NotNull
    public List<ExportService> getAvailableExportServices(@NotNull Project project) {
        List<ExportService> availableServices = new ArrayList<>();
        
        for (ExportService service : exportServices.values()) {
            if (service.hasLanguageFilesInProject(project)) {
                availableServices.add(service);
            }
        }
        
        // 按语言名称排序
        availableServices.sort(Comparator.comparing(ExportService::getLanguageName));
        
        return availableServices;
    }
    
    /**
     * 获取所有注册的语言名称
     * @return 语言名称列表
     */
    @NotNull
    public Set<String> getRegisteredLanguageNames() {
        return Collections.unmodifiableSet(exportServices.keySet());
    }
    
    /**
     * 检查是否有注册的导出服务
     * @return 如果有注册的服务返回true
     */
    public boolean hasRegisteredServices() {
        return !exportServices.isEmpty();
    }
}