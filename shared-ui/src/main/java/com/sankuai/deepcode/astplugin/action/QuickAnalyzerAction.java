package com.sankuai.deepcode.astplugin.action;

import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.ui.Messages;
import com.intellij.psi.PsiFile;
import com.sankuai.deepcode.astplugin.analyzer.AnalyzerService;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 快速AST分析器Action
 * 用于快速验证分析器功能，无需重启IDE
 *
 * <AUTHOR>
 */
public class QuickAnalyzerAction extends AnAction {

    public QuickAnalyzerAction() {
        super("Quick AST Analyzer");
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        PsiFile currentFile = e.getData(CommonDataKeys.PSI_FILE);

        if (currentFile == null) {
            Messages.showWarningDialog(
                    "No file selected. Please open a Java file and try again.",
                    "AST Analyzer"
            );
            return;
        }

        // 支持所有文件类型，让分析器自己判断
        String fileInfo = String.format("File: %s, Type: %s, Language: %s",
                currentFile.getName(),
                currentFile.getClass().getSimpleName(),
                currentFile.getLanguage().getID());

        try {
            // 在ReadAction中执行PSI分析，确保线程安全
            ReadAction.run(() -> {
                try {
                    AnalyzerService analyzerService = AnalyzerService.getInstance();
                    AnalysisResult result = analyzerService.analyze(currentFile);

                    // 在EDT中显示结果
                    com.intellij.openapi.application.ApplicationManager.getApplication().invokeLater(() -> {
                        showAnalysisResult(result, currentFile.getName());
                    });

                } catch (Exception ex) {
                    // 在EDT中显示错误
                    com.intellij.openapi.application.ApplicationManager.getApplication().invokeLater(() -> {
                        Messages.showErrorDialog(
                                "Error during analysis: " + ex.getMessage() + "\n\n" +
                                        "Stack trace: " + getStackTrace(ex),
                                "AST Analyzer Error"
                        );
                    });
                    ex.printStackTrace();
                }
            });

        } catch (Exception ex) {
            Messages.showErrorDialog(
                    "Error during analysis: " + ex.getMessage() + "\n\n" +
                            "Stack trace: " + getStackTrace(ex),
                    "AST Analyzer Error"
            );
            ex.printStackTrace();
        }
    }

    @Override
    public void update(@NotNull AnActionEvent e) {
        // 支持所有文件类型
        PsiFile file = e.getData(CommonDataKeys.PSI_FILE);
        e.getPresentation().setEnabledAndVisible(file != null);
    }

    @Override
    public @NotNull ActionUpdateThread getActionUpdateThread() {
        return ActionUpdateThread.BGT;
    }

    private String getStackTrace(Exception e) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }

    private void showAnalysisResult(AnalysisResult result, String fileName) {
        // 构建结果消息
        StringBuilder message = new StringBuilder();
        message.append("=== AST Analysis Results ===\n\n");
        message.append("File: ").append(result.getFileName()).append("\n");
        message.append("Language: ").append(result.getLanguage()).append("\n\n");

        // 统计信息
        message.append("=== Statistics ===\n");
        message.append("Total nodes: ").append(result.getNodes().size()).append("\n");
        message.append("Call relations: ").append(result.getCallRelations().size()).append("\n");

        if (!result.getStatistics().isEmpty()) {
            result.getStatistics().forEach((key, value) ->
                    message.append(key).append(": ").append(value).append("\n"));
        }

        // 调用关系详情
        if (!result.getCallRelations().isEmpty()) {
            message.append("\n=== Call Relations (Grouped by Caller) ===\n");

            // 按调用方分组聚合调用关系
            Map<String, List<CallRelation>> relationsByCaller = new LinkedHashMap<>();
            for (CallRelation relation : result.getCallRelations()) {
                String callerSignature = relation.getCaller().getSignature();
                relationsByCaller.computeIfAbsent(callerSignature, k -> new ArrayList<>()).add(relation);
            }

            int callerCount = 0;
            for (Map.Entry<String, List<CallRelation>> entry : relationsByCaller.entrySet()) {
                if (callerCount >= 5) { // 限制显示的调用方数量
                    message.append("... and ").append(relationsByCaller.size() - 5).append(" more callers\n");
                    break;
                }

                String callerSignature = entry.getKey();
                List<CallRelation> callerRelations = entry.getValue();

                // 显示调用方信息
                message.append("\n📞 CALLER ").append(++callerCount).append(": ").append(callerSignature).append("\n");
                message.append("   ID: ").append(callerRelations.get(0).getCaller().getId()).append("\n");
                message.append("   Calls ").append(callerRelations.size()).append(" different methods:\n");

                // 显示该调用方的调用关系（限制数量）
                for (int i = 0; i < Math.min(callerRelations.size(), 3); i++) {
                    CallRelation relation = callerRelations.get(i);
                    message.append("   ").append(i + 1).append(". → ");

                    // 显示被调用方信息
                    message.append(relation.getCallee().getSignature());
                    if (relation.isExternal()) {
                        message.append(" [EXTERNAL]");
                    }

                    // 显示调用次数
                    if (relation.getCallCount() > 1) {
                        message.append(" (").append(relation.getCallCount()).append(" calls)");
                    }
                    message.append("\n");

                    // 显示被调用方完整ID
                    message.append("      Callee ID: ").append(relation.getCallee().getId()).append("\n");

                    // 显示第一个调用实例
                    if (!relation.getAllCallInstances().isEmpty()) {
                        CallRelation.CallInstance firstInstance = relation.getAllCallInstances().get(0);
                        message.append("      First call: Line ").append(firstInstance.getLineNumber())
                                .append(" - ").append(firstInstance.getExpression()).append("\n");

                        if (relation.getAllCallInstances().size() > 1) {
                            message.append("      (+ ").append(relation.getAllCallInstances().size() - 1)
                                    .append(" more instances)\n");
                        }
                    }
                }

                if (callerRelations.size() > 3) {
                    message.append("   ... and ").append(callerRelations.size() - 3).append(" more calls\n");
                }
            }
        } else {
            message.append("\n❌ No call relations found!\n");
            message.append("This might indicate an issue with the analyzer.\n");
        }

        // 错误信息
        if (!result.getErrors().isEmpty()) {
            message.append("\n=== Errors ===\n");
            for (String error : result.getErrors()) {
                message.append("• ").append(error).append("\n");
            }
        }

        // 显示结果
        Messages.showInfoMessage(
                message.toString(),
                "AST Analysis Results - " + fileName
        );
    }
}