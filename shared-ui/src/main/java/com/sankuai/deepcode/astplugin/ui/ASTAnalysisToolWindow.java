package com.sankuai.deepcode.astplugin.ui;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentFactory;
import org.jetbrains.annotations.NotNull;

/**
 * AST分析工具窗口工厂
 *
 * <AUTHOR>
 */
public class ASTAnalysisToolWindow implements ToolWindowFactory {

    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        // 创建AST分析面板
        ASTAnalysisPanel analysisPanel = new ASTAnalysisPanel(project);
        
        // 创建内容并添加到工具窗口
        ContentFactory contentFactory = ContentFactory.getInstance();
        Content content = contentFactory.createContent(analysisPanel, "", false);
        toolWindow.getContentManager().addContent(content);
    }
}