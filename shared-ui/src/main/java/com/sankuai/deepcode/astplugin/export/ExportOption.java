package com.sankuai.deepcode.astplugin.export;

/**
 * 导出选项枚举
 *
 * <AUTHOR>
 */
public enum ExportOption {

    /**
     * 导出统计信息CSV
     */
    STATISTICS_CSV("导出统计CSV", "导出项目代码统计信息为CSV文件"),

    /**
     * 导出函数调用详情
     */
    CALL_DETAILS("导出函数调用详情", "导出详细的函数调用关系信息"),

    /**
     * 导出完整分析报告
     */
    EXPORT_IMPORTS("导出Import报告", "导出包含所有导入关系分析信息的报告"),

    /**
     * 自定义导出（预留扩展）
     */
    CUSTOM("自定义导出", "自定义导出格式和内容");

    private final String displayName;
    private final String description;

    ExportOption(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }
}