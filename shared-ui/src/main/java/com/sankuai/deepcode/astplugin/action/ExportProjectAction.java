package com.sankuai.deepcode.astplugin.action;

import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.fileChooser.FileChooser;
import com.intellij.openapi.fileChooser.FileChooserDescriptor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.vfs.VirtualFile;
import com.sankuai.deepcode.astplugin.export.ExportOption;
import com.sankuai.deepcode.astplugin.export.ExportRegistry;
import com.sankuai.deepcode.astplugin.export.ExportService;
import com.sankuai.deepcode.astplugin.ui.ExportSelectionDialog;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * 导出整个项目的 AST 分析结果
 * 支持多语言和多种导出选项
 *
 * <AUTHOR>
 */
public class ExportProjectAction extends AnAction {

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            Messages.showErrorDialog("No project context available", "Error");
            return;
        }

        // 获取可用的导出服务
        ExportRegistry registry = ExportRegistry.getInstance();
        List<ExportService> availableServices = registry.getAvailableExportServices(project);

        if (availableServices.isEmpty()) {
            Messages.showInfoMessage("No supported language files found in the project", "Information");
            return;
        }

        // 显示导出选择对话框
        ExportSelectionDialog dialog = new ExportSelectionDialog(project, availableServices);
        if (dialog.showAndGet()) {
            ExportService selectedService = dialog.getSelectedService();
            ExportOption selectedOption = dialog.getSelectedOption();

            if (selectedService != null && selectedOption != null) {
                // 选择输出目录
                FileChooserDescriptor descriptor = new FileChooserDescriptor(false, true, false, false, false, false);
                descriptor.setTitle("Choose Directory to Save " + selectedService.getLanguageName() + " AST Report");
                descriptor.setDescription("Select a directory where the " + selectedOption.getDisplayName() + " will be saved");

                VirtualFile selectedDir = FileChooser.chooseFile(descriptor, project, null);
                if (selectedDir != null) {
                    // 执行导出
                    selectedService.exportProject(project, selectedOption, selectedDir);
                }
            }
        }
    }



    @Override
    public void update(@NotNull AnActionEvent e) {
        // Enable action only when a project is open
        Project project = e.getProject();
        e.getPresentation().setEnabled(project != null);
    }

    @Override
    public @NotNull ActionUpdateThread getActionUpdateThread() {
        return ActionUpdateThread.BGT;
    }
}