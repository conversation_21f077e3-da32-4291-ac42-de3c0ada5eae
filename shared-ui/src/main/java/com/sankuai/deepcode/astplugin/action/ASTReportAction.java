package com.sankuai.deepcode.astplugin.action;

import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.psi.PsiFile;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.components.JBTextArea;
import com.intellij.util.ui.JBFont;
import com.sankuai.deepcode.astplugin.analyzer.AnalyzerService;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.awt.*;

/**
 * AST 解析结果触发解析
 *
 * <AUTHOR>
 */
public class ASTReportAction extends AnAction {

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        Editor editor = e.getData(CommonDataKeys.EDITOR);
        PsiFile psiFile = e.getData(CommonDataKeys.PSI_FILE);

        if (project == null || editor == null || psiFile == null) {
            JOptionPane.showMessageDialog(null, "No file selected or project context missing", "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Generate AST report using AnalyzerService
        AnalyzerService analyzerService = AnalyzerService.getInstance();
        String report = analyzerService.generateASTReport(psiFile);

        // Display report in a dialog
        showReportDialog(project, report, psiFile.getName());
    }

    private void showReportDialog(Project project, String report, String fileName) {
        JDialog dialog = new JDialog();
        dialog.setTitle("AST Report - " + fileName);
        dialog.setSize(800, 600);
        dialog.setLocationRelativeTo(null);
        dialog.setModal(true);

        JBTextArea textArea = new JBTextArea(report);
        textArea.setEditable(false);
        textArea.setBackground(JBColor.WHITE);
        textArea.setFont(JBFont.create(new Font(Font.MONOSPACED, Font.PLAIN, 12)));

        dialog.add(new JBScrollPane(textArea), BorderLayout.CENTER);

        JButton closeButton = new JButton("Close");
        closeButton.addActionListener(e -> dialog.dispose());

        JPanel buttonPanel = new JPanel();
        buttonPanel.add(closeButton);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        dialog.setVisible(true);
    }

    @Override
    public void update(@NotNull AnActionEvent e) {
        // Enable action only when a file is open
        PsiFile psiFile = e.getData(CommonDataKeys.PSI_FILE);
        e.getPresentation().setEnabled(psiFile != null);
    }

    @Override
    public @NotNull ActionUpdateThread getActionUpdateThread() {
        return ActionUpdateThread.BGT;
    }
}