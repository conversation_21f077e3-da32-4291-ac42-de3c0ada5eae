package com.sankuai.deepcode.astplugin.analyzer;

import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.Language;

/**
 * AST分析器接口
 * 
 * <AUTHOR>
 */
public interface ASTAnalyzer {
    
    /**
     * 分析PSI文件并返回分析结果
     *
     * @param psiFile PSI文件对象
     * @return 分析结果
     */
    AnalysisResult analyze(PsiFile psiFile);
    
    /**
     * 生成AST分析报告的文本格式
     *
     * @param psiFile PSI文件对象
     * @return 报告文本
     */
    default String generateASTReport(PsiFile psiFile) {
        try {
            AnalysisResult result = analyze(psiFile);
            return formatAnalysisResult(result);
        } catch (Exception e) {
            return "Error during analysis: " + e.getMessage();
        }
    }

    /**
     * 格式化分析结果为文本报告
     *
     * @param result 分析结果
     * @return 格式化的报告
     */
    default String formatAnalysisResult(AnalysisResult result) {
        if (result == null) {
            return "Analysis result is null";
        }

        StringBuilder report = new StringBuilder();

        // 基本信息
        report.append("=== AST Analysis Report ===\n");
        report.append("File: ").append(result.getFileName()).append("\n");
        report.append("Language: ").append(result.getLanguage()).append("\n");
        report.append("Timestamp: ").append(result.getTimestamp()).append("\n");
        report.append("Nodes: ").append(result.getNodes().size()).append("\n");
        report.append("Call Relations: ").append(result.getCallRelations().size()).append("\n");
        report.append("\n");

        // 错误信息
        if (!result.getErrors().isEmpty()) {
            report.append("=== Errors ===\n");
            result.getErrors().forEach(error -> report.append("ERROR: ").append(error).append("\n"));
            report.append("\n");
        }

        // 统计信息
        if (!result.getStatistics().isEmpty()) {
            report.append("=== Statistics ===\n");
            result.getStatistics().entrySet().stream()
                .sorted(java.util.Map.Entry.comparingByKey())
                .forEach(entry -> report.append(entry.getKey())
                    .append(": ")
                    .append(entry.getValue())
                    .append("\n"));
        }

        return report.toString();
    }

    /**
     * 线程安全的行号获取方法
     */
    default int getSafeLineNumber(PsiElement element) {
        try {
            PsiFile containingFile = element.getContainingFile();
            if (containingFile == null) {
                return 1;
            }

            // 获取元素在文件中的偏移量
            int offset = element.getTextOffset();
            if (offset < 0) {
                return 1;
            }

            // 使用文档管理器获取行号（线程安全）
            com.intellij.openapi.editor.Document document =
                    com.intellij.psi.PsiDocumentManager.getInstance(element.getProject())
                            .getDocument(containingFile);

            if (document != null) {
                // 确保偏移量在有效范围内
                int safeOffset = Math.min(offset, document.getTextLength() - 1);
                safeOffset = Math.max(0, safeOffset);
                return document.getLineNumber(safeOffset) + 1; // 转换为1基索引
            }

            // 备用方法：手动计算行号
            String text = containingFile.getText();
            if (text != null && offset < text.length()) {
                int lineNumber = 1;
                for (int i = 0; i < offset; i++) {
                    if (text.charAt(i) == '\n') {
                        lineNumber++;
                    }
                }
                return lineNumber;
            }

        } catch (Exception ignore) {
        }
        return 1; // 返回默认行号
    }



    /**
     * 检查是否支持分析给定的PSI文件
     *
     * @param psiFile PSI文件对象
     * @return 如果支持则返回true
     */
    boolean supports(PsiFile psiFile);
    
    /**
     * 获取支持的语言标识
     *
     * @return 语言标识字符串
     */
    Language getSupportedLanguage();
}