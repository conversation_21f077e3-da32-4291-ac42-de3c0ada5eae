package com.sankuai.deepcode.astplugin.ui;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.ui.Gray;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.util.ui.JBUI;
import com.intellij.util.ui.UIUtil;
import com.sankuai.deepcode.astplugin.export.ExportOption;
import com.sankuai.deepcode.astplugin.export.ExportService;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import javax.swing.border.Border;
import javax.swing.border.CompoundBorder;
import java.awt.*;
import java.util.List;

/**
 * 导出选择对话框
 * 允许用户选择语言和导出选项
 *
 * <AUTHOR>
 */
public class ExportSelectionDialog extends DialogWrapper {

    private final Project project;
    private final List<ExportService> availableServices;

    private ExportService selectedService;
    private ExportOption selectedOption;

    public ExportSelectionDialog(@NotNull Project project, @NotNull List<ExportService> availableServices) {
        super(project, true);
        this.project = project;
        this.availableServices = availableServices;

        setTitle("导出 AST 分析报告");
        setResizable(true);

        init();
    }

    @Override
    protected @Nullable JComponent createCenterPanel() {
        // 主面板使用IDE主题背景色
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(UIUtil.getPanelBackground());
        mainPanel.setBorder(JBUI.Borders.empty(20));
        mainPanel.setPreferredSize(new Dimension(550, 450));

        // 创建标题区域
        JPanel titlePanel = createTitlePanel();
        mainPanel.add(titlePanel, BorderLayout.NORTH);

        // 卡片容器面板
        JPanel cardsContainer = new JPanel(new BorderLayout());
        cardsContainer.setBackground(UIUtil.getPanelBackground());
        cardsContainer.setBorder(JBUI.Borders.emptyTop(20));

        // 卡片面板
        JPanel cardsPanel = new JPanel();
        cardsPanel.setLayout(new BoxLayout(cardsPanel, BoxLayout.Y_AXIS));
        cardsPanel.setBackground(UIUtil.getPanelBackground());

        // 为每个服务创建卡片
        ButtonGroup optionGroup = new ButtonGroup();

        for (int i = 0; i < availableServices.size(); i++) {
            ExportService service = availableServices.get(i);

            if (i > 0) {
                cardsPanel.add(Box.createVerticalStrut(12));
            }

            JPanel serviceCard = createModernServiceCard(service, optionGroup);
            cardsPanel.add(serviceCard);
        }

        // 滚动面板
        JBScrollPane scrollPane = new JBScrollPane(cardsPanel);
        scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setBorder(null);
        scrollPane.setViewportBorder(null);
        scrollPane.setBackground(UIUtil.getPanelBackground());
        scrollPane.getViewport().setBackground(UIUtil.getPanelBackground());

        cardsContainer.add(scrollPane, BorderLayout.CENTER);
        mainPanel.add(cardsContainer, BorderLayout.CENTER);

        return mainPanel;
    }

    private JPanel createTitlePanel() {
        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setBackground(UIUtil.getPanelBackground());

        // 主标题
        JBLabel titleLabel = new JBLabel("导出 AST 分析报告");
        titleLabel.setFont(titleLabel.getFont().deriveFont(Font.BOLD, 18f));
        titleLabel.setForeground(UIUtil.getLabelForeground());

        // 副标题
        JBLabel subtitleLabel = new JBLabel("选择要导出的语言和报告类型");
        subtitleLabel.setFont(subtitleLabel.getFont().deriveFont(Font.PLAIN, 13f));
        subtitleLabel.setForeground(UIUtil.getContextHelpForeground());

        JPanel textPanel = new JPanel();
        textPanel.setLayout(new BoxLayout(textPanel, BoxLayout.Y_AXIS));
        textPanel.setBackground(UIUtil.getPanelBackground());
        textPanel.add(titleLabel);
        textPanel.add(Box.createVerticalStrut(5));
        textPanel.add(subtitleLabel);

        // 图标
        JBLabel iconLabel = new JBLabel(AllIcons.ToolbarDecorator.Export);

        titlePanel.add(iconLabel, BorderLayout.WEST);
        titlePanel.add(Box.createHorizontalStrut(12), BorderLayout.CENTER);
        titlePanel.add(textPanel, BorderLayout.CENTER);

        return titlePanel;
    }

    private JPanel createModernServiceCard(@NotNull ExportService service, ButtonGroup optionGroup) {
        // 主卡片面板
        JPanel cardPanel = new JPanel(new BorderLayout());

        // 使用主题相关的边框和背景
        Color borderColor = JBColor.namedColor("Component.borderColor", Gray._220);
        Color cardBackground = JBColor.namedColor("Panel.background", UIUtil.getPanelBackground());

        Border outerBorder = JBUI.Borders.customLine(borderColor, 1);
        Border innerBorder = JBUI.Borders.empty(16, 18, 16, 18);
        cardPanel.setBorder(new CompoundBorder(outerBorder, innerBorder));
        cardPanel.setBackground(cardBackground);

        // 顶部区域：语言信息
        JPanel headerPanel = createCardHeader(service);
        cardPanel.add(headerPanel, BorderLayout.NORTH);

        // 中间区域：分隔线
        JSeparator separator = new JSeparator();
        separator.setForeground(JBColor.namedColor("Separator.foreground", Gray._210));
        separator.setBorder(JBUI.Borders.empty(8, 0));
        cardPanel.add(separator, BorderLayout.CENTER);

        // 底部区域：导出选项
        JPanel optionsPanel = createOptionsPanel(service, optionGroup);
        cardPanel.add(optionsPanel, BorderLayout.SOUTH);

        return cardPanel;
    }

    private JPanel createCardHeader(@NotNull ExportService service) {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(UIUtil.getPanelBackground());

        // 语言标题和图标
        JPanel titlePanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        titlePanel.setBackground(UIUtil.getPanelBackground());

        // 根据语言类型选择图标
        Icon languageIcon = getLanguageIcon(service.getLanguageName());
        if (languageIcon != null) {
            JBLabel iconLabel = new JBLabel(languageIcon);
            titlePanel.add(iconLabel);
            titlePanel.add(Box.createHorizontalStrut(8));
        }

        JBLabel languageLabel = new JBLabel(service.getLanguageName());
        languageLabel.setFont(languageLabel.getFont().deriveFont(Font.BOLD, 15f));
        languageLabel.setForeground(UIUtil.getLabelForeground());
        titlePanel.add(languageLabel);

        headerPanel.add(titlePanel, BorderLayout.WEST);

        // 文件数量标签
        int fileCount = service.getLanguageFileCount(project);
        JBLabel fileCountLabel = new JBLabel(fileCount + " files");
        fileCountLabel.setFont(fileCountLabel.getFont().deriveFont(Font.PLAIN, 12f));
        fileCountLabel.setForeground(UIUtil.getContextHelpForeground());

        // 添加文件图标
        JPanel countPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 0, 0));
        countPanel.setBackground(UIUtil.getPanelBackground());
        countPanel.add(new JBLabel(AllIcons.FileTypes.Text));
        countPanel.add(Box.createHorizontalStrut(4));
        countPanel.add(fileCountLabel);

        headerPanel.add(countPanel, BorderLayout.EAST);

        return headerPanel;
    }

    private JPanel createOptionsPanel(@NotNull ExportService service, ButtonGroup optionGroup) {
        JPanel optionsPanel = new JPanel();
        optionsPanel.setLayout(new BoxLayout(optionsPanel, BoxLayout.Y_AXIS));
        optionsPanel.setBackground(UIUtil.getPanelBackground());
        optionsPanel.setBorder(JBUI.Borders.emptyTop(8));

        List<ExportOption> supportedOptions = service.getSupportedExportOptions();

        for (int i = 0; i < supportedOptions.size(); i++) {
            ExportOption option = supportedOptions.get(i);

            if (i > 0) {
                optionsPanel.add(Box.createVerticalStrut(6));
            }

            JPanel optionPanel = createOptionPanel(service, option, optionGroup);
            optionsPanel.add(optionPanel);
        }

        return optionsPanel;
    }

    private JPanel createOptionPanel(@NotNull ExportService service, @NotNull ExportOption option, ButtonGroup optionGroup) {
        JPanel optionPanel = new JPanel(new BorderLayout());
        optionPanel.setBackground(UIUtil.getPanelBackground());
        optionPanel.setBorder(JBUI.Borders.empty(6, 0));

        // 单选按钮
        JRadioButton optionButton = new JRadioButton();
        optionButton.setBackground(UIUtil.getPanelBackground());
        optionButton.setText(option.getDisplayName());
        optionButton.setToolTipText(option.getDescription());
        optionButton.setFont(optionButton.getFont().deriveFont(Font.PLAIN, 13f));

        // 设置选择监听器
        optionButton.addActionListener(e -> {
            selectedService = service;
            selectedOption = option;
        });

        optionGroup.add(optionButton);
        optionPanel.add(optionButton, BorderLayout.NORTH);

        // 选项描述
        JBLabel descLabel = new JBLabel("<html>" + option.getDescription() + "</html>");
        descLabel.setFont(descLabel.getFont().deriveFont(Font.PLAIN, 11f));
        descLabel.setForeground(UIUtil.getContextHelpForeground());
        descLabel.setBorder(JBUI.Borders.emptyLeft(20));
        optionPanel.add(descLabel, BorderLayout.CENTER);

        return optionPanel;
    }

    private Icon getLanguageIcon(String languageName) {
        return switch (languageName.toLowerCase()) {
            case "python" -> AllIcons.Language.Python;
            case "java" -> AllIcons.FileTypes.Java;
            case "javascript", "js" -> AllIcons.FileTypes.JavaScript;
            default -> AllIcons.FileTypes.Text;
        };
    }

    @Override
    protected Action @NotNull [] createActions() {
        return new Action[]{getOKAction(), getCancelAction()};
    }

    @Override
    protected void doOKAction() {
        if (selectedService == null || selectedOption == null) {
            // 使用IDE主题的错误提示
            JOptionPane.showMessageDialog(
                    getContentPane(),
                    "请选择要导出的语言和报告类型",
                    "选择错误",
                    JOptionPane.WARNING_MESSAGE
            );
            return;
        }
        super.doOKAction();
    }

    @Override
    protected String getDimensionServiceKey() {
        return "AST.ExportSelectionDialog";
    }

    @Nullable
    public ExportService getSelectedService() {
        return selectedService;
    }

    @Nullable
    public ExportOption getSelectedOption() {
        return selectedOption;
    }
}