package com.sankuai.deepcode.astplugin.ui;

import com.intellij.openapi.actionSystem.ActionPlaces;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPanel;
import com.sankuai.deepcode.astplugin.action.ExportProjectAction;
import com.sankuai.deepcode.astplugin.action.ExportReportAction;
import com.sankuai.deepcode.astplugin.analyzer.AnalyzerService;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;

import javax.swing.*;
import java.awt.*;

/**
 * AST分析主面板
 * <AUTHOR>
 */
public class ASTAnalysisPanel extends JBPanel<ASTAnalysisPanel> {
    private final Project project;
    private final AnalyzerService analyzerService;

    private JBLabel statusLabel;
    private JButton analyzeButton;
    private JButton exportButton;
    private JButton exportAllButton; // 新增导出全部按钮
    private ASTResultPanel resultPanel;
    private AnalysisResult currentAnalysisResult;
    private PsiFile currentPsiFile;

    public ASTAnalysisPanel(Project project) {
        super(new BorderLayout());
        this.project = project;
        this.analyzerService = AnalyzerService.getInstance();

        initializeUI();
        setupEventHandlers();
        setupFileChangeListener();
    }

    private void initializeUI() {
        // 顶部工具栏
        JPanel toolbarPanel = createToolbarPanel();
        add(toolbarPanel, BorderLayout.NORTH);

        // 状态标签
        statusLabel = new JBLabel("请选择一个文件进行分析");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        add(statusLabel, BorderLayout.SOUTH);

        // 结果显示面板
        resultPanel = new ASTResultPanel();
        add(resultPanel, BorderLayout.CENTER);
    }

    private JPanel createToolbarPanel() {
        JPanel toolbar = new JBPanel<>(new BorderLayout());
        toolbar.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));

        // 创建自动换行的按钮容器
        JPanel buttonPanel = new JBPanel<ASTAnalysisPanel>() {
            private final int GAP = 5;

            {
                // 禁用默认布局管理器，使用自定义布局
                setLayout(null);

                // 添加组件监听器，当容器大小变化时重新布局
                addComponentListener(new java.awt.event.ComponentAdapter() {
                    @Override
                    public void componentResized(java.awt.event.ComponentEvent e) {
                        revalidate();
                        repaint();
                    }
                });
            }

            @Override
            public Dimension getPreferredSize() {
                return calculatePreferredSize();
            }

            @Override
            public void doLayout() {
                layoutComponents();
            }

            private Dimension calculatePreferredSize() {
                if (getComponentCount() == 0) {
                    return new Dimension(0, 0);
                }

                int containerWidth = getWidth();

                // 如果容器还没有宽度，计算单行所需宽度
                if (containerWidth <= 10) {
                    int totalWidth = GAP;
                    int maxHeight = 0;

                    for (Component comp : getComponents()) {
                        if (comp.isVisible()) {
                            Dimension d = comp.getPreferredSize();
                            totalWidth += d.width + GAP;
                            maxHeight = Math.max(maxHeight, d.height);
                        }
                    }

                    return new Dimension(totalWidth, maxHeight + GAP * 2);
                }

                // 有宽度时，计算换行后的实际高度
                return new Dimension(containerWidth, calculateHeight(containerWidth));
            }

            private int calculateHeight(int availableWidth) {
                int currentX = GAP;
                int currentY = GAP;
                int lineHeight = 0;
                for (Component comp : getComponents()) {
                    if (comp.isVisible()) {
                        Dimension d = comp.getPreferredSize();

                        // 检查是否需要换行（除了第一个组件）
                        if (currentX > GAP && currentX + d.width > availableWidth - GAP) {
                            // 换行：移到下一行
                            currentY += lineHeight + GAP;
                            currentX = GAP;
                            lineHeight = 0;
                        }

                        currentX += d.width + GAP;
                        lineHeight = Math.max(lineHeight, d.height);
                    }
                }

                return currentY + lineHeight + GAP;
            }

            private void layoutComponents() {
                int availableWidth = getWidth();
                if (availableWidth <= 0) {
                    return; // 容器还没有大小
                }

                int currentX = GAP;
                int currentY = GAP;
                int lineHeight = 0;

                for (Component comp : getComponents()) {
                    if (comp.isVisible()) {
                        Dimension d = comp.getPreferredSize();

                        // 检查是否需要换行（除了第一个组件）
                        if (currentX > GAP && currentX + d.width > availableWidth - GAP) {
                            // 换行：移到下一行
                            currentY += lineHeight + GAP;
                            currentX = GAP;
                            lineHeight = 0;
                        }

                        // 设置组件位置和大小
                        comp.setBounds(currentX, currentY, d.width, d.height);

                        // 更新位置和行高
                        currentX += d.width + GAP;
                        lineHeight = Math.max(lineHeight, d.height);
                    }
                }

                // 重新计算并设置首选高度
                int totalHeight = currentY + lineHeight + GAP;
                Dimension currentSize = getPreferredSize();
                if (currentSize.height != totalHeight) {
                    setPreferredSize(new Dimension(availableWidth, totalHeight));
                    if (getParent() != null) {
                        SwingUtilities.invokeLater(() -> getParent().revalidate());
                    }
                }
            }
        };

        // 创建所有按钮
        analyzeButton = new JButton("分析当前文件");
        analyzeButton.setToolTipText("分析当前打开的文件");

        exportButton = new JButton("导出");
        exportButton.setToolTipText("导出分析结果为JSON文件 (Cmd+Option+E)");
        exportButton.setEnabled(false); // 初始状态禁用

        exportAllButton = new JButton("导出全部");
        exportAllButton.setToolTipText("导出整个项目的Python文件AST分析结果为CSV");

        // 添加按钮到容器
        buttonPanel.add(analyzeButton);
        buttonPanel.add(exportButton);
        buttonPanel.add(exportAllButton);

        toolbar.add(buttonPanel, BorderLayout.WEST);

        return toolbar;
    }



    private void setupEventHandlers() {
        analyzeButton.addActionListener(e -> analyzeCurrentFile());
        exportButton.addActionListener(e -> exportCurrentResult());
        exportAllButton.addActionListener(e -> exportAllProjectFiles());
    }



    private void analyzeCurrentFile() {
        VirtualFile currentFile = getCurrentFile();
        if (currentFile == null) {
            statusLabel.setText("没有打开的文件");
            resultPanel.clearResults();
            exportButton.setEnabled(false);
            currentAnalysisResult = null;
            currentPsiFile = null;
            updateAnalyzeButtonText();
            return;
        }

        PsiFile psiFile = PsiManager.getInstance(project).findFile(currentFile);
        if (psiFile == null) {
            statusLabel.setText("无法获取文件的PSI信息");
            resultPanel.clearResults();
            exportButton.setEnabled(false);
            currentAnalysisResult = null;
            currentPsiFile = null;
            updateAnalyzeButtonText();
            return;
        }

        // 在EDT中更新UI状态
        SwingUtilities.invokeLater(() -> {
            statusLabel.setText("正在分析: " + currentFile.getName() + "...");
            analyzeButton.setEnabled(false);
            exportButton.setEnabled(false);
        });

        // 使用Application任务管理器在后台线程中执行分析
        // 注意：AnalyzerService.analyze()内部已经使用ReadAction.compute()
        com.intellij.openapi.application.ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                // 分析操作 - AnalyzerService内部会使用ReadAction
                AnalysisResult result = analyzerService.analyze(psiFile);

                // 在EDT中更新UI
                SwingUtilities.invokeLater(() -> {
                    try {
                        // 设置上下文以支持跳转功能
                        resultPanel.setContext(project, currentFile);
                        resultPanel.displayResult(result);
                        statusLabel.setText("分析完成: " + currentFile.getName());
                        analyzeButton.setEnabled(true);

                        // 保存当前分析结果和文件，并启用导出按钮
                        currentAnalysisResult = result;
                        currentPsiFile = psiFile;
                        exportButton.setEnabled(true);

                        // 更新按钮文本（从"分析当前文件"变为"刷新"）
                        updateAnalyzeButtonText();
                    } catch (Exception uiEx) {
                        statusLabel.setText("UI更新失败: " + uiEx.getMessage());
                        analyzeButton.setEnabled(true);
                        exportButton.setEnabled(false);
                        currentAnalysisResult = null;
                        currentPsiFile = null;

                        // 更新按钮文本（清空状态后恢复为"分析当前文件"）
                        updateAnalyzeButtonText();
                    }
                });
            } catch (Exception e) {
                // 在EDT中更新错误状态
                SwingUtilities.invokeLater(() -> {
                    statusLabel.setText("分析失败: " + e.getMessage());
                    resultPanel.clearResults();
                    analyzeButton.setEnabled(true);
                    exportButton.setEnabled(false);
                    currentAnalysisResult = null;
                    currentPsiFile = null;

                    // 更新按钮文本（错误状态下恢复为"分析当前文件"）
                    updateAnalyzeButtonText();
                });
                e.printStackTrace(); // 添加异常打印用于调试
            }
        });
    }

    private void exportCurrentResult() {
        if (currentAnalysisResult == null || currentPsiFile == null) {
            Messages.showErrorDialog("没有可导出的分析结果", "导出错误");
            return;
        }

        // 直接调用ExportReportAction的功能
        ExportReportAction exportAction = new ExportReportAction();

        // 创建包含文件和项目信息的DataContext
        DataContext dataContext = dataId -> {
            if (CommonDataKeys.PROJECT.getName().equals(dataId)) {
                return project;
            } else if (CommonDataKeys.PSI_FILE.getName().equals(dataId)) {
                return currentPsiFile;
            }
            return null;
        };

        // 创建AnActionEvent
        AnActionEvent event = AnActionEvent.createFromAnAction(
                exportAction,
                null,
                ActionPlaces.UNKNOWN,
                dataContext
        );

        exportAction.actionPerformed(event);
    }

    private VirtualFile getCurrentFile() {
        FileEditorManager editorManager = FileEditorManager.getInstance(project);
        VirtualFile[] selectedFiles = editorManager.getSelectedFiles();
        return selectedFiles.length > 0 ? selectedFiles[0] : null;
    }

    /**
     * 导出整个项目的Python文件
     */
    private void exportAllProjectFiles() {
        // 直接调用ExportProjectAction的功能
        ExportProjectAction exportAction = new ExportProjectAction();

        // 创建包含项目信息的DataContext
        DataContext dataContext = dataId -> {
            if (CommonDataKeys.PROJECT.getName().equals(dataId)) {
                return project;
            }
            return null;
        };

        // 创建AnActionEvent
        AnActionEvent event = AnActionEvent.createFromAnAction(
                exportAction,
                null,
                ActionPlaces.UNKNOWN,
                dataContext
        );

        exportAction.actionPerformed(event);
    }

    private void setupFileChangeListener() {
        // 监听文件编辑器的选择变化
        project.getMessageBus().connect().subscribe(
                com.intellij.openapi.fileEditor.FileEditorManagerListener.FILE_EDITOR_MANAGER,
                new com.intellij.openapi.fileEditor.FileEditorManagerListener() {
                    @Override
                    public void selectionChanged(com.intellij.openapi.fileEditor.FileEditorManagerEvent event) {
                        updateAnalyzeButtonText();
                    }
                }
        );
    }

    private void updateAnalyzeButtonText() {
        VirtualFile currentFile = getCurrentFile();

        // 如果当前有分析结果且导出按钮可用
        if (currentAnalysisResult != null && currentPsiFile != null && exportButton.isEnabled()) {
            // 检查当前文件是否与已分析的文件相同
            VirtualFile analyzedFile = currentPsiFile.getVirtualFile();
            if (currentFile != null && currentFile.equals(analyzedFile)) {
                // 当前文件就是已分析的文件，显示"刷新"
                analyzeButton.setText("刷新");
                analyzeButton.setToolTipText("重新分析当前文件");
            } else {
                // 当前文件与已分析的文件不同，显示"分析当前文件"
                analyzeButton.setText("分析当前文件");
                analyzeButton.setToolTipText("分析当前打开的文件");
            }
        } else {
            // 没有分析结果或导出按钮不可用，显示"分析当前文件"
            analyzeButton.setText("分析当前文件");
            analyzeButton.setToolTipText("分析当前打开的文件");
        }
    }
}