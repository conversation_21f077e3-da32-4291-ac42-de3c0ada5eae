package com.sankuai.deepcode.astplugin.analyzer;

import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.extensions.ExtensionPointName;
import com.intellij.psi.PsiFile;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.Language;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 分析器服务接口实现
 *
 * <AUTHOR>
 */
public class AnalyzerService {
    
    private static final Logger LOG = Logger.getInstance(AnalyzerService.class);
    private static final ExtensionPointName<ASTAnalyzer> ANALYZER_EP_NAME =
        ExtensionPointName.create("com.sankuai.deepcode.astplugin.analyzer");
    
    private static final AnalyzerService INSTANCE = new AnalyzerService();
    private final List<ASTAnalyzer> dynamicAnalyzers = new CopyOnWriteArrayList<>();
    
    private AnalyzerService() {}
    
    public static AnalyzerService getInstance() {
        return INSTANCE;
    }
    
    /**
     * 动态注册分析器
     */
    public void registerAnalyzer(ASTAnalyzer analyzer) {
        if (analyzer != null) {
            if (!dynamicAnalyzers.contains(analyzer)) {
                dynamicAnalyzers.add(analyzer);
                LOG.info("✅ Registered analyzer: " + analyzer.getClass().getSimpleName() +
                        " for language: " + analyzer.getSupportedLanguage());
                LOG.info("📊 Total registered analyzers: " + dynamicAnalyzers.size());
            } else {
                LOG.info("⚠️ Analyzer already registered: " + analyzer.getClass().getSimpleName());
            }
        } else {
            LOG.warn("❌ Tried to register null analyzer");
        }
    }
    
    /**
     * 分析PSI文件 - 确保在ReadAction中执行
     */
    public AnalysisResult analyze(PsiFile psiFile) {
        if (psiFile == null) {
            LOG.warn("PsiFile is null");
            return createErrorResult("PsiFile is null", Language.JAVA);
        }

        // 确保在ReadAction中执行所有PSI操作
        return ReadAction.compute(() -> {
            try {
                ASTAnalyzer analyzer = findAnalyzerForFile(psiFile);
                if (analyzer != null) {
                    LOG.info("✅ Using analyzer: " + analyzer.getClass().getSimpleName() + " for file: " + psiFile.getName());
                    return analyzer.analyze(psiFile);
                } else {
                    LOG.warn("❌ No analyzer found for file: " + psiFile.getName() + ", language: " + psiFile.getLanguage().getID());
                    LOG.info("📋 Available analyzers (" + getAvailableAnalyzers().size() + "):");
                    for (ASTAnalyzer a : getAvailableAnalyzers()) {
                        LOG.info("  - " + a.getClass().getSimpleName() + " supports: " + a.getSupportedLanguage());
                    }
                    return createUnsupportedLanguageResult(psiFile);
                }
            } catch (Exception e) {
                LOG.error("Error during analysis of file: " + psiFile.getName(), e);
                return createErrorResult("Error during analysis: " + e.getMessage(), Language.JAVA);
            }
        });
    }

    /**
     * 生成AST报告 - 确保在ReadAction中执行
     */
    public String generateASTReport(PsiFile psiFile) {
        return ReadAction.compute(() -> {
            try {
                AnalysisResult result = analyze(psiFile);
                if (result != null) {
                    ASTAnalyzer analyzer = findAnalyzerForFile(psiFile);
                    if (analyzer != null) {
                        return analyzer.generateASTReport(psiFile);
                    }
                }
                return "Failed to generate report";
            } catch (Exception e) {
                LOG.error("Error generating AST report", e);
                return "Error generating report: " + e.getMessage();
            }
        });
    }

    /**
     * 获取可用的分析器
     */
    public List<ASTAnalyzer> getAvailableAnalyzers() {
        List<ASTAnalyzer> allAnalyzers = new ArrayList<>();
        
        // 首先添加动态注册的分析器
        allAnalyzers.addAll(dynamicAnalyzers);
        
        // 然后尝试从扩展点获取
        try {
            if (ANALYZER_EP_NAME.hasAnyExtensions()) {
                allAnalyzers.addAll(Arrays.asList(ANALYZER_EP_NAME.getExtensions()));
            }
        } catch (Exception e) {
            LOG.warn("Failed to get analyzers from extension point", e);
        }
        
        return allAnalyzers;
    }

    /**
     * 为文件查找合适的分析器 - 在ReadAction中执行
     */
    public ASTAnalyzer findAnalyzerForFile(PsiFile psiFile) {
        if (psiFile == null) {
            return null;
        }

        List<ASTAnalyzer> analyzers = getAvailableAnalyzers();
        for (ASTAnalyzer analyzer : analyzers) {
            try {
                // supports方法可能需要访问PSI，确保在ReadAction中
                boolean supports = ReadAction.compute(() -> analyzer.supports(psiFile));
                if (supports) {
                    return analyzer;
                }
            } catch (Exception e) {
                LOG.warn("Error checking analyzer support: " + analyzer.getClass().getSimpleName(), e);
            }
        }

        return null;
    }

    /**
     * 创建错误结果
     */
    private AnalysisResult createErrorResult(String errorMessage, Language language) {
        AnalysisResult result = new AnalysisResult("Unknown", language);
        result.addError(errorMessage);
        return result;
    }

    /**
     * 创建不支持语言的结果 - 在ReadAction中执行PSI操作
     */
    private AnalysisResult createUnsupportedLanguageResult(PsiFile psiFile) {
        // 注意：此方法已经在ReadAction中被调用
        Language language = mapToLanguageEnum(psiFile.getLanguage().getDisplayName());
        String fileName = psiFile.getName();

        AnalysisResult result = new AnalysisResult(fileName, language);
        result.addError("No analyzer available for language: " + psiFile.getLanguage().getDisplayName());

        try {
            // PSI操作已经在ReadAction上下文中
            result.updateStatistics("file_size", psiFile.getTextLength());
            result.updateStatistics("language_supported", 0);

            // 计算元素数量 - 使用更安全的方式
            int elementCount = countPsiElements(psiFile);
            result.updateStatistics("total_elements", elementCount);
        } catch (Exception e) {
            LOG.warn("Error getting file statistics: " + e.getMessage());
            // 如果PSI操作失败，至少设置基本统计
            result.updateStatistics("file_size", 0);
            result.updateStatistics("total_elements", 0);
        }

        return result;
    }
    
    /**
     * 安全地计算PSI元素数量
     */
    private int countPsiElements(PsiFile psiFile) {
        try {
            // 使用更轻量级的方式计算元素
            return com.intellij.psi.util.PsiTreeUtil.findChildrenOfType(psiFile, com.intellij.psi.PsiElement.class).size();
        } catch (Exception e) {
            LOG.warn("Error counting PSI elements: " + e.getMessage());
            return 0;
        }
    }

    private Language mapToLanguageEnum(String languageDisplayName) {
        if (languageDisplayName == null) return Language.JAVA;
        
        return switch (languageDisplayName.toLowerCase()) {
            case "java" -> Language.JAVA;
            case "python" -> Language.PYTHON;
            default -> Language.JAVA;
        };
    }
}