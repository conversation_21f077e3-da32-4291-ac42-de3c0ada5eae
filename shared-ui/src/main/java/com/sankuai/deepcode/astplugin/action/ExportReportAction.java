package com.sankuai.deepcode.astplugin.action;

import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.fileChooser.FileChooser;
import com.intellij.openapi.fileChooser.FileChooserDescriptor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;
import com.sankuai.deepcode.astplugin.analyzer.AnalyzerService;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;
import com.sankuai.deepcode.astplugin.model.ImportInfo;
import org.jetbrains.annotations.NotNull;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AST 解析结果报告导出
 *
 * <AUTHOR>
 */
public class ExportReportAction extends AnAction {

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        PsiFile psiFile = e.getData(CommonDataKeys.PSI_FILE);

        if (project == null || psiFile == null) {
            Messages.showErrorDialog("No file selected or project context missing", "Error");
            return;
        }

        // Show file chooser dialog first
        FileChooserDescriptor descriptor = new FileChooserDescriptor(false, true, false, false, false, false);
        descriptor.setTitle("Choose Directory to Save AST Report");
        descriptor.setDescription("Select a directory where the AST report will be saved as JSON");

        VirtualFile selectedDir = FileChooser.chooseFile(descriptor, project, null);
        if (selectedDir != null) {
            // Move analysis to background thread to avoid EDT slow operations
            ApplicationManager.getApplication().executeOnPooledThread(() -> {
                AnalysisResult analysisResult = null;
                String fileName = null;
                
                try {
                    // Generate AST analysis result in read action to ensure PSI safety
                    // Use a holder to capture the result from ReadAction
                    final AnalysisResult[] resultHolder = new AnalysisResult[1];
                    final String[] fileNameHolder = new String[1];
                    
                    com.intellij.openapi.application.ReadAction.run(() -> {
                        try {
                            AnalyzerService analyzerService = AnalyzerService.getInstance();
                            AnalysisResult result = analyzerService.analyze(psiFile);
                            
                            // Create a completely detached copy of the analysis result
                            // This ensures no PSI references are held
                            resultHolder[0] = createDetachedAnalysisResult(result);
                            fileNameHolder[0] = psiFile.getName();
                        } catch (Exception ex) {
                            throw new RuntimeException("Analysis failed in ReadAction", ex);
                        }
                    });
                    
                    analysisResult = resultHolder[0];
                    fileName = fileNameHolder[0];
                    
                    if (analysisResult == null) {
                        throw new RuntimeException("Analysis result is null");
                    }
                    
                } catch (Exception ex) {
                    // Handle any errors in analysis
                    ApplicationManager.getApplication().invokeLater(() ->
                        Messages.showErrorDialog("Analysis failed: " + ex.getMessage(), "Analysis Error")
                    );
                    return;
                }
                
                // Export report in separate thread with detached data
                final AnalysisResult finalResult = analysisResult;
                final String finalFileName = fileName;
                
                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    try {
                        exportReportAsJson(finalResult, selectedDir, finalFileName, project);
                    } catch (Exception ex) {
                        ApplicationManager.getApplication().invokeLater(() ->
                            Messages.showErrorDialog("Export failed: " + ex.getMessage(), "Export Error")
                        );
                    }
                });
            });
        }
    }

    private void exportReportAsJson(AnalysisResult analysisResult, VirtualFile directory, String originalFileName, Project project) {
        try {
            // Generate filename with timestamp
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String baseFileName = originalFileName.replaceAll("\\.[^.]*$", ""); // Remove extension
            String exportFileName = String.format("AST_Report_%s_%s.json", baseFileName, timestamp);

            String filePath = directory.getPath() + "/" + exportFileName;

            // Convert analysis result to JSON
            String jsonReport = convertToJson(analysisResult, originalFileName, filePath);

            // Write JSON report to file
            try (FileWriter writer = new FileWriter(filePath)) {
                writer.write(jsonReport);
            }

            // Show success message using ApplicationManager.invokeLater to ensure proper EDT thread handling
            ApplicationManager.getApplication().invokeLater(() -> {
                int result = Messages.showYesNoDialog(
                        project,
                        "AST report exported successfully to:\n" + filePath + "\n\nWould you like to open the containing folder?",
                        "Export Successful",
                        "Open Folder",
                        "Close",
                        Messages.getInformationIcon()
                );

                if (result == Messages.YES) {
                    try {
                        // Open the containing folder
                        String os = System.getProperty("os.name").toLowerCase();
                        if (os.contains("win")) {
                            Runtime.getRuntime().exec("explorer.exe /select," + filePath);
                        } else if (os.contains("mac")) {
                            Runtime.getRuntime().exec("open -R " + filePath);
                        } else {
                            Runtime.getRuntime().exec("xdg-open " + directory.getPath());
                        }
                    } catch (IOException ex) {
                        // Also wrap this warning dialog in invokeLater for consistency
                        ApplicationManager.getApplication().invokeLater(() ->
                            Messages.showWarningDialog("Could not open folder: " + ex.getMessage(), "Warning")
                        );
                    }
                }
            });

        } catch (IOException ex) {
            ApplicationManager.getApplication().invokeLater(() ->
                Messages.showErrorDialog("Failed to export report: " + ex.getMessage(), "Export Error")
            );
        }
    }

    /**
     * 将分析结果转换为JSON格式
     */
    private String convertToJson(AnalysisResult result, String originalFileName, String exportPath) {
        StringBuilder json = new StringBuilder();
        json.append("{\n");

        // 基本信息
        json.append("  \"metadata\": {\n");
        json.append("    \"originalFile\": \"").append(escapeJson(originalFileName)).append("\",\n");
        json.append("    \"exportTime\": \"").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\",\n");
        json.append("    \"exportPath\": \"").append(escapeJson(exportPath)).append("\",\n");
        json.append("    \"analysisTime\": \"").append(result.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\",\n");
        json.append("    \"language\": \"").append(result.getLanguage().name()).append("\",\n");
        json.append("    \"fileName\": \"").append(escapeJson(result.getFileName())).append("\"\n");
        json.append("  },\n");

        // 统计信息
        json.append("  \"statistics\": {\n");
        Map<String, Integer> stats = result.getStatistics();
        List<String> statKeys = new ArrayList<>(stats.keySet());
        for (int i = 0; i < statKeys.size(); i++) {
            String key = statKeys.get(i);
            json.append("    \"").append(key).append("\": ").append(stats.get(key));
            if (i < statKeys.size() - 1) {
                json.append(",");
            }
            json.append("\n");
        }
        json.append("  },\n");

        // 导入信息
        json.append("  \"imports\": [\n");
        List<ImportInfo> imports = result.getImports();
        for (int i = 0; i < imports.size(); i++) {
            ImportInfo importInfo = imports.get(i);
            json.append("    {\n");
            json.append("      \"statement\": \"").append(escapeJson(importInfo.getStatement())).append("\",\n");
            json.append("      \"lineNumber\": ").append(importInfo.getLineNumber()).append(",\n");
            json.append("      \"type\": \"").append(importInfo.getType().name()).append("\",\n");
            json.append("      \"isExternal\": ").append(importInfo.isExternal()).append(",\n");
            json.append("      \"filePath\": \"").append(escapeJson(importInfo.getFilePath())).append("\",\n");

            // 添加被导入的目标文件路径
            String targetFilePath = importInfo.getTargetFilePath();
            json.append("      \"targetFilePath\": ").append(targetFilePath != null ? "\"" + escapeJson(targetFilePath) + "\"" : "null").append(",\n");

            json.append("      \"resolvedClasses\": [\n");

            List<String> resolvedClasses = importInfo.getResolvedClasses();
            for (int j = 0; j < resolvedClasses.size(); j++) {
                json.append("        \"").append(escapeJson(resolvedClasses.get(j))).append("\"");
                if (j < resolvedClasses.size() - 1) {
                    json.append(",");
                }
                json.append("\n");
            }
            json.append("      ]\n");
            json.append("    }");
            if (i < imports.size() - 1) {
                json.append(",");
            }
            json.append("\n");
        }
        json.append("  ],\n");

        // 代码结构节点
        json.append("  \"nodes\": [\n");
        Collection<AnalysisNode> nodes = result.getNodes().values();
        List<AnalysisNode> nodeList = nodes.stream()
                .sorted(Comparator.comparing(AnalysisNode::getLineNumber))
                .collect(Collectors.toList());

        for (int i = 0; i < nodeList.size(); i++) {
            AnalysisNode node = nodeList.get(i);
            json.append("    {\n");
            json.append("      \"id\": \"").append(escapeJson(node.getId())).append("\",\n");
            json.append("      \"type\": \"").append(node.getType().name()).append("\",\n");
            json.append("      \"name\": \"").append(escapeJson(node.getName())).append("\",\n");
            json.append("      \"className\": \"").append(escapeJson(node.getClassName())).append("\",\n");
            json.append("      \"packageName\": \"").append(escapeJson(node.getPackageName())).append("\",\n");
            json.append("      \"lineNumber\": ").append(node.getLineNumber()).append(",\n");
            json.append("      \"signature\": \"").append(escapeJson(node.getSignature())).append("\",\n");
            json.append("      \"moduleName\": ").append(node.getModuleName() != null ? "\"" + escapeJson(node.getModuleName()) + "\"" : "null").append(",\n");
            json.append("      \"filePath\": ").append(node.getFilePath() != null ? "\"" + escapeJson(node.getFilePath()) + "\"" : "null").append(",\n");
            json.append("      \"language\": \"").append(escapeJson(node.getLanguage())).append("\"\n");
            json.append("    }");
            if (i < nodeList.size() - 1) {
                json.append(",");
            }
            json.append("\n");
        }
        json.append("  ],\n");

        // 调用关系
        json.append("  \"callRelations\": [\n");
        List<CallRelation> relations = result.getCallRelations();
        for (int i = 0; i < relations.size(); i++) {
            CallRelation relation = relations.get(i);
            json.append("    {\n");
            json.append("      \"caller\": {\n");
            json.append("        \"id\": \"").append(escapeJson(relation.getCaller().getId())).append("\",\n");
            json.append("        \"signature\": \"").append(escapeJson(relation.getCaller().getSignature())).append("\",\n");
            json.append("        \"name\": \"").append(escapeJson(relation.getCaller().getName())).append("\",\n");
            json.append("        \"className\": \"").append(escapeJson(relation.getCaller().getClassName())).append("\",\n");
            json.append("        \"packageName\": \"").append(escapeJson(relation.getCaller().getPackageName())).append("\",\n");
            json.append("        \"lineNumber\": ").append(relation.getCaller().getLineNumber()).append(",\n");
            json.append("        \"type\": \"").append(relation.getCaller().getType().name()).append(",\n");
            json.append("        \"language\": \"").append(escapeJson(relation.getCaller().getLanguage())).append("\",\n");

            // 添加模块和文件路径信息
            String callerModuleName = relation.getCaller().getModuleName();
            String callerFilePath = relation.getCaller().getFilePath();

            json.append("        \"moduleName\": ").append(callerModuleName != null ? "\"" + escapeJson(callerModuleName) + "\"" : "null").append(",\n");
            json.append("        \"filePath\": ").append(callerFilePath != null ? "\"" + escapeJson(callerFilePath) + "\"" : "null").append("\n");
            json.append("      },\n");
            json.append("      \"callee\": {\n");
            json.append("        \"id\": \"").append(escapeJson(relation.getCallee().getId())).append("\",\n");
            json.append("        \"signature\": \"").append(escapeJson(relation.getCallee().getSignature())).append("\",\n");
            json.append("        \"name\": \"").append(escapeJson(relation.getCallee().getName())).append("\",\n");
            json.append("        \"className\": \"").append(escapeJson(relation.getCallee().getClassName())).append("\",\n");
            json.append("        \"packageName\": \"").append(escapeJson(relation.getCallee().getPackageName())).append("\",\n");
            json.append("        \"lineNumber\": ").append(relation.getCallee().getLineNumber()).append(",\n");
            json.append("        \"type\": \"").append(relation.getCallee().getType().name()).append("\",\n");
            json.append("        \"language\": \"").append(escapeJson(relation.getCallee().getLanguage())).append("\",\n");

            // 添加模块和文件路径信息（特别是对外部调用很重要）
            String calleeModuleName = relation.getCallee().getModuleName();
            String calleeFilePath = relation.getCallee().getFilePath();

            json.append("        \"moduleName\": ").append(calleeModuleName != null ? "\"" + escapeJson(calleeModuleName) + "\"" : "null").append(",\n");
            json.append("        \"filePath\": ").append(calleeFilePath != null ? "\"" + escapeJson(calleeFilePath) + "\"" : "null").append(",\n");

            // 添加是否为外部调用的标识
            json.append("        \"isExternal\": ").append(relation.isExternal()).append("\n");
            json.append("      },\n");
            json.append("      \"isExternal\": ").append(relation.isExternal()).append(",\n");
            json.append("      \"callCount\": ").append(relation.getCallCount()).append(",\n");
            json.append("      \"callInstances\": [\n");

            List<CallRelation.CallInstance> instances = relation.getAllCallInstances();
            for (int j = 0; j < instances.size(); j++) {
                CallRelation.CallInstance instance = instances.get(j);
                json.append("        {\n");
                json.append("          \"lineNumber\": ").append(instance.getLineNumber()).append(",\n");
                json.append("          \"columnNumber\": ").append(instance.getColumnNumber()).append(",\n");
                json.append("          \"expression\": \"").append(escapeJson(instance.getExpression())).append("\"\n");
                json.append("        }");
                if (j < instances.size() - 1) json.append(",");
                json.append("\n");
            }
            json.append("      ]\n");
            json.append("    }");
            if (i < relations.size() - 1) {
                json.append(",");
            }
            json.append("\n");
        }
        json.append("  ],\n");

        // 错误信息
        json.append("  \"errors\": [\n");
        List<String> errors = result.getErrors();
        for (int i = 0; i < errors.size(); i++) {
            json.append("    \"").append(escapeJson(errors.get(i))).append("\"");
            if (i < errors.size() - 1) {
                json.append(",");
            }
            json.append("\n");
        }
        json.append("  ]\n");

        json.append("}");
        return json.toString();
    }

    /**
     * 创建与PSI完全解耦的分析结果副本
     * 这确保导出过程中不会访问任何PSI元素
     */
    private AnalysisResult createDetachedAnalysisResult(AnalysisResult original) {
        try {
            // 创建新的AnalysisResult实例，使用原始的基本信息
            AnalysisResult detached = new AnalysisResult(original.getFileName(), original.getLanguage());
            
            // 复制节点（AnalysisNode应该已经是纯数据对象）
            for (AnalysisNode node : original.getNodes().values()) {
                detached.addNode(createDetachedNode(node));
            }
            
            // 复制调用关系（创建安全的副本）
            for (CallRelation relation : original.getCallRelations()) {
                detached.addCallRelation(createDetachedCallRelation(relation));
            }
            
            // 复制导入信息（应该已经是纯数据）
            for (ImportInfo importInfo : original.getImports()) {
                detached.addImport(createDetachedImport(importInfo));
            }
            
            // 复制错误信息（纯字符串）
            for (String error : original.getErrors()) {
                detached.addError(error);
            }
            
            // 复制统计信息（纯基本类型）
            for (Map.Entry<String, Integer> stat : original.getStatistics().entrySet()) {
                detached.updateStatistics(stat.getKey(), stat.getValue());
            }
            
            return detached;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create detached analysis result: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建与PSI解耦的节点副本
     */
    private AnalysisNode createDetachedNode(AnalysisNode original) {
        return new AnalysisNode(
            safeString(original.getId()),
            original.getType(),
            safeString(original.getName()),
            safeString(original.getClassName()),
            safeString(original.getPackageName()),
            original.getLineNumber(),
            safeString(original.getSignature()),
            safeString(original.getModuleName()),
            safeString(original.getFilePath()),
            safeString(original.getLanguage()),
            original.getModuleType()
        );
    }
    
    /**
     * 创建与PSI解耦的调用关系副本
     */
    private CallRelation createDetachedCallRelation(CallRelation original) {
        // 复制所有调用实例，创建安全的副本
        List<CallRelation.CallInstance> detachedInstances = new ArrayList<>();
        for (CallRelation.CallInstance instance : original.getAllCallInstances()) {
            detachedInstances.add(new CallRelation.CallInstance(
                instance.getLineNumber(),
                instance.getColumnNumber(),
                safeString(instance.getExpression())  // 这里确保表达式是安全的字符串
            ));
        }
        
        // 使用所有参数的构造函数创建新的CallRelation
        CallRelation detached = new CallRelation(
            createDetachedNode(original.getCaller()),
            createDetachedNode(original.getCallee()),
            original.getCallLineNumber(),  // 使用正确的方法名
            safeString(original.getCallExpression()),  // 使用正确的方法名
            original.isExternal(),
            detachedInstances
        );
        
        return detached;
    }
    
    /**
     * 创建与PSI解耦的导入信息副本
     */
    private ImportInfo createDetachedImport(ImportInfo original) {
        // 安全地复制解析的类列表
        List<String> detachedResolvedClasses = new ArrayList<>();
        for (String resolvedClass : original.getResolvedClasses()) {
            detachedResolvedClasses.add(safeString(resolvedClass));
        }
        
        // 使用完整的构造函数创建新的ImportInfo，包含filePath和targetFilePath
        return new ImportInfo(
            safeString(original.getStatement()),
            original.getLineNumber(),
            original.getType(),
            original.isExternal(),
            detachedResolvedClasses,
            safeString(original.getFilePath()),  // 当前文件路径
            safeString(original.getTargetFilePath())  // 被导入的文件路径
        );
    }
    
    /**
     * 安全地转换字符串，确保没有PSI引用
     */
    private String safeString(String str) {
        return str == null ? null : String.valueOf(str);  // 创建新的String对象
    }

    /**
     * 转义JSON字符串中的特殊字符
     */
    private String escapeJson(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    @Override
    public void update(@NotNull AnActionEvent e) {
        // Enable action only when a file is open
        PsiFile psiFile = e.getData(CommonDataKeys.PSI_FILE);
        e.getPresentation().setEnabled(psiFile != null);
    }

    @Override
    public @NotNull ActionUpdateThread getActionUpdateThread() {
        return ActionUpdateThread.BGT;
    }
}