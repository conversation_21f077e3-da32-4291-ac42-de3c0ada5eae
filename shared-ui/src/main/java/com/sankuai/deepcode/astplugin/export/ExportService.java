package com.sankuai.deepcode.astplugin.export;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * 导出服务接口，用于支持不同语言的项目分析结果导出
 *
 * <AUTHOR>
 */
public interface ExportService {

    /**
     * 获取支持的导出语言名称
     * @return 语言名称（如：Java, Python）
     */
    @NotNull
    String getLanguageName();

    /**
     * 获取支持的导出选项
     * @return 导出选项列表
     */
    @NotNull
    List<ExportOption> getSupportedExportOptions();

    /**
     * 执行导出操作
     * @param project 项目对象
     * @param exportOption 导出选项
     * @param outputDir 输出目录
     */
    void exportProject(@NotNull Project project, @NotNull ExportOption exportOption, @NotNull VirtualFile outputDir);

    /**
     * 检查项目中是否包含该语言的文件
     * @param project 项目对象
     * @return 如果包含该语言文件返回true
     */
    boolean hasLanguageFilesInProject(@NotNull Project project);

    /**
     * 获取项目中该语言文件的数量
     * @param project 项目对象
     * @return 文件数量
     */
    int getLanguageFileCount(@NotNull Project project);
}