package com.sankuai.deepcode.astplugin.ui;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPanel;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.components.JBTabbedPane;
import com.intellij.ui.treeStructure.Tree;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

import javax.swing.*;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import java.awt.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;

/**
 * AST分析结果显示面板
 *
 * <AUTHOR>
 */
public class ASTResultPanel extends JBPanel<ASTResultPanel> {
    private JBTabbedPane tabbedPane;
    private JBLabel headerLabel;
    private Tree structureTree;
    private JPanel callRelationsPanel;
    private JPanel statisticsPanel;
    private Project project;
    private VirtualFile currentFile;

    public ASTResultPanel() {
        super(new BorderLayout());
        initializeUI();
    }

    public ASTResultPanel(Project project, VirtualFile currentFile) {
        super(new BorderLayout());
        this.project = project;
        this.currentFile = currentFile;
        initializeUI();
    }

    public void setContext(Project project, VirtualFile currentFile) {
        this.project = project;
        this.currentFile = currentFile;
    }

    private void initializeUI() {
        // 头部信息
        headerLabel = new JBLabel("暂无分析结果");
        headerLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        headerLabel.setFont(headerLabel.getFont().deriveFont(Font.BOLD, 14f));
        add(headerLabel, BorderLayout.NORTH);

        // 选项卡面板
        tabbedPane = new JBTabbedPane();

        // 代码结构选项卡
        structureTree = new Tree();
        structureTree.setRootVisible(false);
        setupStructureTreeClickHandler();
        JBScrollPane structureScrollPane = new JBScrollPane(structureTree);
        tabbedPane.addTab("代码结构", structureScrollPane);

        // 调用关系选项卡
        callRelationsPanel = new JBPanel<>(new BorderLayout());
        tabbedPane.addTab("调用关系", callRelationsPanel);

        // 统计信息选项卡
        statisticsPanel = new JBPanel<>(new BorderLayout());
        tabbedPane.addTab("统计信息", statisticsPanel);

        add(tabbedPane, BorderLayout.CENTER);

        // 初始状态
        clearResults();
    }

    public void displayResult(AnalysisResult result) {
        if (result == null) {
            clearResults();
            return;
        }

        // 更新头部信息
        updateHeader(result);

        // 更新代码结构树
        updateStructureTree(result);

        // 更新调用关系面板
        updateCallRelationsPanel(result);

        // 更新统计信息面板
        updateStatisticsPanel(result);

        tabbedPane.setVisible(true);
    }

    public void clearResults() {
        headerLabel.setText("暂无分析结果");
        structureTree.setModel(new DefaultTreeModel(new DefaultMutableTreeNode("暂无数据")));
        callRelationsPanel.removeAll();
        statisticsPanel.removeAll();
        tabbedPane.setVisible(false);
        repaint();
    }

    private void updateHeader(AnalysisResult result) {
        String headerText = String.format(
                "<html><b>文件:</b> %s<br/><b>语言:</b> %s<br/><b>分析时间:</b> %s</html>",
                result.getFileName(),
                result.getLanguage(),
                result.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );
        headerLabel.setText(headerText);
    }

    private void updateStructureTree(AnalysisResult result) {
        DefaultMutableTreeNode root = new DefaultMutableTreeNode("代码结构");

        // 首先添加导入信息
        List<ImportInfo> imports = result.getImports();
        if (!imports.isEmpty()) {
            DefaultMutableTreeNode importsNode = getDefaultMutableTreeNode(imports);

            root.add(importsNode);
        }

        // 按类型分组节点
        Map<AnalysisNode.NodeType, List<AnalysisNode>> nodesByType = new HashMap<>();
        for (AnalysisNode node : result.getNodes().values()) {
            nodesByType.computeIfAbsent(node.getType(), k -> new ArrayList<>()).add(node);
        }

        // 按特定顺序显示节点类型
        AnalysisNode.NodeType[] orderedTypes = {
                AnalysisNode.NodeType.CLASS,
                AnalysisNode.NodeType.INTERFACE,
                AnalysisNode.NodeType.ENUM,
                AnalysisNode.NodeType.METHOD,
                AnalysisNode.NodeType.FIELD,
                AnalysisNode.NodeType.FUNCTION,
                AnalysisNode.NodeType.VARIABLE
        };

        // 为每种类型创建树节点
        for (AnalysisNode.NodeType type : orderedTypes) {
            List<AnalysisNode> typeNodes = nodesByType.get(type);
            if (typeNodes != null && !typeNodes.isEmpty()) {
                DefaultMutableTreeNode typeNode = new DefaultMutableTreeNode(
                        String.format("%s (%d)", getTypeDisplayName(type), typeNodes.size())
                );

                typeNodes.stream()
                        .sorted(Comparator.comparing(AnalysisNode::getLineNumber))
                        .forEach(node -> {
                            String nodeText = String.format("%s (第%d行)", node.getName(), node.getLineNumber());
                            StructureNodeInfo nodeInfo = new StructureNodeInfo(node.getLineNumber(), nodeText);
                            DefaultMutableTreeNode nodeTreeNode = new DefaultMutableTreeNode(nodeInfo);
                            typeNode.add(nodeTreeNode);
                        });

                root.add(typeNode);
            }
        }

        structureTree.setModel(new DefaultTreeModel(root));

        // 展开所有节点
        for (int i = 0; i < structureTree.getRowCount(); i++) {
            structureTree.expandRow(i);
        }
    }

    private static DefaultMutableTreeNode getDefaultMutableTreeNode(List<ImportInfo> imports) {
        DefaultMutableTreeNode importsNode = new DefaultMutableTreeNode(
                String.format("导入 (%d)", imports.size())
        );

        for (ImportInfo importInfo : imports) {
            // 创建导入节点信息，包含行号用于跳转
            String nodeText = String.format("%s (第%d行)",
                    importInfo.getStatement(), importInfo.getLineNumber());
            ImportNodeInfo importNodeInfo = new ImportNodeInfo(importInfo.getLineNumber(), nodeText, importInfo);
            DefaultMutableTreeNode importNode = new DefaultMutableTreeNode(importNodeInfo);
            importsNode.add(importNode);
        }
        return importsNode;
    }

    /**
     * 设置代码结构树的点击处理器
     */
    private void setupStructureTreeClickHandler() {
        structureTree.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                if (e.getClickCount() == 1) { // 单击
                    javax.swing.tree.TreePath path = structureTree.getPathForLocation(e.getX(), e.getY());
                    if (path != null) {
                        DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();
                        handleStructureNodeClick(node);
                    }
                }
            }
        });

        // 添加鼠标移动监听器来显示 tooltip
        structureTree.addMouseMotionListener(new java.awt.event.MouseMotionAdapter() {
            @Override
            public void mouseMoved(java.awt.event.MouseEvent e) {
                int row = structureTree.getRowForLocation(e.getX(), e.getY());
                if (row >= 0) {
                    javax.swing.tree.TreePath path = structureTree.getPathForRow(row);
                    if (path != null) {
                        DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();
                        Object userObject = node.getUserObject();

                        String tooltip = null;
                        if (userObject instanceof ImportNodeInfo info) {
                            ImportInfo importInfo = info.importInfo;

                            StringBuilder resolvedClasses = new StringBuilder();
                            if (!importInfo.getResolvedClasses().isEmpty()) {
                                for (String className : importInfo.getResolvedClasses()) {
                                    if (!resolvedClasses.isEmpty()) {
                                        resolvedClasses.append("<br/>");
                                    }
                                    resolvedClasses.append("  - ").append(className);
                                }
                            }

                            tooltip = String.format(
                                    "<html><b>导入语句:</b> %s<br/>" +
                                            "<b>行号:</b> %d<br/>" +
                                            "<b>类型:</b> %s<br/>" +
                                            "<b>来源:</b> %s<br/>" +
                                            "<b>文件路径:</b> %s<br/>" +
                                            "%s" +
                                            "<b>提示:</b> 单击跳转到导入语句</html>",
                                    importInfo.getStatement(),
                                    importInfo.getLineNumber(),
                                    getImportTypeDisplayName(importInfo.getType()),
                                    importInfo.isExternal() ? "外部" : "内部",
                                    importInfo.getFilePath() != null ? importInfo.getFilePath() : "未知",
                                    !resolvedClasses.isEmpty() ?
                                            "<b>解析的类:</b><br/>" + resolvedClasses.toString() + "<br/>" : ""
                            );
                        } else if (userObject instanceof StructureNodeInfo info) {
                            tooltip = String.format(
                                    "<html><b>元素:</b> %s<br/>" +
                                            "<b>提示:</b> 单击跳转到第%d行</html>",
                                    info.text.replaceAll(" \\(第\\d+行\\)", ""),
                                    info.lineNumber
                            );
                        }

                        structureTree.setToolTipText(tooltip);
                    }
                } else {
                    structureTree.setToolTipText(null);
                }
            }
        });
    }

    /**
     * 处理代码结构节点点击
     */
    private void handleStructureNodeClick(DefaultMutableTreeNode node) {
        if (project == null || currentFile == null) {
            return;
        }

        Object userObject = node.getUserObject();
        if (userObject instanceof StructureNodeInfo info) {
            jumpToLine(info.lineNumber);
        } else if (userObject instanceof ImportNodeInfo info) {
            jumpToLine(info.lineNumber);
        } else {
            // 尝试从节点文本中解析行号
            String nodeText = userObject.toString();
            int lineNumber = parseLineNumberFromText(nodeText);
            if (lineNumber > 0) {
                jumpToLine(lineNumber);
            }
        }
    }

    /**
     * 从文本中解析行号
     */
    private int parseLineNumberFromText(String text) {
        try {
            // 匹配 "(第X行)" 格式
            int startIndex = text.lastIndexOf("(第");
            int endIndex = text.lastIndexOf("行)");
            if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
                String lineStr = text.substring(startIndex + 2, endIndex);
                return Integer.parseInt(lineStr);
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        return -1;
    }

    /**
     * 跳转到指定行
     */
    private void jumpToLine(int lineNumber) {
        if (project == null || currentFile == null || lineNumber <= 0) {
            return;
        }

        try {
            com.intellij.openapi.fileEditor.OpenFileDescriptor descriptor =
                    new com.intellij.openapi.fileEditor.OpenFileDescriptor(project, currentFile, lineNumber - 1, 0);
            com.intellij.openapi.editor.Editor editor =
                    com.intellij.openapi.fileEditor.FileEditorManager.getInstance(project).openTextEditor(descriptor, true);
            if (editor != null) {
                com.intellij.openapi.editor.LogicalPosition position =
                        new com.intellij.openapi.editor.LogicalPosition(lineNumber - 1, 0);
                editor.getCaretModel().moveToLogicalPosition(position);
                editor.getScrollingModel().scrollToCaret(com.intellij.openapi.editor.ScrollType.CENTER);
            }
        } catch (Exception e) {
            System.err.println("Error jumping to line " + lineNumber + ": " + e.getMessage());
        }
    }

    /**
     * 设置调用关系树的点击处理器
     */
    private void setupCallRelationTreeClickHandler(Tree callRelationTree) {
        callRelationTree.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                if (e.getClickCount() == 1) { // 单击
                    javax.swing.tree.TreePath path = callRelationTree.getPathForLocation(e.getX(), e.getY());
                    if (path != null) {
                        DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();
                        handleCallRelationNodeClick(node);
                    }
                }
            }
        });
    }

    /**
     * 处理调用关系节点点击
     */
    private void handleCallRelationNodeClick(DefaultMutableTreeNode node) {
        if (project == null || currentFile == null) {
            return;
        }

        Object userObject = node.getUserObject();
        if (userObject instanceof CalleeNodeInfo info) {
            CallRelation relation = info.relation;

            // 如果只有一个调用实例，直接跳转
            if (relation.getAllCallInstances().size() == 1) {
                CallRelation.CallInstance instance = relation.getAllCallInstances().get(0);
                jumpToLine(instance.getLineNumber());
            } else {
                // 多个调用实例，显示选择对话框
                showCallInstanceSelectionDialog(relation);
            }
        } else if (userObject instanceof CallerNodeInfo info) {
            // 跳转到调用方定义
            jumpToLine(info.caller.getLineNumber());
        }
    }

    /**
     * 显示调用实例选择对话框
     */
    private void showCallInstanceSelectionDialog(CallRelation relation) {
        List<CallRelation.CallInstance> instances = relation.getAllCallInstances();
        String[] options = new String[instances.size()];

        for (int i = 0; i < instances.size(); i++) {
            CallRelation.CallInstance instance = instances.get(i);
            options[i] = String.format("第%d行: %s", instance.getLineNumber(), instance.getExpression());
        }

        try {
            // 确保在EDT线程中执行，并延迟执行以确保组件完全显示
            SwingUtilities.invokeLater(() -> {
                try {
                    // 获取顶层窗口作为父组件，避免组件位置问题
                    Window parentWindow = SwingUtilities.getWindowAncestor(this);
                    Component parentComponent = (parentWindow != null && parentWindow.isDisplayable()) ? parentWindow : null;

                    String selected = (String) JOptionPane.showInputDialog(
                            parentComponent,
                            "选择要跳转的调用位置:",
                            "多个调用位置",
                            JOptionPane.QUESTION_MESSAGE,
                            null,
                            options,
                            options[0]
                    );

                    if (selected != null) {
                        // 解析选中的行号
                        for (CallRelation.CallInstance instance : instances) {
                            String optionText = String.format("第%d行: %s", instance.getLineNumber(), instance.getExpression());
                            if (optionText.equals(selected)) {
                                jumpToLine(instance.getLineNumber());
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    System.err.println("Error showing call instance selection dialog: " + e.getMessage());
                    // 回退方案：直接跳转到第一个调用位置
                    if (!instances.isEmpty()) {
                        jumpToLine(instances.get(0).getLineNumber());
                    }
                }
            });
        } catch (Exception e) {
            System.err.println("Error scheduling dialog display: " + e.getMessage());
            // 回退方案：直接跳转到第一个调用位置
            if (!instances.isEmpty()) {
                jumpToLine(instances.get(0).getLineNumber());
            }
        }
    }

    private void updateCallRelationsPanel(AnalysisResult result) {
        callRelationsPanel.removeAll();

        List<CallRelation> relations = result.getCallRelations();
        if (relations.isEmpty()) {
            JBLabel noDataLabel = new JBLabel("暂无调用关系数据", SwingConstants.CENTER);
            callRelationsPanel.add(noDataLabel, BorderLayout.CENTER);
        } else {
            JPanel contentPanel = new JBPanel<>(new BorderLayout());

            // 按调用方分组聚合调用关系
            Map<String, List<CallRelation>> relationsByCaller = new LinkedHashMap<>();
            for (CallRelation relation : relations) {
                String callerSignature = relation.getCaller().getSignature();
                relationsByCaller.computeIfAbsent(callerSignature, k -> new ArrayList<>()).add(relation);
            }

            // 创建分组显示的树结构
            DefaultMutableTreeNode root = new DefaultMutableTreeNode("调用关系");

            // 按调用方的行号排序
            List<Map.Entry<String, List<CallRelation>>> sortedEntries = relationsByCaller.entrySet().stream()
                    .sorted(Comparator.comparing(entry -> entry.getValue().get(0).getCaller().getLineNumber()))
                    .toList();

            for (Map.Entry<String, List<CallRelation>> entry : sortedEntries) {
                String callerSignature = entry.getKey();
                List<CallRelation> callerRelations = entry.getValue();

                // 创建调用方节点
                String callerNodeText = String.format("📞 %s (%d个调用)",
                        getSimpleMethodName(callerSignature), callerRelations.size());
                DefaultMutableTreeNode callerNode = new DefaultMutableTreeNode(callerNodeText);

                // 为调用方节点设置完整信息（用于tooltip）
                CallerNodeInfo callerInfo = new CallerNodeInfo(
                        callerRelations.get(0).getCaller(),
                        callerRelations.size()
                );
                callerNode.setUserObject(callerInfo);

                // 按首次调用行号排序被调用方法
                List<CallRelation> sortedCallerRelations = callerRelations.stream()
                        .sorted(Comparator.comparing(relation -> relation.getAllCallInstances().get(0).getLineNumber()))
                        .toList();

                // 添加被调用方法节点
                for (CallRelation relation : sortedCallerRelations) {
                    String calleeText = String.format("→ %s%s",
                            getSimpleMethodName(relation.getCallee().getSignature()),
                            relation.isExternal() ? " [外部]" : "");

                    DefaultMutableTreeNode calleeNode = new DefaultMutableTreeNode(calleeText);

                    // 为被调用方节点设置详细信息
                    CalleeNodeInfo calleeInfo = new CalleeNodeInfo(relation);
                    calleeNode.setUserObject(calleeInfo);

                    callerNode.add(calleeNode);
                }

                root.add(callerNode);
            }

            // 创建自定义的树组件
            Tree callRelationTree = new Tree(new DefaultTreeModel(root));
            callRelationTree.setRootVisible(false);
            callRelationTree.setShowsRootHandles(true);

            // 设置自定义的单元格渲染器
            callRelationTree.setCellRenderer(new CallRelationTreeCellRenderer());

            // 添加双击跳转功能
            setupCallRelationTreeClickHandler(callRelationTree);

            // 添加鼠标监听器来显示tooltip
            callRelationTree.addMouseMotionListener(new java.awt.event.MouseMotionAdapter() {
                @Override
                public void mouseMoved(java.awt.event.MouseEvent e) {
                    int row = callRelationTree.getRowForLocation(e.getX(), e.getY());
                    if (row >= 0) {
                        javax.swing.tree.TreePath path = callRelationTree.getPathForRow(row);
                        if (path != null) {
                            DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();
                            Object userObject = node.getUserObject();

                            String tooltip = null;
                            if (userObject instanceof CallerNodeInfo info) {
                                tooltip = String.format(
                                        "<html><b>调用方:</b> %s<br/>" +
                                                "<b>ID:</b> %s<br/>" +
                                                "<b>定义行号:</b> %d<br/>" +
                                                "<b>调用数量:</b> %d个不同方法<br/>" +
                                                "<b>提示:</b> 单击跳转到定义</html>",
                                        info.caller.getSignature(),
                                        info.caller.getId(),
                                        info.caller.getLineNumber(),
                                        info.callCount
                                );
                            } else if (userObject instanceof CalleeNodeInfo info) {
                                StringBuilder callInstances = new StringBuilder();
                                for (CallRelation.CallInstance instance : info.relation.getAllCallInstances()) {
                                    if (!callInstances.isEmpty()) {
                                        callInstances.append("<br/>");
                                    }
                                    callInstances.append("第").append(instance.getLineNumber())
                                            .append("行: ").append(instance.getExpression());
                                }

                                String jumpHint = info.relation.getAllCallInstances().size() == 1 ?
                                        "单击直接跳转" : "单击选择跳转位置";

                                AnalysisNode callee = info.relation.getCallee();

                                // 构建详细的被调用方信息
                                StringBuilder tooltipBuilder = new StringBuilder();
                                tooltipBuilder.append("<html><b>被调用方:</b> ").append(callee.getSignature()).append("<br/>");
                                tooltipBuilder.append("<b>ID:</b> ").append(callee.getId()).append("<br/>");
                                tooltipBuilder.append("<b>类型:</b> ").append(info.relation.isExternal() ? "外部调用" : "内部调用").append("<br/>");
                                tooltipBuilder.append("<b>所属类:</b> ").append(callee.getClassName()).append("<br/>");
                                tooltipBuilder.append("<b>包名:</b> ").append(callee.getPackageName()).append("<br/>");

                                // 如果是外部调用，显示 jar 包和文件路径信息
                                if (info.relation.isExternal()) {
                                    String moduleName = callee.getModuleName();
                                    String filePath = callee.getFilePath();

                                    if (moduleName != null && !moduleName.isEmpty() &&
                                        !moduleName.equals("null") && !moduleName.startsWith("UNKNOWN")) {
                                        tooltipBuilder.append("<b>来源:</b> ").append(moduleName).append("<br/>");
                                    }

                                    if (filePath != null && !filePath.isEmpty() &&
                                        !filePath.equals("null") && !filePath.startsWith("UNKNOWN")) {
                                        tooltipBuilder.append("<b>文件路径:</b> ").append(filePath).append("<br/>");
                                    }
                                }

                                tooltipBuilder.append("<b>定义行号:</b> ").append(callee.getLineNumber()).append("<br/>");
                                tooltipBuilder.append("<b>调用次数:</b> ").append(info.relation.getCallCount()).append("次<br/>");
                                tooltipBuilder.append("<b>调用位置:</b><br/>").append(callInstances.toString()).append("<br/>");
                                tooltipBuilder.append("<b>提示:</b> ").append(jumpHint).append("</html>");

                                tooltip = tooltipBuilder.toString();
                            }

                            callRelationTree.setToolTipText(tooltip);
                        }
                    } else {
                        callRelationTree.setToolTipText(null);
                    }
                }
            });

            // 展开所有调用方节点
            for (int i = 0; i < callRelationTree.getRowCount(); i++) {
                callRelationTree.expandRow(i);
            }

            contentPanel.add(new JBScrollPane(callRelationTree), BorderLayout.CENTER);

            // 统计信息
            JPanel statsPanel = new JBPanel<>(new FlowLayout(FlowLayout.LEFT));
            statsPanel.add(new JBLabel("总调用关系: " + relations.size()));
            statsPanel.add(new JBLabel("  调用方数量: " + relationsByCaller.size()));

            long externalCalls = relations.stream().mapToLong(r -> r.isExternal() ? 1 : 0).sum();
            long internalCalls = relations.size() - externalCalls;
            statsPanel.add(new JBLabel("  内部调用: " + internalCalls));
            statsPanel.add(new JBLabel("  外部调用: " + externalCalls));

            contentPanel.add(statsPanel, BorderLayout.SOUTH);
            callRelationsPanel.add(contentPanel, BorderLayout.CENTER);
        }

        callRelationsPanel.revalidate();
        callRelationsPanel.repaint();
    }

    private void updateStatisticsPanel(AnalysisResult result) {
        statisticsPanel.removeAll();

        Map<String, Integer> statistics = result.getStatistics();
        if (statistics.isEmpty()) {
            JBLabel noDataLabel = new JBLabel("暂无统计数据", SwingConstants.CENTER);
            statisticsPanel.add(noDataLabel, BorderLayout.CENTER);
        } else {
            JPanel contentPanel = new JBPanel<>(new GridBagLayout());
            GridBagConstraints gbc = new GridBagConstraints();
            gbc.insets = new Insets(5, 10, 5, 10);
            gbc.anchor = GridBagConstraints.WEST;

            int row = 0;
            for (Map.Entry<String, Integer> entry : statistics.entrySet()) {
                gbc.gridx = 0;
                gbc.gridy = row;
                contentPanel.add(new JBLabel(getStatisticDisplayName(entry.getKey()) + ":"), gbc);

                gbc.gridx = 1;
                JBLabel valueLabel = new JBLabel(entry.getValue().toString());
                valueLabel.setFont(valueLabel.getFont().deriveFont(Font.BOLD));
                contentPanel.add(valueLabel, gbc);

                row++;
            }

            statisticsPanel.add(contentPanel, BorderLayout.NORTH);
        }

        statisticsPanel.revalidate();
        statisticsPanel.repaint();
    }

    private String getTypeDisplayName(AnalysisNode.NodeType type) {
        return switch (type) {
            case CLASS -> "类";
            case METHOD -> "方法";
            case FIELD -> "字段";
            case INTERFACE -> "接口";
            case ENUM -> "枚举";
            default -> type.name();
        };
    }

    private String getStatisticDisplayName(String key) {
        return switch (key) {
            case "file_size" -> "文件大小";
            case "total_elements" -> "总元素数";
            case "class_count" -> "类数量";
            case "method_count" -> "方法数量";
            case "field_count" -> "字段数量";
            case "interface_count" -> "接口数量";
            case "enum_count" -> "枚举数量";
            default -> key;
        };
    }

    private String getImportTypeDisplayName(ImportInfo.ImportType type) {
        return switch (type) {
            case SINGLE -> "单个类导入";
            case WILDCARD -> "通配符导入";
            case STATIC -> "静态导入";
            case STATIC_WILDCARD -> "静态通配符导入";
            default -> type.name();
        };
    }

    /**
     * 从完整的方法签名中提取简单的方法名
     */
    private String getSimpleMethodName(String signature) {
        if (signature == null) {
            return "Unknown";
        }

        // 提取类名.方法名部分
        int lastDotIndex = signature.lastIndexOf('.');
        if (lastDotIndex > 0) {
            String classAndMethod = signature.substring(0, lastDotIndex);
            int secondLastDotIndex = classAndMethod.lastIndexOf('.');
            if (secondLastDotIndex > 0) {
                return classAndMethod.substring(secondLastDotIndex + 1);
            }
        }

        return signature;
    }

    /**
     * 调用方节点信息
     */
    private record CallerNodeInfo(AnalysisNode caller, int callCount) {
        @Override
        public String toString() {
            return String.format("📞 %s (%d个调用)",
                    caller.getName(), callCount);
        }
    }

    /**
     * 被调用方节点信息
     */
    private record CalleeNodeInfo(CallRelation relation) {

        @Override
        public String toString() {
            return String.format("→ %s%s",
                    relation.getCallee().getName(),
                    relation.isExternal() ? " [外部]" : "");
        }
    }

    /**
     * 结构节点信息
     */
    private record StructureNodeInfo(int lineNumber, String text) {

        @Override
        public String toString() {
            return text;
        }
    }

    /**
     * 导入节点信息
     */
    private record ImportNodeInfo(int lineNumber, String text, ImportInfo importInfo) {
        @Override
        public String toString() {
            return text;
        }
    }

    /**
     * 自定义树单元格渲染器
     */
    private static class CallRelationTreeCellRenderer extends javax.swing.tree.DefaultTreeCellRenderer {
        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value,
                                                      boolean sel, boolean expanded, boolean leaf, int row, boolean hasFocus) {

            super.getTreeCellRendererComponent(tree, value, sel, expanded, leaf, row, hasFocus);

            if (value instanceof DefaultMutableTreeNode) {
                DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
                Object userObject = node.getUserObject();

                if (userObject instanceof CallerNodeInfo) {
                    // 调用方节点 - 使用蓝色
                    setForeground(sel ? JBColor.WHITE : new Color(0, 100, 200));
                    setIcon(null);
                } else if (userObject instanceof CalleeNodeInfo info) {
                    // 被调用方节点 - 外部调用用红色，内部调用用绿色
                    if (info.relation.isExternal()) {
                        setForeground(sel ? JBColor.WHITE : new Color(200, 50, 50));
                    } else {
                        setForeground(sel ? JBColor.WHITE : new Color(50, 150, 50));
                    }
                    setIcon(null);
                }
            }

            return this;
        }
    }
}