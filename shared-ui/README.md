# Shared UI 模块

Shared UI 是 DeepCode AST 分析器插件的共享用户界面模块，提供了所有语言插件通用的UI组件和用户交互界面。

## 🎯 模块作用

这个模块为插件系统提供：
- 🖥️ **统一的用户界面**：提供一致的用户体验
- 🔧 **工具窗口**：AST分析结果显示和交互
- 📊 **数据可视化**：将分析结果以直观的方式展示
- ⚙️ **操作面板**：提供分析、导出等功能操作
- 🎨 **主题适配**：支持IDE的主题和外观设置

## 📁 目录结构

```
shared-ui/src/main/java/com/sankuai/deepcode/astplugin/
├── ui/                             # 用户界面组件
│   ├── ASTAnalysisPanel.java      # 主分析面板
│   ├── ASTResultPanel.java        # 结果显示面板
│   ├── ASTToolWindow.java         # 工具窗口
│   ├── NodeTreePanel.java         # 节点树状显示
│   ├── CallRelationPanel.java     # 调用关系面板
│   ├── StatisticsPanel.java       # 统计信息面板
│   └── ExportDialog.java          # 导出配置对话框
├── action/                         # 用户操作
│   ├── QuickAnalyzerAction.java   # 快速分析动作
│   ├── ExportReportAction.java    # 导出报告动作
│   ├── ShowAnalysisAction.java    # 显示分析窗口
│   └── RefreshAnalysisAction.java # 刷新分析结果
└── util/                          # UI工具
    ├── UIUtils.java               # UI工具方法
    ├── TreeRenderer.java          # 树状组件渲染
    └── TableRenderer.java         # 表格组件渲染
```

## 🎨 核心UI组件

### ASTAnalysisPanel
主分析面板，整合了所有子组件：

```java
public class ASTAnalysisPanel extends JPanel {
    private NodeTreePanel nodeTreePanel;           // 节点树
    private CallRelationPanel callRelationPanel;   // 调用关系
    private StatisticsPanel statisticsPanel;       // 统计信息
    private JTabbedPane tabbedPane;                // 标签页容器
    
    public void updateResults(AnalysisResult result) {
        // 更新所有子面板的显示内容
    }
}
```

### NodeTreePanel
以树状结构显示代码节点：

- 按类型分组显示（类、方法、字段等）
- 支持搜索和过滤功能
- 双击节点跳转到对应源码位置
- 右键菜单提供复制、导出等操作

### CallRelationPanel  
显示方法/函数之间的调用关系：

- 以表格形式展示调用关系
- 区分内部调用和外部调用
- 支持按调用方/被调用方排序
- 提供调用链追踪功能

### StatisticsPanel
显示分析结果的统计信息：

- 节点数量统计（按类型）
- 包/模块分布统计
- 调用关系统计
- 代码复杂度指标

## ⚙️ 用户操作

### QuickAnalyzerAction
快速分析当前文件的用户操作：

```java
public class QuickAnalyzerAction extends AnAction {
    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        PsiFile file = getCurrentFile(e);
        if (file != null) {
            AnalysisResult result = analyzeFile(file);
            showResults(result);
        }
    }
}
```

**触发方式**：
- 右键菜单：`Quick AST Analyzer`
- 快捷键：`Ctrl+Alt+Shift+T`
- 工具菜单：`Tools` → `AST Analysis` → `Quick Analysis`

### ExportReportAction
导出分析报告的用户操作：

支持的导出格式：
- **JSON格式**：完整的结构化数据
- **CSV格式**：适合电子表格处理
- **HTML报告**：可视化分析报告

### ShowAnalysisAction
显示/隐藏AST分析工具窗口：

- 快捷键：`Ctrl+Alt+T`
- 工具菜单：`Tools` → `AST Analysis` → `Show Analysis Window`

## 🖼️ 界面布局

### 工具窗口布局

```
┌─────────────────────────────────────────────────────────┐
│ AST Analysis                                    [×] [↓] │
├─────────────────────────────────────────────────────────┤
│ [Analyze] [Export] [Refresh]                   [Filter] │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─ Nodes ────┐ ┌─ Call Relations ────┐ ┌─ Statistics ─┐ │
│  │ + Classes  │ │ Caller │ Callee    │ │ Classes: 15   │ │
│  │   - Service│ │ main() │ service() │ │ Methods: 42   │ │
│  │   - Util   │ │        │           │ │ Fields:  8    │ │
│  │ + Methods  │ │        │           │ │ Calls:   28   │ │
│  │   - init() │ │        │           │ │               │ │
│  └────────────┘ └───────────────────┘ └─────────────────┘ │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ Details: [显示选中节点的详细信息]                          │ │
└─────────────────────────────────────────────────────────┘
```

### 弹窗显示

对于快速分析，会显示简洁的弹窗：

```
┌──────── Analysis Results ────────┐
│                                  │
│ File: UserService.java           │
│ ✓ 3 classes analyzed             │
│ ✓ 12 methods found               │
│ ✓ 8 call relations detected     │
│                                  │
│ [View Details] [Export] [Close]  │
└──────────────────────────────────┘
```

## 🎯 交互功能

### 搜索和过滤

```java
// 节点搜索
public void filterNodes(String searchText) {
    nodeTreePanel.setFilter(node -> 
        node.getName().contains(searchText) ||
        node.getPackageName().contains(searchText)
    );
}

// 类型过滤
public void filterByNodeType(Set<NodeType> allowedTypes) {
    nodeTreePanel.setFilter(node -> 
        allowedTypes.contains(node.getType())
    );
}
```

### 源码导航

双击节点或调用关系时，自动跳转到对应的源码位置：

```java
private void navigateToSource(AnalysisNode node) {
    VirtualFile file = findVirtualFile(node.getFilePath());
    if (file != null) {
        Editor editor = FileEditorManager.getInstance(project)
            .openTextEditor(new OpenFileDescriptor(project, file, getLine(node)), true);
        if (editor != null) {
            editor.getCaretModel().moveToLogicalPosition(
                new LogicalPosition(node.getLineNumber() - 1, 0)
            );
        }
    }
}
```

### 上下文菜单

右键点击节点或调用关系时显示上下文菜单：

- **复制信息**：复制节点ID、签名等信息
- **跳转到源码**：导航到对应源码位置  
- **查看调用者**：显示调用该方法的所有位置
- **查看被调用**：显示该方法调用的所有方法
- **导出选中项**：导出选中的节点或关系

## 🎨 主题和样式

### 支持IDE主题

UI组件自动适配IDE的当前主题：

```java
public class ThemedComponent extends JComponent {
    @Override
    protected void paintComponent(Graphics g) {
        // 使用IDE主题色彩
        g.setColor(UIManager.getColor("Panel.background"));
        // ...
    }
}
```

### 自定义图标

为不同节点类型提供图标：

- 🏛️ 类 (CLASS)
- ⚙️ 方法 (METHOD)  
- 📦 字段 (FIELD)
- 🔗 接口 (INTERFACE)
- 📝 枚举 (ENUM)

### 颜色编码

- 🟢 **绿色**：内部调用
- 🟡 **黄色**：外部调用
- 🔴 **红色**：错误或无法解析的调用
- 🔵 **蓝色**：当前选中项

## ⌨️ 键盘快捷键

在工具窗口内支持的快捷键：

- `F5`：刷新分析结果
- `Ctrl+F`：搜索节点
- `Enter`：跳转到选中节点源码
- `Ctrl+C`：复制选中项信息
- `Delete`：清空当前分析结果
- `Ctrl+E`：导出当前结果

## 🔧 自定义配置

### UI设置

通过IDE设置界面配置UI行为：

```
Settings → Tools → AST Analysis → UI Settings
├── Show line numbers in tree
├── Auto-expand tree nodes  
├── Double-click behavior
├── Default export format
└── Remember window layout
```

### 显示选项

用户可以控制显示内容：

- 显示/隐藏特定节点类型
- 显示/隐藏外部调用
- 显示/隐藏包名
- 显示/隐藏行号

## 🚀 性能优化

### 延迟加载

对于大型分析结果，使用延迟加载：

```java
public class LazyTreeModel extends DefaultTreeModel {
    @Override
    public Object getChild(Object parent, int index) {
        // 延迟加载子节点
        if (!isLoaded(parent)) {
            loadChildren(parent);
        }
        return super.getChild(parent, index);
    }
}
```

### 虚拟化显示

对于大量数据，使用虚拟化表格和树：

- 只渲染可见区域的组件
- 支持平滑滚动
- 内存占用恒定

## 🔗 依赖关系

- 依赖 `shared-core` 模块
- 被 `deepcode-java-plugin` 使用
- 被 `deepcode-python-plugin` 使用
- 依赖 `IntelliJ Platform SDK`

## 📝 开发指南

### 添加新的UI组件

1. 继承合适的基类（JPanel、JComponent等）
2. 实现数据更新接口
3. 处理用户交互事件
4. 添加到主面板布局

### 自定义渲染器

```java
public class CustomNodeRenderer extends DefaultTreeCellRenderer {
    @Override
    public Component getTreeCellRendererComponent(
            JTree tree, Object value, boolean selected,
            boolean expanded, boolean leaf, int row, boolean hasFocus) {
        
        Component component = super.getTreeCellRendererComponent(
            tree, value, selected, expanded, leaf, row, hasFocus);
            
        if (value instanceof AnalysisNode) {
            AnalysisNode node = (AnalysisNode) value;
            setIcon(getIconForNodeType(node.getType()));
            setText(formatNodeDisplay(node));
        }
        
        return component;
    }
}
```

通过 Shared UI 模块，我们提供了统一、直观、高效的用户界面，让用户能够轻松地查看和分析代码结构。