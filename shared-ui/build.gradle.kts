plugins {
    id("java-library")
    id("org.jetbrains.intellij.platform") version "2.6.0"
}

group = "com.sankuai.deepcode"
version = "1.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
    intellijPlatform {
        defaultRepositories()
    }
}

dependencies {
    implementation(project(":shared-core"))
    
    // 最小化IntelliJ平台依赖，仅用于编译UI组件需要的基础类
    intellijPlatform {
        intellijIdeaCommunity("2024.1.4")
        // 不添加额外的插件依赖，保持最小化
    }

    // 基础注解
    compileOnly("org.jetbrains:annotations:24.0.1")
}

intellijPlatform {
    // 完全禁用插件功能，仅作为库使用
    buildSearchableOptions = false
    instrumentCode = false
}

tasks {
    // 禁用所有插件相关任务，因为这只是一个共享UI库
    withType<Task> {
        val taskName = name
        if (taskName.contains("Plugin") || 
            taskName.contains("Sandbox") || 
            taskName.contains("Ide") ||
            taskName.contains("Verify") ||
            taskName.contains("Sign") ||
            taskName.contains("Publish")) {
            enabled = false
        }
    }

    // 构建纯Java库JAR
    jar {
        archiveBaseName.set("shared-ui")
        manifest {
            attributes(
                "Implementation-Title" to "DeepCode AST Plugin Shared UI",
                "Implementation-Version" to project.version,
                "Implementation-Vendor" to "Shanghai Sankuai Technology Co., Ltd."
            )
        }
    }

    // 确保编译设置正确
    compileJava {
        options.compilerArgs.addAll(listOf("-Xlint:deprecation", "-Xlint:unchecked"))
        options.release.set(17)
    }
}