package com.sankuai.deepcode.astplugin.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 调用关系，表示一个方法调用另一个方法的关系
 */
public class CallRelation {
    // 调用者
    private final AnalysisNode caller;
    // 被调用者
    private final AnalysisNode callee;
    // 主要调用发生的行号（第一次调用）
    private final int callLineNumber;
    // 主要调用表达式（第一次调用）
    private final String callExpression;
    // 是否为外部调用
    private final boolean isExternal;
    // 所有调用实例
    private final List<CallInstance> allCallInstances;
    // 调用次数
    private final int callCount;

    public CallRelation(AnalysisNode caller, AnalysisNode callee, int callLineNumber,
                        String callExpression, boolean isExternal) {
        this(caller, callee, callLineNumber, callExpression, isExternal, new ArrayList<>());
    }

    public CallRelation(AnalysisNode caller, AnalysisNode callee, int callLineNumber,
                        String callExpression, boolean isExternal, List<CallInstance> allCallInstances) {
        this.caller = caller;
        this.callee = callee;
        this.callLineNumber = callLineNumber;
        this.callExpression = callExpression;
        this.isExternal = isExternal;
        this.allCallInstances = new ArrayList<>(allCallInstances);
        this.callCount = Math.max(1, allCallInstances.size());
    }

    // Getters
    public AnalysisNode getCaller() {
        return caller;
    }

    public AnalysisNode getCallee() {
        return callee;
    }

    public int getCallLineNumber() {
        return callLineNumber;
    }

    public String getCallExpression() {
        return callExpression;
    }

    public boolean isExternal() {
        return isExternal;
    }

    public List<CallInstance> getAllCallInstances() {
        return new ArrayList<>(allCallInstances);
    }

    public int getCallCount() {
        return callCount;
    }

    @Override
    public String toString() {
        String countInfo = callCount > 1 ? String.format(" (%d calls)", callCount) : "";
        return String.format("%s -> %s (line %d)%s%s",
                caller.getSignature(), callee.getSignature(), callLineNumber,
                isExternal ? " [EXTERNAL]" : "", countInfo);
    }

    /**
     * 调用实例，表示一次具体的方法调用
     */
    public static class CallInstance {
        private final int lineNumber;
        private final int columnNumber;
        private final String expression;

        public CallInstance(int lineNumber, String expression) {
            this(lineNumber, 0, expression);
        }

        public CallInstance(int lineNumber, int columnNumber, String expression) {
            this.lineNumber = lineNumber;
            this.columnNumber = columnNumber;
            this.expression = expression;
        }

        public int getLineNumber() {
            return lineNumber;
        }

        public int getColumnNumber() {
            return columnNumber;
        }

        public String getExpression() {
            return expression;
        }

        @Override
        public String toString() {
            if (columnNumber > 0) {
                return String.format("line %d, col %d: %s", lineNumber, columnNumber, expression);
            } else {
                return String.format("line %d: %s", lineNumber, expression);
            }
        }
    }
}