package com.sankuai.deepcode.astplugin.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 导入信息，表示一个import语句的详细信息
 */
public class ImportInfo {
    // 导入语句文本
    private final String statement;
    // 行号
    private final int lineNumber;
    // 导入类型
    private final ImportType type;
    // 是否为外部导入
    private final boolean isExternal;
    // 解析出的具体类（用于通配符导入）
    private final List<String> resolvedClasses;
    // 当前文件路径（相对于工程目录）
    private final String filePath;
    // 被导入的文件路径（相对于工程目录）
    private final String targetFilePath;

    /**
     * 导入类型枚举
     */
    public enum ImportType {
        // 单个类导入：import java.util.List; 或 import os
        SINGLE,
        // 通配符导入：import java.util.*; 或 from os import *
        WILDCARD,
        // 静态导入：import static java.lang.Math.PI;
        STATIC,
        // 静态通配符：import static java.lang.Math.*;
        STATIC_WILDCARD,
        // FROM导入（Python特有）：from os import path
        FROM_IMPORT
    }

    public ImportInfo(String statement, int lineNumber, ImportType type) {
        this(statement, lineNumber, type, false, new ArrayList<>(), "unknown", null);
    }

    public ImportInfo(String statement, int lineNumber, ImportType type, boolean isExternal) {
        this(statement, lineNumber, type, isExternal, new ArrayList<>(), "unknown", null);
    }

    public ImportInfo(String statement, int lineNumber, ImportType type, List<String> resolvedClasses) {
        this(statement, lineNumber, type, false, resolvedClasses, "unknown", null);
    }

    public ImportInfo(String statement, int lineNumber, ImportType type, boolean isExternal, List<String> resolvedClasses) {
        this(statement, lineNumber, type, isExternal, resolvedClasses, "unknown", null);
    }

    public ImportInfo(String statement, int lineNumber, ImportType type, boolean isExternal, List<String> resolvedClasses, String filePath, String targetFilePath) {
        this.statement = statement;
        this.lineNumber = lineNumber;
        this.type = type;
        this.isExternal = isExternal;
        this.resolvedClasses = new ArrayList<>(resolvedClasses);
        this.filePath = filePath != null ? filePath : "unknown";
        this.targetFilePath = targetFilePath;
    }

    // Getters
    public String getStatement() {
        return statement;
    }

    public int getLineNumber() {
        return lineNumber;
    }

    public ImportType getType() {
        return type;
    }

    public boolean isExternal() {
        return isExternal;
    }

    public List<String> getResolvedClasses() {
        return new ArrayList<>(resolvedClasses);
    }

    public String getFilePath() {
        return filePath;
    }

    public String getTargetFilePath() {
        return targetFilePath;
    }

    @Override
    public String toString() {
        String externalFlag = isExternal ? " [External]" : " [Internal]";
        String filePathInfo = " [" + filePath + "]";
        String targetFileInfo = targetFilePath != null ? " -> [" + targetFilePath + "]" : "";
        if (type == ImportType.WILDCARD || type == ImportType.STATIC_WILDCARD) {
            return String.format("%s (line %d, type: %s, resolved: %d classes)%s%s%s",
                    statement, lineNumber, type, resolvedClasses.size(), externalFlag, filePathInfo, targetFileInfo);
        } else {
             return String.format("%s (line %d, type: %s)%s%s%s", statement, lineNumber, type, externalFlag, filePathInfo, targetFileInfo);
        }
    }
}