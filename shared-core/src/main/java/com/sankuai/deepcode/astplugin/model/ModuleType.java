package com.sankuai.deepcode.astplugin.model;

/**
 * 模块类型枚举
 * 表示代码模块的来源类型
 * <AUTHOR>
 */
public enum ModuleType {
    /**
     * 标准库模块（如Java的JDK，Python的标准库）
     */
    STANDARD_LIBRARY("STDLIB", true),
    
    /**
     * 第三方库模块（如Maven依赖，Python的pip包）
     */
    THIRD_PARTY("THIRD_PARTY", true),
    
    /**
     * 项目内部模块
     */
    PROJECT("PROJECT", false),
    
    /**
     * 未知类型模块
     */
    UNKNOWN("UNKNOWN", true);
    
    private final String code;
    private final boolean isExternal;
    
    ModuleType(String code, boolean isExternal) {
        this.code = code;
        this.isExternal = isExternal;
    }
    
    /**
     * 获取模块类型代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 是否为外部模块
     */
    public boolean isExternal() {
        return isExternal;
    }
}
