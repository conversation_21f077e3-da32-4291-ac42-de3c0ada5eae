package com.sankuai.deepcode.astplugin.model;

import java.util.Objects;

/**
 * 分析节点，表示代码中的一个可分析单元（类、方法等）
 * <AUTHOR>
 */
public class AnalysisNode {
    // 唯一标识
    private final String id;
    // 节点类型
    private final NodeType type;
    // 节点名称
    private final String name;
    // 所属类名
    private final String className;
    // 所属包名
    private final String packageName;
    // 行号
    private final int lineNumber;
    // 完整签名
    private final String signature;
    // 编程语言
    private final String language;

    // 可变字段，用于外部方法信息
    // 模块名（Java的Maven模块，Python的模块等）
    private String moduleName;
    // 文件路径
    private String filePath;
    // 模块类型
    private ModuleType moduleType;

    public AnalysisNode(String id, NodeType type, String name, String className,
                        String packageName, int lineNumber, String signature) {
        this(id, type, name, className, packageName, lineNumber, signature, null, null, "Java");
    }

    public AnalysisNode(String id, NodeType type, String name, String className,
                        String packageName, int lineNumber, String signature,
                        String moduleName, String filePath, String language) {
        this(id, type, name, className, packageName, lineNumber, signature, 
             moduleName, filePath, language, ModuleType.UNKNOWN);
    }

    public AnalysisNode(String id, NodeType type, String name, String className,
                        String packageName, int lineNumber, String signature,
                        String moduleName, String filePath, String language, ModuleType moduleType) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.className = className;
        this.packageName = packageName;
        this.lineNumber = lineNumber;
        this.signature = signature;
        this.moduleName = moduleName;
        this.filePath = filePath;
        this.language = language;
        this.moduleType = moduleType;
    }

    // Getters
    public String getId() {
        return id;
    }

    public NodeType getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getClassName() {
        return className;
    }

    public String getPackageName() {
        return packageName;
    }

    public int getLineNumber() {
        return lineNumber;
    }

    public String getSignature() {
        return signature;
    }

    public String getModuleName() {
        return moduleName;
    }

    public String getFilePath() {
        return filePath;
    }

    public String getLanguage() {
        return language;
    }

    public ModuleType getModuleType() {
        return moduleType;
    }

    // Setters for mutable fields (用于外部方法信息设置)
    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public void setModuleType(ModuleType moduleType) {
        this.moduleType = moduleType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AnalysisNode that = (AnalysisNode) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return String.format("%s[%s] at line %d", signature, type, lineNumber);
    }

    public enum NodeType {
        CLASS, METHOD, FIELD, INTERFACE, ENUM,
        // Python特有的节点类型
        FUNCTION, MODULE, VARIABLE,
        // 嵌套函数类型
        INNER_FUNCTION
    }
}