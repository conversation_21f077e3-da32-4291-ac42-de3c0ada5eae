package com.sankuai.deepcode.astplugin.model;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 分析结果，包含所有分析得到的结构化数据
 * <AUTHOR>
 */
public class AnalysisResult {
    private final String fileName;
    private final Language language;
    private final LocalDateTime timestamp;

    private final Map<String, AnalysisNode> nodes = new HashMap<>();
    private final List<CallRelation> callRelations = new ArrayList<>();
    private final Map<String, Integer> statistics = new HashMap<>();
    private final List<String> errors = new ArrayList<>();
    private final List<ImportInfo> imports = new ArrayList<>();

    public AnalysisResult(String fileName, Language language) {
        this.fileName = fileName;
        this.language = language;
        this.timestamp = LocalDateTime.now();
    }

    // 便捷构造函数，支持字符串参数
    public AnalysisResult(String fileName, String languageCode) {
        this.fileName = fileName;
        this.language = Language.fromCode(languageCode);
        this.timestamp = LocalDateTime.now();
    }

    // 便捷构造函数，根据文件名自动推断语言
    public AnalysisResult(String fileName) {
        this.fileName = fileName;
        this.language = Language.fromFileName(fileName);
        this.timestamp = LocalDateTime.now();
    }

    // 添加节点
    public void addNode(AnalysisNode node) {
        nodes.put(node.getId(), node);
    }

    // 添加调用关系
    public void addCallRelation(CallRelation relation) {
        callRelations.add(relation);
    }

    // 更新统计信息
    public void updateStatistics(String key, int value) {
        statistics.put(key, value);
    }

    public void incrementStatistics(String key) {
        statistics.put(key, statistics.getOrDefault(key, 0) + 1);
    }

    // 添加错误信息
    public void addError(String error) {
        errors.add(error);
    }

    // 添加导入信息
    public void addImport(ImportInfo importInfo) {
        imports.add(importInfo);
    }

    // 批量添加导入信息
    public void addImports(List<ImportInfo> importInfos) {
        imports.addAll(importInfos);
    }

    // 兼容方法：添加字符串格式的导入信息（已废弃）
    @Deprecated
    public void addImport(String importStatement) {
        // 为了向后兼容，创建一个简单的ImportInfo对象
        imports.add(new ImportInfo(importStatement, 0, ImportInfo.ImportType.SINGLE, true, new ArrayList<>(), "unknown", null));
    }



    // Getters
    public String getFileName() { return fileName; }
    public Language getLanguage() { return language; }
    public String getLanguageCode() { return language.getCode(); }
    public String getLanguageDisplayName() { return language.getDisplayName(); }

    public LocalDateTime getTimestamp() { return timestamp; }
    public Map<String, AnalysisNode> getNodes() { return Collections.unmodifiableMap(nodes); }
    public List<CallRelation> getCallRelations() { return Collections.unmodifiableList(callRelations); }
    public Map<String, Integer> getStatistics() { return Collections.unmodifiableMap(statistics); }
    public List<String> getErrors() { return Collections.unmodifiableList(errors); }
    public List<ImportInfo> getImports() { return Collections.unmodifiableList(imports); }

    // 便捷方法
    public Collection<AnalysisNode> getNodesByType(AnalysisNode.NodeType type) {
        return nodes.values().stream()
            .filter(node -> node.getType() == type)
            .toList();
    }

    public List<CallRelation> getCallRelationsForCaller(String callerId) {
        return callRelations.stream()
            .filter(relation -> relation.getCaller().getId().equals(callerId))
            .toList();
    }

    public List<CallRelation> getCallRelationsForCallee(String calleeId) {
        return callRelations.stream()
            .filter(relation -> relation.getCallee().getId().equals(calleeId))
            .toList();
    }
}