// 配置为纯 Java 库，不应用IntelliJ插件
plugins {
    id("java-library")
}

group = "com.sankuai.deepcode"
version = "1.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

dependencies {
    // 基础注解
    compileOnly("org.jetbrains:annotations:24.0.1")

    // 注意：shared-core 是纯数据模型和接口库，不依赖任何IntelliJ Platform
    // 只包含基础的 Java 类和接口定义
}

tasks {
    jar {
        archiveBaseName.set("shared-core")
        manifest {
            attributes(
                "Implementation-Title" to "DeepCode AST Plugin Shared Core",
                "Implementation-Version" to project.version
            )
        }
    }

    test {
        useJUnitPlatform()
    }
}

// 确保编译设置
tasks.withType<JavaCompile> {
    options.release.set(17)
}