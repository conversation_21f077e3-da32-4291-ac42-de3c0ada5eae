# Shared Core 模块

Shared Core 是 DeepCode AST 分析器插件的核心共享模块，包含了所有语言分析器共同使用的数据模型、接口定义和核心逻辑。

## 🎯 模块作用

这个模块为整个插件系统提供：
- 🏗️ **统一的数据模型**：定义了分析结果的标准格式
- 🔌 **分析器接口**：为不同语言的分析器提供统一的接口规范
- 🛠️ **核心服务**：提供分析器管理和结果处理服务
- 📊 **统计工具**：提供分析结果的统计和汇总功能

## 📁 目录结构

```
shared-core/src/main/java/com/sankuai/deepcode/astplugin/
├── model/                          # 数据模型
│   ├── AnalysisNode.java          # 代码节点表示
│   ├── AnalysisResult.java        # 分析结果容器
│   ├── CallRelation.java          # 调用关系模型
│   ├── ImportInfo.java            # 导入信息模型
│   ├── Language.java              # 编程语言枚举
│   └── NodeType.java              # 节点类型枚举
├── analyzer/                       # 分析器接口
│   ├── ASTAnalyzer.java           # 分析器接口定义
│   ├── AnalyzerService.java       # 分析器服务接口
│   └── DefaultAnalyzerService.java # 默认服务实现
└── util/                          # 工具类
    ├── AnalysisUtils.java         # 分析工具方法
    └── StatisticsCalculator.java  # 统计计算
```

## 🔧 核心组件

### 数据模型 (model/)

#### AnalysisNode
代表代码中的一个可分析单元（类、方法、字段、函数、变量等）：

```java
public class AnalysisNode {
    private String id;                  // 唯一标识符
    private NodeType type;              // 节点类型
    private String name;                // 名称
    private String packageName;         // 包名/模块名
    private String filePath;            // 文件路径
    private int lineNumber;             // 行号
    private String signature;           // 方法签名（如果适用）
    private Language language;          // 编程语言
    
    // ... 其他属性和方法
}
```

支持的节点类型：
- `CLASS` - 类
- `INTERFACE` - 接口  
- `METHOD` - 方法/函数
- `FIELD` - 字段/属性
- `VARIABLE` - 变量
- `ENUM` - 枚举
- `ANNOTATION` - 注解

#### CallRelation
表示方法/函数之间的调用关系：

```java
public class CallRelation {
    private String callerId;            // 调用方ID
    private String calleeId;            // 被调用方ID
    private boolean isInternal;         // 是否为内部调用
    private List<CallInstance> instances; // 调用实例列表
    
    public static class CallInstance {
        private int lineNumber;         // 调用位置行号
        private String context;         // 调用上下文
    }
}
```

#### AnalysisResult
分析结果的容器，包含所有分析信息：

```java
public class AnalysisResult {
    private String filePath;                            // 分析的文件路径
    private Language language;                          // 文件语言
    private Map<String, AnalysisNode> nodes;           // 所有节点
    private List<CallRelation> callRelations;          // 调用关系
    private List<ImportInfo> imports;                  // 导入信息
    private Map<String, Object> metadata;             // 元数据
    private List<String> errors;                       // 错误信息
    private List<String> warnings;                     // 警告信息
}
```

### 分析器接口 (analyzer/)

#### ASTAnalyzer
所有语言分析器必须实现的核心接口：

```java
public interface ASTAnalyzer {
    /**
     * 检查是否支持指定文件类型
     */
    boolean supports(PsiFile psiFile);
    
    /**
     * 分析指定文件
     */
    AnalysisResult analyze(PsiFile psiFile);
    
    /**
     * 获取支持的语言
     */
    String getSupportedLanguage();
}
```

#### AnalyzerService
分析器管理服务，负责根据文件类型选择合适的分析器：

```java
public interface AnalyzerService {
    /**
     * 分析文件，自动选择合适的分析器
     */
    AnalysisResult analyze(PsiFile file);
    
    /**
     * 注册分析器
     */
    void registerAnalyzer(ASTAnalyzer analyzer);
    
    /**
     * 获取支持的语言列表
     */
    Set<String> getSupportedLanguages();
}
```

## 🔌 扩展机制

### 添加新的节点类型

1. 在 `NodeType` 枚举中添加新类型：
```java
public enum NodeType {
    // 现有类型...
    PROPERTY,      // 属性
    DECORATOR,     // 装饰器
    MODULE         // 模块
}
```

2. 在分析器中处理新类型：
```java
public class CustomAnalyzer implements ASTAnalyzer {
    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        // 处理新的节点类型
        AnalysisNode propertyNode = new AnalysisNode();
        propertyNode.setType(NodeType.PROPERTY);
        // ...
    }
}
```

### 注册自定义分析器

通过 IntelliJ 的扩展点机制注册：

```xml
<extensions defaultExtensionNs="com.sankuai.deepcode.astplugin">
    <analyzer implementation="com.example.CustomASTAnalyzer"/>
</extensions>
```

或者编程方式注册：
```java
AnalyzerService service = AnalyzerService.getInstance();
service.registerAnalyzer(new CustomASTAnalyzer());
```

## 📊 使用示例

### 基本分析

```java
// 获取分析器服务
AnalyzerService analyzerService = AnalyzerService.getInstance();

// 分析文件
PsiFile psiFile = // ... 获取PSI文件
AnalysisResult result = analyzerService.analyze(psiFile);

// 获取分析结果
Collection<AnalysisNode> nodes = result.getNodes().values();
List<CallRelation> relations = result.getCallRelations();

// 统计信息
int classCount = result.getNodeCountByType(NodeType.CLASS);
int methodCount = result.getNodeCountByType(NodeType.METHOD);
```

### 过滤和查询

```java
// 查找特定类型的节点
List<AnalysisNode> methods = result.getNodes().values().stream()
    .filter(node -> node.getType() == NodeType.METHOD)
    .collect(Collectors.toList());

// 查找特定包的节点
List<AnalysisNode> serviceNodes = result.getNodes().values().stream()
    .filter(node -> node.getPackageName().contains("service"))
    .collect(Collectors.toList());

// 查找调用关系
List<CallRelation> internalCalls = result.getCallRelations().stream()
    .filter(CallRelation::isInternal)
    .collect(Collectors.toList());
```

### 统计分析

```java
// 使用统计计算器
StatisticsCalculator calculator = new StatisticsCalculator();
Map<NodeType, Integer> nodeStats = calculator.calculateNodeStatistics(result);
Map<String, Integer> packageStats = calculator.calculatePackageStatistics(result);
```

## 🎯 设计原则

1. **模块化**：各组件职责清晰，便于扩展和维护
2. **语言无关**：数据模型支持多种编程语言的特性
3. **线程安全**：所有核心组件都是线程安全的
4. **扩展性**：通过接口和扩展点支持新功能
5. **一致性**：所有分析器产生统一格式的结果

## 🔗 依赖关系

- 被 `deepcode-java-plugin` 依赖
- 被 `deepcode-python-plugin` 依赖  
- 被 `shared-ui` 依赖
- 依赖 `IntelliJ Platform SDK`

## 📝 开发注意事项

1. **线程安全**：所有公共API都必须是线程安全的
2. **PSI访问**：分析器实现中的PSI访问必须在ReadAction中进行
3. **异常处理**：提供优雅的错误处理和恢复机制
4. **向后兼容**：接口变更时需要考虑向后兼容性
5. **文档完整**：所有公共接口都必须有完整的JavaDoc文档

通过 Shared Core 模块，我们确保了整个插件系统的一致性和可扩展性，为不同语言的分析器提供了坚实的基础。