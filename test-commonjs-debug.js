// 测试 CommonJS require 的详细调试日志
// 这个文件专门用于触发和观察 CommonJS require 的解析过程

// 1. Node.js 内置模块 - 应该触发详细的解析日志
const crypto = require('crypto');

// 2. 其他 Node.js 内置模块
const fs = require('fs');
const path = require('path');

// 3. 外部三方库
const React = require('react');
const lodash = require('lodash');

// 4. 相对路径导入
const utils = require('./utils');

// 5. 别名路径导入
const pageUtils = require('@page/utils');

console.log('CommonJS debug test file loaded');

// 使用这些导入来确保它们被分析
console.log('crypto:', typeof crypto);
console.log('fs:', typeof fs);
console.log('React:', typeof React);
console.log('lodash:', typeof lodash);
console.log('utils:', typeof utils);
console.log('pageUtils:', typeof pageUtils);

module.exports = {
    crypto,
    fs,
    React,
    lodash,
    utils,
    pageUtils
};
