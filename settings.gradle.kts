rootProject.name = "deepcode-ast-plugin"

// 引入子项目
include("shared-core")
include("shared-ui")
include("deepcode-java-plugin")
include("deepcode-python-plugin")
include("deepcode-typescript-plugin")

// 配置项目目录结构
project(":shared-core").projectDir = file("shared-core")
project(":shared-ui").projectDir = file("shared-ui")
project(":deepcode-java-plugin").projectDir = file("deepcode-java-plugin")
project(":deepcode-python-plugin").projectDir = file("deepcode-python-plugin")
project(":deepcode-typescript-plugin").projectDir = file("deepcode-typescript-plugin")
