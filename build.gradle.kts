plugins {
    id("org.jetbrains.intellij.platform") version "2.6.0" apply false
}

group = "com.sankuai.deepcode"
version = "1.0-SNAPSHOT"

allprojects {
    group = "com.sankuai.deepcode"
    version = "1.0-SNAPSHOT"

    repositories {
        mavenCentral()
        maven("https://cache-redirector.jetbrains.com/intellij-dependencies")
    }
}

// 配置子项目 - 对UI和插件项目应用IntelliJ配置
subprojects {
    apply(plugin = "java")

    configure<JavaPluginExtension> {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    // 新版intellij-platform-gradle-plugin的配置在各自模块中进行
    // 只保留基本的Java配置

    tasks.withType<JavaCompile> {
        options.release.set(17)
    }
}
