#!/bin/bash

# DeepCode AST Plugin - IDE 调试启动脚本
# 用法：
#   ./dev-ide.sh        # 启动 IntelliJ IDEA Community (Java插件)
#   ./dev-ide.sh python # 启动 PyCharm Community (Python插件)

set -e

# 显示使用说明
show_usage() {
    echo "🚀 DeepCode AST Plugin - IDE 调试启动"
    echo "========================================="
    echo ""
    echo "用法："
    echo "  ./dev-ide.sh        # 启动 IntelliJ IDEA Community (Java插件)"
    echo "  ./dev-ide.sh python # 启动 PyCharm Community (Python插件)"
    echo ""
}

# 检查项目环境
check_environment() {
    if [ ! -f "build.gradle.kts" ]; then
        echo "❌ 错误: 请在项目根目录运行此脚本"
        exit 1
    fi

    echo "📋 检查 Java 版本..."
    java -version
    echo ""
}

# 启动 IntelliJ IDEA Community (Java插件)
start_idea() {
    echo "🔧 启动 IntelliJ IDEA Community 调试模式..."
    echo "插件: DeepCode AST Analyzer for Java"
    echo ""
    
    echo "📦 构建 Java 插件..."
    ./gradlew :deepcode-java-plugin:buildPlugin

    echo ""
    echo "🚀 启动 IntelliJ IDEA Community..."
    echo "💡 提示: IDE启动后，插件会自动加载"
    echo "💡 快捷键: Ctrl+Alt+Shift+T 快速AST分析"
    echo ""
    
    ./gradlew :deepcode-java-plugin:runIde
}

# 启动 PyCharm Community (Python插件)  
start_pycharm() {
    echo "🔧 启动 PyCharm Community 调试模式..."
    echo "插件: DeepCode AST Analyzer for Python"
    echo ""
    
    echo "📦 构建 Python 插件..."
    ./gradlew :deepcode-python-plugin:buildPlugin

    echo ""
    echo "🚀 启动 PyCharm Community..."
    echo "💡 提示: IDE启动后，插件会自动加载"
    echo "💡 快捷键: Ctrl+Alt+Shift+T 快速AST分析"
    echo ""
    
    ./gradlew :deepcode-python-plugin:runIde
}

# 主程序
main() {
    show_usage
    check_environment
    
    # 根据参数决定启动哪个IDE
    if [ "$1" = "python" ]; then
        start_pycharm
    elif [ "$1" = "java" ] || [ -z "$1" ]; then
        start_idea
    else
        echo "❌ 未知参数: $1"
        echo ""
        show_usage
        exit 1
    fi
}

# 执行主程序
main "$@"