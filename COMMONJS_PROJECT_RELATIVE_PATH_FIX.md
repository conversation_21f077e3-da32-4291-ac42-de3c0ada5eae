# CommonJS require() 项目相对路径转换修复

## 问题描述

虽然 CommonJS require 的 PSI 解析已经成功，但返回的是绝对路径而不是项目相对路径：

**问题现象**：
```json
{
  "statement": "require('crypto')",
  "targetFilePath": "/Users/<USER>/Documents/projects/bm/banma_fe_page/node_modules/@types/node/crypto.d.ts"
}
```

**期望结果**：
```json
{
  "statement": "require('crypto')",
  "targetFilePath": "node_modules/@types/node/crypto.d.ts"
}
```

## 根本原因

在之前的修复中，我们对外部模块和内部模块采用了不同的路径处理策略：

```java
// 问题代码：对外部模块直接返回绝对路径
if (isExternal) {
    LOG.info("外部模块，使用绝对路径: " + absolutePath);
    return absolutePath;  // 直接返回绝对路径，没有转换
} else {
    // 只对内部模块转换为相对路径
    String relativePath = convertToProjectRelativePath(absolutePath, callExpression);
    return relativePath != null ? relativePath : absolutePath;
}
```

**问题**：这种区分处理导致外部模块（如 Node.js 内置模块、node_modules 中的库）返回绝对路径，不符合用户期望。

## 修复方案

### 统一路径处理策略

将所有文件路径都统一转换为项目相对路径（如果可能）：

```java
// 修复后：所有文件路径都尝试转换为项目相对路径
if (absolutePath != null) {
    LOG.info("[CommonJS Debug] ✅ 成功获取文件路径: " + absolutePath);
    
    // 所有文件路径都需要转换为项目相对路径（如果以项目路径开始）
    String relativePath = convertToProjectRelativePath(absolutePath, callExpression);
    if (relativePath != null) {
        LOG.info("[CommonJS Debug] ✅ 转换为项目相对路径: " + relativePath);
        return relativePath;
    } else {
        // 如果无法转换为相对路径，说明文件在项目外部，返回绝对路径
        LOG.info("[CommonJS Debug] ⚠️ 无法转换为相对路径，使用绝对路径: " + absolutePath);
        return absolutePath;
    }
}
```

### 修复逻辑

#### 1. 统一处理策略
- **所有文件**：无论内部还是外部，都尝试转换为项目相对路径
- **转换成功**：返回项目相对路径（如 `node_modules/@types/node/crypto.d.ts`）
- **转换失败**：返回绝对路径（文件在项目外部的情况）

#### 2. 路径转换规则
- **项目内文件**：`/project/src/utils.ts` → `src/utils.ts`
- **node_modules**：`/project/node_modules/react/index.js` → `node_modules/react/index.js`
- **类型定义**：`/project/node_modules/@types/node/crypto.d.ts` → `node_modules/@types/node/crypto.d.ts`
- **项目外文件**：保持绝对路径

#### 3. 增强日志记录
```java
LOG.info("[CommonJS Debug] ✅ 转换为项目相对路径: " + relativePath);
LOG.info("[CommonJS Debug] ⚠️ 无法转换为相对路径，使用绝对路径: " + absolutePath);
```

## 修复效果

### 修复前
```
[CommonJS Debug] ✅ 成功获取文件路径: /Users/<USER>/Documents/projects/bm/banma_fe_page/node_modules/@types/node/crypto.d.ts
[CommonJS Debug] 外部模块，使用绝对路径: /Users/<USER>/Documents/projects/bm/banma_fe_page/node_modules/@types/node/crypto.d.ts

{
  "targetFilePath": "/Users/<USER>/Documents/projects/bm/banma_fe_page/node_modules/@types/node/crypto.d.ts"
}
```

### 修复后（预期）
```
[CommonJS Debug] ✅ 成功获取文件路径: /Users/<USER>/Documents/projects/bm/banma_fe_page/node_modules/@types/node/crypto.d.ts
[CommonJS Debug] ✅ 转换为项目相对路径: node_modules/@types/node/crypto.d.ts

{
  "targetFilePath": "node_modules/@types/node/crypto.d.ts"
}
```

## 技术要点

### 1. 统一处理原则
- 不再区分内部/外部模块的路径处理方式
- 所有路径都尝试转换为项目相对路径
- 提供一致的用户体验

### 2. 路径转换依赖
- 使用现有的 `convertToProjectRelativePath()` 方法
- 该方法会检查文件是否在项目目录下
- 自动处理项目根路径的移除

### 3. 回退机制
- 如果文件确实在项目外部，保留绝对路径
- 确保不会丢失文件位置信息

## 支持的场景

修复后的实现支持：

### Node.js 内置模块
```javascript
const crypto = require('crypto');
// targetFilePath: node_modules/@types/node/crypto.d.ts
```

### 外部三方库
```javascript
const React = require('react');
// targetFilePath: node_modules/react/index.js
```

### Scoped 包
```javascript
const antdIcons = require('@ant-design/icons');
// targetFilePath: node_modules/@ant-design/icons/lib/index.d.ts
```

### 内部项目文件
```javascript
const utils = require('./utils');
// targetFilePath: src/utils.ts
```

### 别名路径
```javascript
const pageUtils = require('@page/utils');
// targetFilePath: src/page/utils.ts
```

## 验证方法

1. **编译验证**：`./gradlew compileJava` - ✅ 成功
2. **功能测试**：运行插件分析包含 `require('crypto')` 的文件
3. **路径检查**：确认 `targetFilePath` 是项目相对路径
4. **日志观察**：查看路径转换过程的详细日志

## 总结

此次修复实现了 CommonJS require 的统一路径处理：

- ✅ **统一策略**：所有文件路径都尝试转换为项目相对路径
- ✅ **用户友好**：返回简洁的相对路径而不是冗长的绝对路径
- ✅ **保持兼容**：项目外文件仍返回绝对路径，不丢失信息
- ✅ **详细日志**：便于验证和问题诊断

现在 `require('crypto')` 应该返回 `node_modules/@types/node/crypto.d.ts` 而不是完整的绝对路径！🎯
