#!/bin/bash

# DeepCode AST Plugin - 一键打包脚本
# 此脚本会构建所有插件并在各自目录生成插件产物

set -e

# 显示使用说明
show_usage() {
    echo "📦 DeepCode AST Plugin - 一键打包"
    echo "=================================="
    echo ""
    echo "用法: $0 [plugin_type]"
    echo ""
    echo "参数:"
    echo "  java    构建 Java 插件 (deepcode-java-plugin)"
    echo "  py      构建 Python 插件 (deepcode-python-plugin)"
    echo "  ts      构建 TypeScript 插件 (deepcode-typescript-plugin)"
    echo "  (空)    构建所有插件 (默认)"
    echo ""
    echo "示例:"
    echo "  $0        # 构建所有插件"
    echo "  $0 java   # 只构建 Java 插件"
    echo "  $0 py     # 只构建 Python 插件"
    echo "  $0 ts     # 只构建 TypeScript 插件"
    echo ""
    echo "构建产物将在各插件目录的 build/distributions/ 下生成 .zip 文件"
    echo ""
}

# 检查环境
check_environment() {
    if [ ! -f "build.gradle.kts" ]; then
        echo "❌ 错误: 请在项目根目录运行此脚本"
        exit 1
    fi

    echo "📋 检查 Java 版本..."
    java -version
    echo ""
}

# 清理构建产物
clean_builds() {
    echo "🧹 清理之前的构建产物..."
    ./gradlew clean
    echo ""
}

# 构建 Java 插件
build_java_plugin() {
    echo "🔨 构建 Java 插件..."
    echo "=============================="
    
    echo "📦 正在构建 DeepCode AST Analyzer for Java..."
    # 确保完整构建 shared-ui 模块（包括 searchableOptions）
    echo "🔧 构建 shared-core 依赖..."
    ./gradlew :shared-core:build

    echo "🔧 构建 shared-ui 依赖..."
    ./gradlew :shared-ui:build

    echo "🔧 构建 Java 插件..."
    ./gradlew :deepcode-java-plugin:buildPlugin

    # 检查构建结果
    if [ -f "deepcode-java-plugin/build/distributions/deepcode-java-plugin-1.0-SNAPSHOT.zip" ]; then
        echo "✅ Java 插件构建成功!"
        echo "📁 插件位置: deepcode-java-plugin/build/distributions/"
        ls -lh "deepcode-java-plugin/build/distributions/"*.zip
        echo ""
    else
        echo "❌ Java 插件构建失败!"
        return 1
    fi
}

# 构建 Python 插件
build_python_plugin() {
    echo "🔨 构建 Python 插件..."
    echo "==============================="
    
    echo "📦 正在构建 DeepCode AST Analyzer for Python..."
    # Gradle 会自动处理依赖，但确保 shared 模块已构建
    ./gradlew :deepcode-python-plugin:buildPlugin

    # 检查构建结果
    if [ -f "deepcode-python-plugin/build/distributions/deepcode-python-plugin-1.0-SNAPSHOT.zip" ]; then
        echo "✅ Python 插件构建成功!"
        echo "📁 插件位置: deepcode-python-plugin/build/distributions/"
        ls -lh "deepcode-python-plugin/build/distributions/"*.zip
        echo ""
    else
        echo "❌ Python 插件构建失败!"
        return 1
    fi
}

# 构建 TypeScript 插件
build_typescript_plugin() {
    echo "🔨 构建 TypeScript 插件..."
    echo "=================================="

    echo "📦 正在构建 DeepCode AST Analyzer for TypeScript..."
    # 确保构建依赖模块
    ./gradlew :deepcode-typescript-plugin:buildPlugin

    # 检查构建结果
    if [ -f "deepcode-typescript-plugin/build/distributions/deepcode-typescript-plugin-1.0-SNAPSHOT.zip" ]; then
        echo "✅ TypeScript 插件构建成功!"
        echo "📁 插件位置: deepcode-typescript-plugin/build/distributions/"
        ls -lh "deepcode-typescript-plugin/build/distributions/"*.zip
        echo ""
    else
        echo "❌ TypeScript 插件构建失败!"
        return 1
    fi
}

# 显示安装说明
show_installation_guide() {
    local plugin_type=$1

    if [ -z "$plugin_type" ]; then
        echo "🎉 所有插件构建完成!"
    else
        echo "🎉 ${plugin_type} 插件构建完成!"
    fi
    echo "==================="
    echo ""
    echo "📁 插件产物位置:"

    # 根据构建的插件类型显示对应位置
    case "$plugin_type" in
        "java")
            echo "  • Java 插件: deepcode-java-plugin/build/distributions/"
            ;;
        "py")
            echo "  • Python 插件: deepcode-python-plugin/build/distributions/"
            ;;
        "ts")
            echo "  • TypeScript 插件: deepcode-typescript-plugin/build/distributions/"
            ;;
        *)
            echo "  • Java 插件:       deepcode-java-plugin/build/distributions/"
            echo "  • Python 插件:     deepcode-python-plugin/build/distributions/"
            echo "  • TypeScript 插件: deepcode-typescript-plugin/build/distributions/"
            ;;
    esac

    echo ""
    echo "💾 安装方法:"
    echo "1. 打开 IntelliJ IDEA/PyCharm"
    echo "2. 进入 File → Settings → Plugins"
    echo "3. 点击齿轮图标 → Install Plugin from Disk..."
    echo "4. 选择对应的 .zip 文件:"
    echo "   - IntelliJ IDEA: 选择 Java 或 TypeScript 插件的 .zip"
    echo "   - PyCharm: 选择 Python 或 TypeScript 插件的 .zip"
    echo "   - WebStorm: 选择 TypeScript 插件的 .zip"
    echo "5. 重启 IDE"
    echo ""
    echo "🔧 使用方法:"
    echo "• 快捷键: Ctrl+Alt+Shift+T 进行 AST 分析"
    echo "• 菜单: Tools → DeepCode AST Analyzer"
    echo ""
    echo "📧 如遇问题，请查看各插件目录下的文档:"
    echo "• deepcode-java-plugin/USAGE.md"
    echo "• deepcode-python-plugin/USAGE.md"
    echo "• deepcode-typescript-plugin/README.md"
}

# 主程序
main() {
    local plugin_type=$1

    show_usage
    check_environment
    clean_builds

    # 根据参数决定构建哪些插件
    case "$plugin_type" in
        "java")
            echo "🚀 开始构建 Java 插件..."
            echo ""
            if build_java_plugin; then
                echo ""
                show_installation_guide "java"
            else
                echo "❌ Java 插件构建失败"
                exit 1
            fi
            ;;
        "py")
            echo "🚀 开始构建 Python 插件..."
            echo ""
            if build_python_plugin; then
                echo ""
                show_installation_guide "py"
            else
                echo "❌ Python 插件构建失败"
                exit 1
            fi
            ;;
        "ts")
            echo "🚀 开始构建 TypeScript 插件..."
            echo ""
            if build_typescript_plugin; then
                echo ""
                show_installation_guide "ts"
            else
                echo "❌ TypeScript 插件构建失败"
                exit 1
            fi
            ;;
        *)
            # 默认构建所有插件
            echo "🚀 开始构建所有插件..."
            echo ""

            # 构建Java插件
            if build_java_plugin; then
                echo ""
            else
                echo "❌ Java 插件构建失败，停止执行"
                exit 1
            fi

            # 构建Python插件
            if build_python_plugin; then
                echo ""
            else
                echo "❌ Python 插件构建失败，停止执行"
                exit 1
            fi

            # 构建TypeScript插件
            if build_typescript_plugin; then
                echo ""
            else
                echo "❌ TypeScript 插件构建失败，停止执行"
                exit 1
            fi

            show_installation_guide
            ;;
    esac
}

# 执行主程序
main "$@"
