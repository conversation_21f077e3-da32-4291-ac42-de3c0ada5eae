// 源文件：static/page/org/pages/StationMark/components/index.tsx
// 从这个文件导入 '../../../Stations/index.hooks'

import React from 'react';
import { ChangeEvent, FC, useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import Selector from '@roo/roo/Selector';
import DataPicker from '@roo/roo/DataPicker';
import { Col, Row } from '@roo/roo/Grid';
import Panel from '@roo/roo/Panel';
import Toast from '@roo/roo/Toast';
import Box from '@roo/roo/Box';
import Loading from '@roo/roo/Loading';
import CheckBox from '@roo/roo/CheckBox';

// 这是问题所在的导入 - 多级相对路径
import { 
    StaffLabelStationBatchMark, 
    StaffLabelStationBatchMarkProps 
} from '../../../Stations/index.hooks';

import MultiMarkVerifyMsg from '../MultiMarkVerifyMsg';

const StationMarkComponent: FC = () => {
    const [loading, setLoading] = useState(false);
    
    return (
        <div>
            <Panel>
                <StaffLabelStationBatchMark />
                <MultiMarkVerifyMsg />
            </Panel>
        </div>
    );
};

export default StationMarkComponent;
